
/****** Object:  StoredProcedure [telesale].[SearchCallResults]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[SearchCallResults]
    @ResultCodeSuiteId UNIQUEIDENTIFIER,
    @Code NVARCHAR(50),
    @StartRow INT,
    @EndRow INT
AS
BEGIN
    DECLARE @SelectString NVARCHAR(MAX)
        = N'
	SELECT	cs.*, rcs.Name ResultCodeSuiteName,
			ROW_NUMBER() OVER (ORDER BY cs.Code) RowNumber
	FROM	dbo.CallResult cs
			LEFT JOIN dbo.ResultCodeSuite rcs ON rcs.Id = cs.ResultCodeSuiteId
	';
	DECLARE @WhereString NVARCHAR(MAX) = N'
	WHERE	cs.IsDeleted=0 
	'
	IF @Code IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + N' AND cs.Code = @Code '
	END
	IF @ResultCodeSuiteId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + N' AND cs.ResultCodeSuiteId = @ResultCodeSuiteId '
	END
	DECLARE @ExecuteString NVARCHAR(MAX) = N'
	WITH mainCte AS
	(
		' + @SelectString + '
		' + @WhereString + '
	)
	SELECT	*,
			(SELECT COUNT(*) FROM mainCte) TotalCount
	FROM	mainCte
	WHERE	mainCte.RowNumber BETWEEN @StartRow AND @EndRow
	'

	DECLARE @ParamDefs NVARCHAR(MAX) = N'
	@ResultCodeSuiteId  UNIQUEIDENTIFIER,
	@Code NVARCHAR(50),
	@StartRow			INT,
	@EndRow				INT
	'

	EXECUTE sp_executesql @ExecuteString, @ParamDefs,
										  @ResultCodeSuiteId = @ResultCodeSuiteId,
										  @Code = @Code,
										  @StartRow = @StartRow,
										  @EndRow = @EndRow

END;
GO