
/****** Object:  StoredProcedure [telesale].[lead_GetAssignedAppointmentInfos]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


--ALTER PROCEDURE [telesale].[lead_GetAssignedAppointmentInfos]
CREATE PROCEDURE [telesale].[lead_GetAssignedAppointmentInfos]

	@IdList			dbo.IdList READONLY,
	@MeetDate		DATETIME,
	@SlotFromTime	DATETIME,
	@SlotToTime		DATETIME

AS
BEGIN
	
	SELECT	org.*,
			ISNULL(fieldSaleCount.FieldSaleCount,0) FieldSaleCount,
			ISNULL(dayAppointmentCount.AppointmentCount,0) DayAppointmentCount,
			ISNULL(appointmentCount.AppointmentCount,0) SlotTimeAppointmentCount
	FROM	dbo.Organization org
			JOIN @IdList temp ON temp.Id = org.Id
			LEFT JOIN
            (
				SELECT	org.Id OrganizationId, COUNT(DISTINCT up.Id) FieldSaleCount
				FROM	dbo.Organization org
						JOIN @IdList ON [@IdList].Id = org.Id
						JOIN dbo.UserProfiles up ON up.OrganizationId = org.Id
						JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
				WHERE	uir.RoleId = '********-C225-4644-B39A-F5152F4A525C'
				GROUP BY org.Id
			) fieldSaleCount ON fieldSaleCount.OrganizationId = org.Id
			LEFT JOIN
            (
				SELECT	org.Id OrganizationId, COUNT(DISTINCT ap.Id) AppointmentCount
				FROM	dbo.Organization org
						JOIN @IdList ON [@IdList].Id = org.Id
						JOIN dbo.UserProfiles up ON up.OrganizationId = org.Id
						JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
						LEFT JOIN dbo.LeadAssignment la ON la.AssignedFieldSaleTeamId = org.Id
						LEFT JOIN dbo.Appointment ap ON ap.LeadAssignmentId = la.Id AND CAST(ap.MeetDate AS DATE) = CAST(@MeetDate AS DATE)
				WHERE	uir.RoleId = '********-C225-4644-B39A-F5152F4A525C'
				GROUP BY org.Id
			) dayAppointmentCount ON dayAppointmentCount.OrganizationId = org.Id
			LEFT JOIN
            (
				SELECT	org.Id OrganizationId, COUNT(DISTINCT ap.Id) AppointmentCount
				FROM	dbo.Organization org
						JOIN @IdList ON [@IdList].Id = org.Id
						JOIN dbo.UserProfiles up ON up.OrganizationId = org.Id
						JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
						LEFT JOIN dbo.LeadAssignment la ON la.AssignedFieldSaleTeamId = org.Id
						LEFT JOIN dbo.Appointment ap ON ap.LeadAssignmentId = la.Id AND ap.MeetDate >= @SlotFromTime AND ap.MeetDate < @SlotToTime
				WHERE	uir.RoleId = '********-C225-4644-B39A-F5152F4A525C'
				GROUP BY org.Id
			) appointmentCount ON appointmentCount.OrganizationId = org.Id

END
GO