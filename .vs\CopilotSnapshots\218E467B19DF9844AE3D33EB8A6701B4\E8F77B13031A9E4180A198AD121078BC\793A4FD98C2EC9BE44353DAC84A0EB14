﻿using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Microsoft.EntityFrameworkCore; // Thêm using cho ToListAsync

namespace Webaby.Core.Access.Queries
{
    public class GetApiAccessQuery : QueryBase<AccessData>
    {
        public string UrlPart { get; set; }
        public HttpMethods Method { get; set; }
    }

    internal sealed class GetApiAccessQueryHandler : QueryHandlerBase<GetApiAccessQuery, AccessData>
    {
        public GetApiAccessQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<AccessData>> ExecuteAsync(GetApiAccessQuery query)
        {
            // Lấy danh sách các AccessEntity phù hợp với UrlPart và Method
            var accessEntities = await EntitySet.Get<AccessEntity>()
                .Where(access => access.ActionName == query.UrlPart && access.Method == query.Method)
                .ToListAsync();

            // Map sang AccessData
            var accessDataList = accessEntities.Select(x => Mapper.Map<AccessData>(x));

            // Tạo QueryResult
            var result = QueryResult.Create(accessDataList);

            return result;
        }
    }
}

