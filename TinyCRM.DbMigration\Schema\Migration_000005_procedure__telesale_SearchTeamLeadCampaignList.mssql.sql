
/****** Object:  StoredProcedure [telesale].[SearchTeamLeadCampaignList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[SearchTeamLeadCampaignList]

	@OrganizationId		UNIQUEIDENTIFIER,
	@CampaignName		NVARCHAR(500),
	@SearchDate			DATETIME,
	@Status				INT,
	@StartRow			INT,
	@EndRow				INT

AS
BEGIN

	DECLARE @WhereString NVARCHAR(MAX) = N''
	SET @WhereString = @WhereString + IIF(ISNULL(@CampaignName,'')='', '', N' AND c.CampaignName LIKE N''%'' + @CampaignName + ''%'' ')
	SET @WhereString = @WhereString + IIF(@Status IS NULL, '', N' AND c.Status = @Status ')
	SET @WhereString = @WhereString + IIF(@SearchDate IS NULL, '', N' AND c.StartDate <= @SearchDate AND (c.EndDate IS NULL OR c.EndDate >= @SearchDate) ')

	DECLARE @ExecuteString NVARCHAR(MAX) = N'
	WITH cte AS
	(
		SELECT	c.*, ROW_NUMBER() OVER (ORDER BY c.StartDate DESC) RowNumber
		FROM	dbo.Campaign c
				JOIN
				(
					SELECT	DISTINCT c.Id
					FROM	dbo.Campaign c
							JOIN dbo.ProspectAssignment pa ON pa.CampaignId = c.Id
					WHERE	c.Deleted= 0 AND pa.IsNotCurrent = 0
							AND pa.AssignedTeamId = @OrganizationId ' + @WhereString + '
				) temp ON temp.Id = c.Id
	)
	SELECT	*, (SELECT COUNT(*) FROM cte) TotalCount
	FROM	cte
	WHERE	cte.RowNumber BETWEEN @StartRow AND @EndRow'

	DECLARE @ParamDefinations NVARCHAR(MAX) = N'
	@OrganizationId		UNIQUEIDENTIFIER,
	@CampaignName		NVARCHAR(500),
	@SearchDate			DATETIME,
	@Status				INT,
	@StartRow			INT,
	@EndRow				INT'

	EXEC sp_executesql @ExecuteString, @ParamDefinations,
										@OrganizationId		= @OrganizationId,
										@CampaignName		= @CampaignName,
										@SearchDate			= @SearchDate,
										@Status				= @Status,
										@StartRow			= @StartRow,
										@EndRow				= @EndRow

END
GO