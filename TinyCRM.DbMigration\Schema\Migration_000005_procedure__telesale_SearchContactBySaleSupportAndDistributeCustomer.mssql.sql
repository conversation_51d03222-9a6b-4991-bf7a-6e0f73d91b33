
/****** Object:  StoredProcedure [telesale].[SearchContactBySaleSupportAndDistributeCustomer]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[SearchContactBySaleSupportAndDistributeCustomer]
	
	----- Search Model
	@CampaignId UNIQUEIDENTIFIER,
	@RequestTicketId UNIQUEIDENTIFIER,
	@Status INT,
	@IsExistCallStatus INT = 0,
	@IncomeFrom INT=0,
	@IncomeTo INT=0,

	@PreviousAssignedTeamId UNIQUEIDENTIFIER =null,
	@PreviousAssignedAgentId UNIQUEIDENTIFIER =null,
	@PreviousPaCallResultId UNIQUEIDENTIFIER =null,
	@ImportSessionId UNIQUEIDENTIFIER,

	@LeadStatus INT = 0,

	@DataSource NVARCHAR(200) =null,
	@PhoneNumber VARCHAR(100)  = null,
	@EnterCampaignBasketDate DATETIME = NULL,
	@EnterCampaignBasketDateTo DATETIME = NULL,
	@HasEmail BIT,

	@ProvinceList IdList READONLY,
	
	-- Fixed dataset by individual selection
	@ContactSelectedList IdList READONLY,

	----- Distribute Limit count
	@LimitCount INT
AS
BEGIN

    SET NOCOUNT ON;

    DECLARE @ContactList TABLE
    (
		rn INT,
		CustomerId UNIQUEIDENTIFIER,
		ProspectAssignmentId UNIQUEIDENTIFIER,
		SurveyFeedbackId UNIQUEIDENTIFIER,
		SurveyFeedbackCodeBinary VARBINARY(MAX),
		SurveyFeedbackCode VARCHAR(50)
    )

	-- Get list of records to be distribute into @ContactList
    IF ( SELECT COUNT(*) FROM   @ContactSelectedList) > 0
	BEGIN
		IF (@RequestTicketId IS NULL)
		BEGIN
			INSERT  INTO @ContactList (rn, CustomerId, ProspectAssignmentId)
			SELECT  ROW_NUMBER() OVER ( ORDER BY (SELECT 1) ) rn ,
					c.Id CustomerId,
					pa.Id ProspectAssignmentId
			FROM    @ContactSelectedList s
					JOIN dbo.ProspectAssignment pa ON pa.Id=s.Id
					JOIN dbo.Prospect p ON pa.ProspectId=p.Id
					JOIN dbo.Customer c ON p.CustomerId=c.Id
			DELETE @ContactList WHERE rn > @LimitCount
			SELECT * FROM @ContactList
		END	
		ELSE 
			DECLARE @SurveyFeedbackId UNIQUEIDENTIFIER
			SELECT @SurveyFeedbackId = ReferenceResultId FROM dbo.ProspectAssignment WHERE CampaignId = @CampaignId AND ReferenceObjectId = @RequestTicketId
			IF @SurveyFeedbackId IS NULL 
			BEGIN
				INSERT  INTO @ContactList ( CustomerId, ProspectAssignmentId)
				SELECT pa.CustomerId,pa.Id ProspectAssignmentId FROM dbo.ProspectAssignment pa
				LEFT JOIN dbo.SurveyFeedback sf ON pa.ReferenceResultId = sf.Id 
				WHERE pa.CampaignId = @CampaignId AND pa.ReferenceObjectId = @RequestTicketId
			END	
			SELECT * FROM @ContactList
			--IF (select COUNT(*) from @ContactList c JOIN dbo.ProspectAssignment pa ON pa.Id = c.ProspectAssignmentId WHERE pa.CampaignId = @CampaignId AND pa.ReferenceObjectId = @RequestTicketId) > 1
			--BEGIN
			--	SELECT * FROM @ContactList
			--	DELETE @ContactList
			--	SELECT * FROM @ContactList
			--END
	END
    ELSE
	BEGIN
        INSERT  INTO @ContactList (rn, CustomerId, ProspectAssignmentId)
		EXEC dbo.SearchToDistributeBySaleSupport
				@CampaignId = @CampaignId, -- uniqueidentifier
				@Status = @Status, -- int
				@IsExistCallStatus = @IsExistCallStatus, -- int
				@IncomeFrom = @IncomeFrom, -- int
				@IncomeTo = @IncomeTo, -- int
				@PreviousAssignedTeamId = @PreviousAssignedTeamId, -- uniqueidentifier
				@PreviousAssignedAgentId = @PreviousAssignedAgentId, -- uniqueidentifier
				@PreviousPaCallResultId = @PreviousPaCallResultId, -- uniqueidentifier
				@ImportSessionId = @ImportSessionId, -- uniqueidentifier
				@DataSource = @DataSource, -- nvarchar(200)
				@PhoneNumber = @PhoneNumber, -- varchar(100)
				@EnterCampaignBasketDate = @EnterCampaignBasketDate, -- datetime
				@EnterCampaignBasketDateTo = @EnterCampaignBasketDateTo, -- datetime
				@HasEmail = @HasEmail,
				@ProvinceList = @ProvinceList, -- IdList
				@PageIndex = 0, -- int
				@PageSize = @LimitCount, -- int
				@ReturnBriefResultToDistribute = 1, -- bit
				@ExportExcel = 0
	END
	---- Generate SurveyFeedbackId & Code
	UPDATE	c
	SET		SurveyFeedbackId = NEWID()
	FROM	@ContactList c

	UPDATE	c
	SET		SurveyFeedbackCodeBinary = CAST(c.SurveyFeedbackId AS VARBINARY(MAX))
	FROM	@ContactList c

	UPDATE	c
	SET		SurveyFeedbackCode = CAST(N'' AS XML).value('xs:base64Binary(xs:hexBinary(sql:column("SurveyFeedbackCodeBinary")))', 'nvarchar(25)')
	FROM	@ContactList c

	UPDATE	c
	SET		SurveyFeedbackCode = REPLACE(REPLACE(SurveyFeedbackCode, '=', ''), '+', '')
	FROM	@ContactList c
	BEGIN TRANSACTION
	BEGIN TRY

		---- Distribute here
		UPDATE  pa
		SET     pa.AssignedTeamId = pa.CustomerId,
				pa.AssignedTeamDate = GETDATE(),
				pa.AssignedAgentId = pa.CustomerId,
				pa.AssignedAgentDate = GETDATE(),
				pa.Status = 1,
				pa.CreatedReason = 1,
				pa.Ignore15DaysRule = 0 ,

				pa.OwnerId = pa.CustomerId,
				pa.OwnerType = 2, -- Customer

				pa.ReferenceResultId = c.SurveyFeedbackId,
				pa.ReferenceResultType = 'SurveyFeedback'

		FROM    @ContactList c
				JOIN dbo.ProspectAssignment pa ON pa.Id = c.ProspectAssignmentId
		
		UPDATE  p
		SET     p.Status = 2,
				p.BackToCommonBasketDate = NULL,
				p.ReprospectDate = NULL,

				p.ReferenceResultId = tmp.SurveyFeedbackId,
				p.ReferenceResultType = 'SurveyFeedback'

		FROM    dbo.ProspectAssignment pa
				JOIN @ContactList tmp ON tmp.ProspectAssignmentId = pa.Id
				JOIN dbo.Prospect p ON p.Id = pa.ProspectId

		---- Create SurveyFeedback
		DECLARE @SurveyId UNIQUEIDENTIFIER
		SELECT	@SurveyId = sc.SurveyId
		FROM	dbo.Campaign c
				JOIN dbo.SurveyCampaign sc ON sc.CampaignId = c.Id
		WHERE	c.Id = @CampaignId

		INSERT INTO dbo.SurveyFeedback (Id, SurveyId, RelatedObjectId, SurveyeeId, SurveyeeOrganizationId, CreatedDate, Code)
		SELECT	c.SurveyFeedbackId, @SurveyId, p.ReferenceObjectId, NULL, NULL, GETDATE(), c.SurveyFeedbackCode
		FROM	@ContactList c
				JOIN dbo.ProspectAssignment pa WITH(NOLOCK) ON pa.Id = c.ProspectAssignmentId
				JOIN dbo.Prospect p WITH(NOLOCK) ON p.Id = pa.ProspectId

		SELECT	ProspectAssignmentId
		FROM	@ContactList

		COMMIT TRANSACTION

	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		THROW;
	END CATCH

	SELECT COUNT(*) TotalAssigned FROM @ContactList

END
GO