
/****** Object:  StoredProcedure [telesale].[DataCallResultFunnelSumary]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[DataCallResultFunnelSumary]

	@CampaignId			UNIQUEIDENTIFIER,
	@OrganizationId		UNIQUEIDENTIFIER,
	@AssignedAgentId	UNIQUEIDENTIFIER

AS
BEGIN

	WITH cte(Id) AS
	(
		SELECT	Id
		FROM	dbo.Organization org
		WHERE	org.Id = @OrganizationId
		UNION ALL
		SELECT	org.Id
		FROM	dbo.Organization org
				JOIN cte temp ON temp.Id = org.ParentId
	)
	SELECT	cte.Id
	INTO	#TempOrgs
	FROM	cte

	DECLARE @SelectString NVARCHAR(MAX) = N'
	SELECT	org.Name OrganizationName, up.FullName TelesaleName,
			SUM(IIF(cr.IsConnected=1,1,0)) TotalConnected,
			SUM(IIF(cr.IsContacted=1,1,0)) TotalContacted,
			SUM(IIF(cr.IsConsulted=1,1,0)) TotalConsulted,
			SUM(IIF(cr.IsInterested=1,1,0)) TotalInterested,
			SUM(IIF(cr.IsEligible=1,1,0)) TotalEligible,
			SUM(IIF(cr.IsAppointmentMade=1,1,0)) TotalAppointmentMade,
			SUM(IIF(cr.IsWin=1,1,0)) TotalWin,
			COUNT(*) TotalData
	'

	DECLARE @FromString NVARCHAR(MAX) = N'
	FROM	dbo.CallResult cr
			JOIN dbo.ProspectAssignment pa ON pa.CallResultId = cr.Id
			LEFT JOIN dbo.Organization org ON org.Id = pa.AssignedTeamId
			LEFT JOIN dbo.UserProfiles up ON up.Id = pa.AssignedAgentId
	'
	IF @OrganizationId IS NOT NULL
	BEGIN
		SET @FromString = @FromString + N'
			JOIN #TempOrgs tempOrg ON tempOrg.Id = org.Id
		'
	END

	DECLARE @WhereString NVARCHAR(MAX) = N'WHERE	pa.IsNotCurrent = 0 '
	IF @CampaignId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' pa.CampaignId = @CampaignId '
	END
	IF @AssignedAgentId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' pa.AssignedAgentId = @AssignedAgentId '
	END

	DECLARE @GroupBy NVARCHAR(MAX) = N'
	GROUP BY org.Name, up.FullName
	'

	DECLARE @ParamDefs NVARCHAR(MAX) = N'
		@CampaignId			UNIQUEIDENTIFIER,
		@OrganizationId		UNIQUEIDENTIFIER,
		@AssignedAgentId	UNIQUEIDENTIFIER
	'

	DECLARE @FullExecString NVARCHAR(MAX) = @SelectString + @FromString + @WhereString + @GroupBy

	EXECUTE sp_executesql @FullExecString, @ParamDefs,
											@CampaignId = @CampaignId,
											@OrganizationId = @OrganizationId,
											@AssignedAgentId = @AssignedAgentId
			

END
GO