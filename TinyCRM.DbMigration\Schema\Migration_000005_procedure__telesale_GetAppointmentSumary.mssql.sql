
/****** Object:  StoredProcedure [telesale].[GetAppointmentSumary]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



--dmo login: userId, team ko dùng
--dmo sup login: teamId -> filter userId
--dmo mng: filter teamId
CREATE PROCEDURE [telesale].[GetAppointmentSumary]
	@TeamId UNIQUEIDENTIFIER,
	@UserId UNIQUEIDENTIFIER,
	@Role INT, --1 - DMO, 2- SupDMO, 3- DMO Manager
	@MeetDateFrom DATETIME,
	@MeetDateTo DATETIME
AS
BEGIN
	SET NOCOUNT ON;
	
	DECLARE @Rank1 FLOAT = 0;
	DECLARE @Rank2 FLOAT = 0;
	DECLARE @Rank3 FLOAT = 0;
	DECLARE @Rank1User NVARCHAR(MAX) = NULL;
	DECLARE @Rank2User NVARCHAR(MAX) = NULL;
	DECLARE @Rank3User NVARCHAR(MAX) = NULL;
	DECLARE @UserRank INT = 0;
	DECLARE @RankOverValue FLOAT = 0;
	DECLARE @RankUnderValue FLOAT = 0;
	DECLARE @IsTopOne BIT = 0;

	DECLARE @TeamFilter IdList;
	IF @Role=3
		IF @TeamId IS NULL
			INSERT INTO @TeamFilter SELECT o.Id FROM dbo.Organization o
			JOIN dbo.UserProfiles up ON up.OrganizationId=o.ParentId
			WHERE up.Id=@UserId
		ELSE
			INSERT INTO @TeamFilter SELECT @TeamId
	ELSE IF @Role=2
		INSERT INTO @TeamFilter SELECT @TeamId
	ELSE IF @Role=1
		INSERT INTO @TeamFilter SELECT OrganizationId FROM dbo.UserProfiles WHERE id=@UserId
	ELSE
		RAISERROR (15600,-1,-1, N'Lỗi'); 

	DECLARE @DMO_COUNT INT = (
		SELECT COUNT(*) FROM dbo.UserProfiles up
		JOIN dbo.UserInRoles uir ON uir.UserId=up.Id
		JOIN dbo.Roles r ON uir.RoleId=r.Id
		WHERE up.OrganizationId IN (SELECT Id FROM @TeamFilter)
		AND r.Name='Field Sale');
	WITH cte AS
	(
		SELECT up.FullName, up.AgentCode CodeDMO, la.AssignedFieldSaleId, SUM(s.APE) APE, ROW_NUMBER() OVER (ORDER BY SUM(s.APE) DESC) APERank
		FROM dbo.Lead l
		JOIN dbo.LeadAssignment la ON l.CurrentLeadAssignmentId=la.Id
		JOIN dbo.Appointment a ON a.Id=la.WaitingAppointmentId
		JOIN dbo.UserProfiles up ON up.Id=la.AssignedFieldSaleId
		LEFT JOIN dbo.Sale s ON s.LeadAssignmentId=la.Id
		WHERE (@Role=1 OR la.AssignedFieldSaleTeamId IN (SELECT Id FROM @TeamFilter) OR up.OrganizationId IN (SELECT Id FROM @TeamFilter))
		AND (@MeetDateFrom IS NULL OR DATEDIFF(DAY, a.MeetDate, @MeetDateFrom)<=0)
		AND (@MeetDateTo IS NULL OR DATEDIFF(DAY, a.MeetDate, @MeetDateTo)>=0)
		AND (up.Id IS NULL OR up.OrganizationId IN (SELECT Id FROM @TeamFilter))
		GROUP BY la.AssignedFieldSaleId, up.AgentCode, up.FullName
	)
	SELECT * INTO #ranktable FROM cte;
	SET @Rank1 = (SELECT APE FROM #ranktable WHERE APERank=1);
	SET @Rank2 = (SELECT APE FROM #ranktable WHERE APERank=2);
	SET @Rank3 = (SELECT APE FROM #ranktable WHERE APERank=3);
	SET @Rank1User = (SELECT ISNULL(CodeDMO, 'N/A')+N' - '+ISNULL(FullName, 'No Name') FROM #ranktable WHERE APERank=1);
	SET @Rank2User = (SELECT ISNULL(CodeDMO, 'N/A')+N' - '+ISNULL(FullName, 'No Name') FROM #ranktable WHERE APERank=2);
	SET @Rank3User = (SELECT ISNULL(CodeDMO, 'N/A')+N' - '+ISNULL(FullName, 'No Name') FROM #ranktable WHERE APERank=3);
	SET @UserRank = (SELECT APERank FROM #ranktable WHERE AssignedFieldSaleId=@UserId);
	SET @RankUnderValue = (SELECT APE FROM #ranktable WHERE APERank=@UserRank+1);
	SET @RankOverValue = (SELECT APE FROM #ranktable WHERE APERank=@UserRank-1);
	SET @IsTopOne = IIF(@UserRank=1, 1, 0);
	SELECT
	SUM(IIF((@Role<>1 OR la.AssignedFieldSaleId=@UserId),1,0)) Total,
	SUM(IIF((@Role<>1 OR la.AssignedFieldSaleId=@UserId) AND a.Status=3 AND l.Status<>4,1,0)) Meet,
	SUM(IIF((@Role<>1 OR la.AssignedFieldSaleId=@UserId) AND a.Status=2,1,0)) NotMeet,
	SUM(IIF((@Role<>1 OR la.AssignedFieldSaleId=@UserId) AND a.AppointmentResultCodeId='B8834940-2110-4A32-94A2-01B0AB5C9BFE',1,0)) Win, --l.status=4
	SUM(IIF((@Role<>1 OR la.AssignedFieldSaleId=@UserId) AND a.Status=1,1,0)) Pending,
	SUM(IIF((@Role<>1 OR la.AssignedFieldSaleId=@UserId) AND a.Status<4 AND DATEDIFF(DAY, a.MeetDate, GETDATE())=0,1,0)) ToDay,
	SUM(IIF((@Role<>1 OR la.AssignedFieldSaleId=@UserId) AND a.Status=1 AND DATEDIFF(MINUTE, a.MeetDate, GETDATE())<0 AND DATEDIFF(MINUTE, a.MeetDate, GETDATE())>-60, 1, 0)) Rush,
	SUM(IIF((@Role<>1 OR la.AssignedFieldSaleId=@UserId) AND (l.Status=1 OR l.Status=2 OR l.Status=3) AND DATEDIFF(DAY, a.MeetDate, GETDATE())>15, 1, 0)) Warn15Day,
	FORMAT(SUM(s.APE), N'C', N'vi-vn') TotalAPEGroup,
	FORMAT(SUM(s.IP), N'C', N'vi-vn') TotalIPGroup,
	IIF(@DMO_COUNT>0, FORMAT(SUM(s.APE)/@DMO_COUNT, N'C', N'vi-vn'), 'N/A') AVGAPEGroup,
	FORMAT(SUM(IIF(la.AssignedFieldSaleId=@UserId, s.APE, 0)), N'C', N'vi-vn') APE,
	@UserRank APERank,
	IIF(@RankUnderValue IS NOT NULL, FORMAT(@RankUnderValue, N'C', N'vi-vn'), FORMAT(0, N'C', N'vi-vn')) RankUnderValue,
	IIF(@RankOverValue IS NOT NULL, FORMAT(@RankOverValue, N'C', N'vi-vn'), IIF(@IsTopOne=0, FORMAT(0, N'C', N'vi-vn'), 'N/A')) RankOverValue,
	FORMAT(@Rank1, N'C', N'vi-vn') Rank1,
	FORMAT(@Rank2, N'C', N'vi-vn') Rank2,
	FORMAT(@Rank3, N'C', N'vi-vn') Rank3,
	@Rank1User Rank1User,
	@Rank2User Rank2User,
	@Rank3User Rank3User
	FROM dbo.Lead l
	JOIN dbo.LeadAssignment la ON l.CurrentLeadAssignmentId=la.Id
	JOIN dbo.Appointment a ON a.Id=la.WaitingAppointmentId
	LEFT JOIN (
		SELECT * FROM (SELECT ROW_NUMBER() OVER (PARTITION BY s.LeadAssignmentId ORDER BY s.ProspectAssignmentId) rn, * FROM dbo.Sale s) s2 WHERE s2.rn=1
	) s ON s.LeadAssignmentId=la.Id
	LEFT JOIN dbo.UserProfiles up ON up.Id=la.AssignedFieldSaleId
	WHERE (@Role=1 OR la.AssignedFieldSaleTeamId IN (SELECT Id FROM @TeamFilter) OR up.OrganizationId IN (SELECT Id FROM @TeamFilter))
	AND (@MeetDateFrom IS NULL OR DATEDIFF(DAY, a.MeetDate, @MeetDateFrom)<=0)
	AND (@MeetDateTo IS NULL OR DATEDIFF(DAY, a.MeetDate, @MeetDateTo)>=0)
	AND (up.Id IS NULL OR up.OrganizationId IN (SELECT Id FROM @TeamFilter))
END
GO