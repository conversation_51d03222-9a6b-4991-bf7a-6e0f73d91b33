using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using System.Reflection;
using Webaby.Data;

namespace TinyCRM.DbMigration.Schema
{
    [DbContext(typeof(MigrationDbContext))]
    [Migration(nameof(Migration_000005_procedure__rpt_PreprocessData_BaoCaoNangSuatLaoDong02))]
    internal class Migration_000005_procedure__rpt_PreprocessData_BaoCaoNangSuatLaoDong02 : RawMigration
    {
        protected override Assembly ResourceAssembly => Assembly.GetExecutingAssembly();
    }
}