
/****** Object:  StoredProcedure [telesale].[Lead_GetSummaryStatusByAgent]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[Lead_GetSummaryStatusByAgent]

	@AgentId			UNIQUEIDENTIFIER,
	@TeamId			UNIQUEIDENTIFIER,
	@CampaignId		UNIQUEIDENTIFIER=NULL,
	@UpdateLeadStatusDate			DATETIME=NULL

AS
BEGIN
	
DECLARE @AppWithoutDMOAction INT
--- Chua co kq feedback tu DMO
SELECT @AppWithoutDMOAction = COUNT(*) 
FROM dbo.Appointment a
JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId=a.Id
JOIN dbo.Lead l ON l.CurrentLeadAssignmentId=la.Id
--JOIN dbo.UserProfiles up ON up.Id=l.CreatedBy
JOIN dbo.ProspectAssignment pa ON pa.Id=l.CreatedByProspectAssignmentId
JOIN dbo.Prospect p ON pa.ProspectId=p.Id
WHERE (@TeamId IS NULL AND pa.AssignedAgentId=@AgentId OR pa.AssignedTeamId=@TeamId) AND la.Status=1 AND a.Status=1 AND p.CampaignId=@CampaignId
AND pa.Status<3

DECLARE @AppReturnedMeet INT, @AppReturnedNotMeet int
--- Return -- Meet and NOT Meet
SELECT @AppReturnedMeet = COUNT(*) 
FROM dbo.Appointment a
JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId=a.Id
JOIN dbo.Lead l ON l.CurrentLeadAssignmentId=la.Id
--JOIN dbo.UserProfiles up ON up.Id=l.CreatedBy
JOIN dbo.ProspectAssignment pa ON pa.Id=l.CreatedByProspectAssignmentId
JOIN dbo.Prospect p ON pa.ProspectId=p.Id
WHERE (@TeamId IS NULL AND pa.AssignedAgentId=@AgentId OR pa.AssignedTeamId=@TeamId) AND l.Status <> 4 AND l.Status <> 5 AND a.Status=3 AND p.CampaignId=@CampaignId
AND pa.Status<3

SELECT @AppReturnedNotMeet = COUNT(*) 
FROM dbo.Appointment a
JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId=a.Id
JOIN dbo.Lead l ON l.CurrentLeadAssignmentId=la.Id
--JOIN dbo.UserProfiles up ON up.Id=l.CreatedBy
JOIN dbo.ProspectAssignment pa ON pa.Id=l.CreatedByProspectAssignmentId
JOIN dbo.Prospect p ON pa.ProspectId=p.Id
WHERE (@TeamId IS NULL AND pa.AssignedAgentId=@AgentId OR pa.AssignedTeamId=@TeamId) AND l.Status <> 4 AND l.Status <> 5 AND (a.Status=2 OR a.Status=5) AND p.CampaignId=@CampaignId
AND pa.Status<3

DECLARE @AppDMOProcessing int
-- DMO dang theo
SELECT @AppDMOProcessing = COUNT(*) 
FROM dbo.Appointment a
JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId=a.Id
JOIN dbo.Lead l ON l.CurrentLeadAssignmentId=la.Id
--JOIN dbo.UserProfiles up ON up.Id=l.CreatedBy
JOIN dbo.ProspectAssignment pa ON pa.Id=l.CreatedByProspectAssignmentId
JOIN dbo.Prospect p ON pa.ProspectId=p.Id
WHERE (@TeamId IS NULL AND pa.AssignedAgentId=@AgentId OR pa.AssignedTeamId=@TeamId) AND la.AssignedFieldSaleTeamId IS NOT NULL AND a.Status=1 AND p.CampaignId=@CampaignId
AND pa.Status<3

DECLARE @WinThisWeek int
-- Win this week
SELECT @WinThisWeek = COUNT(*) 
FROM dbo.Appointment a
JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId=a.Id
JOIN dbo.Lead l ON l.CurrentLeadAssignmentId=la.Id
--JOIN dbo.UserProfiles up ON up.Id=l.CreatedBy
JOIN dbo.ProspectAssignment pa ON pa.Id=l.CreatedByProspectAssignmentId
JOIN dbo.Prospect p ON pa.ProspectId=p.Id
WHERE (@TeamId IS NULL AND pa.AssignedAgentId=@AgentId OR pa.AssignedTeamId=@TeamId) AND l.Status=4 AND DATEDIFF(DAY, a.UpdatedResultDate, GETDATE()) <=7 AND p.CampaignId=@CampaignId
AND pa.Status<3

DECLARE @LostThisWeek int
-- Lost this week
SELECT @LostThisWeek = COUNT(*)
FROM dbo.Appointment a
JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId=a.Id
JOIN dbo.Lead l ON l.CurrentLeadAssignmentId=la.Id
--JOIN dbo.UserProfiles up ON up.Id=l.CreatedBy
JOIN dbo.ProspectAssignment pa ON pa.Id=l.CreatedByProspectAssignmentId
JOIN dbo.Prospect p ON pa.ProspectId=p.Id
WHERE (@TeamId IS NULL AND pa.AssignedAgentId=@AgentId OR pa.AssignedTeamId=@TeamId) AND (l.Status=5) AND DATEDIFF(DAY, a.UpdatedResultDate, GETDATE()) <=7 AND p.CampaignId=@CampaignId

SELECT	ISNULL(@AppWithoutDMOAction,0) AppWithoutDmoAction, ISNULL(@AppDmoProcessing,0) AppDmoProcessing, ISNULL(@AppReturnedMeet,0) AppReturnedMeet, 
		ISNULL(@AppReturnedNotMeet,0) AppReturnedNotMeet,
	ISNULL(@WinThisWeek,0) WinThisWeek, ISNULL(@LostThisWeek,0) LostThisWeek 


END
GO