
/****** Object:  StoredProcedure [telesale].[CreateProspectFromAppointmentRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[CreateProspectFromAppointmentRaw]
	@UserId UNIQUEIDENTIFIER,
	@SessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	INSERT INTO dbo.Prospect
	        ( 
			  Id ,
	          ContactId ,
	          CampaignId ,
	          ReferenceObjectId ,
	          CreatedBy ,
	          Notes ,
	          CreatedDate ,
	          IsHot ,
	          [Status] ,
	          NextCallbackDate ,
	          BackToCommonBasketDate ,
	          ReprospectDate ,
	          CurrentAssignmentId
	        )
	SELECT
	ar.ProspectId Id,
	ar.ContactID ContactId,
	(SELECT TOP 1 Id FROM dbo.Campaigns) CampaignId,
	NULL ReferenceObjectId,
	@UserId CreatedBy,
	N'' Notes,
	GETDATE() CreatedDate,
	0 IsHot,
	2 [Status],
	NULL NextCallbackDate ,
	NULL BackToCommonBasketDate ,
	NULL ReprospectDate ,
	ar.ProspectAssignmentId CurrentAssignmentId
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.Prospect p ON ar.ProspectId = p.Id
	WHERE ar.IsInvalid=0 AND ar.ImportSessionId=@SessionId AND p.Id IS NULL
END
GO