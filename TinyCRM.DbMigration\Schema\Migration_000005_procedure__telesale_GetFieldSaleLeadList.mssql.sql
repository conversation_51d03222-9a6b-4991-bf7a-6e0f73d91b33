
/****** Object:  StoredProcedure [telesale].[GetFieldSaleLeadList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[GetFieldSaleLeadList]

	@FieldSaleId	UNIQUEIDENTIFIER,
	@MeetDate		DATETIME

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh x<PERSON>y ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SELECT	la.Id, ap.Id AppointmentId, l.UserDefinedFormatCode, c.Id ContactId, c.<PERSON>,c.<PERSON>O<PERSON>lient<PERSON>,c.<PERSON>tal<PERSON>us ClientMaritalStatus,c.<PERSON>lient<PERSON>, 
			pr.ProductName, ap.MeetDate, FORMAT(ap.MeetDate, 'HH\H') MeetHour, ap.<PERSON><PERSON><PERSON>, ap.<PERSON>,
			d.<PERSON>, pv.ProvinceName, ap.Notes, ISNULL(tmrPro.FullName,tmrUser.UserName) TMR, ISNULL(sugDMOUp.FullName,sugDMOU.UserName) SuggestedDMOName, 
			la.SuggestedFieldSaleReason, dmoU.UserId FieldSaleId, ISNULL(dmoUp.FullName,dmoU.UserId) DMOName, ap.Status AppointmentStatus, aprc.ResultCode AppointmentResultCode
	FROM	dbo.LeadAssignment la
			JOIN dbo.Lead l ON la.Id = l.CurrentLeadAssignmentId
			JOIN dbo.ProspectAssignment pa ON pa.Id = l.CreatedByProspectAssignmentId
			JOIN dbo.aspnet_Users tmrUser ON tmrUser.UserId = pa.AssignedAgentId
			JOIN dbo.UserProfiles tmrPro ON tmrPro.Id = tmrUser.UserId
			JOIN dbo.Prospect p ON p.Id = pa.ProspectId
			JOIN dbo.Contact c ON c.Id = p.ContactId
			LEFT JOIN dbo.Product pr ON pr.Id = l.ProductId
			JOIN dbo.Appointment ap ON ap.Id = la.WaitingAppointmentId
			LEFT JOIN dbo.AppointmentResultCode aprc ON aprc.Id = ap.AppointmentResultCodeId
			LEFT JOIN dbo.District d ON d.Id = ap.DistrictId
			JOIN dbo.Province pv ON pv.Id = ap.ProvinceId
			LEFT JOIN dbo.aspnet_Users dmoU ON dmoU.UserId = la.AssignedFieldSaleId
			LEFT JOIN dbo.UserProfiles dmoUp ON dmoUp.Id = dmoU.UserId
			LEFT JOIN dbo.aspnet_Users sugDMOU ON sugDMOU.UserId = la.SuggestedFieldSaleId
			LEFT JOIN dbo.UserProfiles sugDMOUp ON sugDMOUp.Id = sugDMOU.UserId
	WHERE	la.AssignedFieldSaleId = @FieldSaleId
			AND CAST(ap.MeetDate AS DATE) = CAST(@MeetDate AS DATE)
			AND ap.Status <> 4 AND ap.Status <> 5 -- Hẹn bị hủy và hẹn bị trả
	ORDER BY ap.MeetDate
******/
END
GO