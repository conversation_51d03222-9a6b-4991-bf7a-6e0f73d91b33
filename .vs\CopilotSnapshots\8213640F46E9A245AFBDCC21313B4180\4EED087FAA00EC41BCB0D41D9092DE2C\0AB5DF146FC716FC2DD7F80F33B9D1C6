﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace Webaby.Core.Role.Queries
{
    public class GetRoleByIdQuery : QueryBase<RoleData>
    {
        public GetRoleByIdQuery(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; private set; }
    }

    internal class GetRoleByIdQueryHandler : QueryHandlerBase<GetRoleByIdQuery, RoleData>
    {
        public GetRoleByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<RoleData>> ExecuteAsync(GetRoleByIdQuery query)
        {
            var entities = await EntitySet.Get<AspNetRoleEntity>().Where(x => x.Id == query.Id).ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<RoleData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
