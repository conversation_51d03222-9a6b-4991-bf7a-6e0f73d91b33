﻿
/****** Object:  StoredProcedure [import].[ImportContact_ScanData]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [import].[ImportContact_ScanData]
	@ImportSessionId UNIQUEIDENTIFIER,
	@CampaignId UNIQUEIDENTIFIER,
	@DedupColumnName VARCHAR(50),
	@DedupColumnValidName VARCHAR(50),
	@SecondaryDedupColumnName VARCHAR(50),
	@SecondaryDedupValidColumnName VARCHAR(50),
	@IncludeCustomer BIT
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SET NOCOUNT ON;

	DECLARE @DeduplicationStatement NVARCHAR(MAX)

	DECLARE @DeduplicationParameter NVARCHAR(MAX) = '@ImportSessionId uniqueidentifier'

	DELETE FROM import.ContactRaw WHERE ImportSessionId!=@ImportSessionId
	
	DELETE FROM import.ImportSession WHERE Status = 0 AND Id != @ImportSessionId

	SET @DeduplicationStatement = '
		UPDATE import.ContactRaw SET ErrorCode = ''InvalidData'' WHERE ' + @DedupColumnValidName + ' IS NULL AND ImportSessionId=@ImportSessionId

		UPDATE cig SET ErrorCode = ''DuplicateInternal''
		FROM import.ContactRaw cig
		JOIN
		(
			SELECT
				Id,
				ROW_NUMBER() OVER (PARTITION BY ' + @DedupColumnValidName + ' ORDER BY Id) rn
			FROM import.ContactRaw
			WHERE ImportSessionId = @ImportSessionId
				AND ErrorCode IS NULL
		) ci ON cig.Id = ci.Id
		WHERE ci.rn > 1
		AND cig.ImportSessionId = @ImportSessionId

		UPDATE cr_exist_basket
		SET cr_exist_basket.ContactId = c.Id
		FROM import.ContactRaw cr_exist_basket
		JOIN dbo.Contact c ON ' + IIF(@DedupColumnName <> 'Phone', ' c.' + @DedupColumnName + '=cr_exist_basket.' + @DedupColumnValidName + ' ', '(c.Phone=cr_exist_basket.PhoneNo OR c.Phone2=cr_exist_basket.PhoneNo OR c.Phone3=cr_exist_basket.PhoneNo) ') + '
		WHERE cr_exist_basket.ErrorCode IS NULL
		AND cr_exist_basket.ImportSessionId = @ImportSessionId

		UPDATE cr_exist_sec_basket
		SET cr_exist_sec_basket.' + @SecondaryDedupValidColumnName + '= NULL
		FROM import.ContactRaw cr_exist_sec_basket
		JOIN dbo.Contact c ON c.' + @SecondaryDedupColumnName + '=cr_exist_sec_basket.' + @SecondaryDedupValidColumnName + '
		WHERE cr_exist_sec_basket.ImportSessionId = @ImportSessionId
	'

	EXEC sp_executesql @DeduplicationStatement,
					   @DeduplicationParameter,
					   @ImportSessionId

	UPDATE cr_new SET cr_new.ErrorCode='NewContact', cr_new.ContactId = NEWID()
	FROM import.ContactRaw cr_new
	WHERE cr_new.ImportSessionId=@ImportSessionId
	AND cr_new.ContactId IS NULL 
	AND cr_new.ErrorCode IS NULL


	IF @CampaignId IS NOT NULL
	BEGIN
		UPDATE cr_exist_ncampaign SET cr_exist_ncampaign.ErrorCode='NotInCampaign'
		FROM import.ContactRaw cr_exist_ncampaign
		LEFT JOIN dbo.Prospect p ON p.ContactId=cr_exist_ncampaign.ContactId AND p.CampaignId=@CampaignId
		WHERE cr_exist_ncampaign.ImportSessionId=@ImportSessionId
		AND p.Id IS NULL 
		AND cr_exist_ncampaign.ErrorCode IS NULL
		AND cr_exist_ncampaign.ContactId IS NOT NULL

		UPDATE cr_campaign_nasigned SET cr_campaign_nasigned.ErrorCode='Campaign_NotAssigned'
		FROM import.ContactRaw cr_campaign_nasigned
		JOIN dbo.ProspectAssignment pa ON pa.IsNotCurrent = 0 AND pa.CampaignId = @CampaignId AND pa.ContactId=cr_campaign_nasigned.ContactId
		WHERE cr_campaign_nasigned.ImportSessionId=@ImportSessionId
		AND pa.AssignedTeamId IS NULL 
		AND pa.AssignedAgentId IS NULL 
		AND cr_campaign_nasigned.ErrorCode IS NULL

		UPDATE cr_assigned SET cr_assigned.ErrorCode=
			CASE ISNULL(cr.FollowUpStatus, 0)
				WHEN 0 THEN IIF(pa.AssignedAgentId IS NOT NULL, 'Agent_NotCall', 'Team_NotCall')
				WHEN 1 THEN IIF(pa.AssignedAgentId IS NOT NULL, 'Agent_SimpleResult', 'Team_SimpleResult')
				WHEN 2 THEN IIF(pa.AssignedAgentId IS NOT NULL, 'Agent_DoneLost', 'Team_DoneLost')
				WHEN 3 THEN IIF(pa.AssignedAgentId IS NOT NULL, 'Agent_Appointment', 'Team_Appointment')
				WHEN 4 THEN IIF(pa.AssignedAgentId IS NOT NULL, 'Agent_DoneSuccess', 'Team_DoneSuccess')
			END
		FROM import.ContactRaw cr_assigned
		JOIN dbo.ProspectAssignment pa ON pa.IsNotCurrent=0 AND pa.CampaignId=@CampaignId AND pa.ContactId=cr_assigned.ContactId
		LEFT JOIN dbo.CallResult cr ON pa.CallResultId=cr.Id
		WHERE cr_assigned.ImportSessionId=@ImportSessionId
		AND pa.AssignedTeamId IS NOT NULL
		AND cr_assigned.ErrorCode IS NULL
	END
	ELSE
	BEGIN
	    UPDATE cr_new SET cr_new.ErrorCode='ExistInBasket'
		FROM import.ContactRaw cr_new
		WHERE cr_new.ImportSessionId=@ImportSessionId
		AND cr_new.ErrorCode IS NULL
		AND cr_new.ContactId IS NOT NULL
	END


	UPDATE cr_pvalid SET cr_pvalid.ProvinceValid = p.Id 
	FROM import.ContactRaw cr_pvalid
	JOIN dbo.Province p ON cr_pvalid.Areas = p.ImportCodeName
	WHERE cr_pvalid.ImportSessionId = @ImportSessionId

	UPDATE cr_gvalid SET cr_gvalid.GenderValid = g.Code
	FROM import.ContactRaw cr_gvalid
	JOIN dbo.Gender g ON cr_gvalid.Gender = g.Name
	WHERE cr_gvalid.ImportSessionId = @ImportSessionId

	UPDATE cr_mvalid SET cr_mvalid.MaritalStatusValid = m.Code
	FROM import.ContactRaw cr_mvalid
	JOIN dbo.MaritalStatus m ON cr_mvalid.MaritalStatus = m.Name
	WHERE cr_mvalid.ImportSessionId = @ImportSessionId

	UPDATE import.ContactRaw SET WarningCode = (ISNULL(WarningCode,0)|1) WHERE Areas IS NOT NULL AND ProvinceValid IS NULL AND ImportSessionId=@ImportSessionId AND ErrorCode NOT IN ('InvalidPhone', 'DuplicateInternal')
	UPDATE import.ContactRaw SET WarningCode = (ISNULL(WarningCode,0)|2) WHERE DOB IS NOT NULL AND DOBValid IS NULL AND ImportSessionId=@ImportSessionId AND ErrorCode NOT IN ('InvalidPhone', 'DuplicateInternal')

	IF @IncludeCustomer = 1 BEGIN
		UPDATE cr_cus SET cr_cus.CustomerId = cus.Id
		FROM import.ContactRaw cr_cus
		JOIN dbo.Customer cus ON cr_cus.PhoneNo = cus.Phone1 
			OR cr_cus.PhoneNo = cus.Phone2 
			OR cr_cus.PhoneNo = cus.Phone3
			OR cr_cus.EmailValid = cus.Email
		WHERE cr_cus.ImportSessionId = @ImportSessionId
	END
 ******/
END
GO