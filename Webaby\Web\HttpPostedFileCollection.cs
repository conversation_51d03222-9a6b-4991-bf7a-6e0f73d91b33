﻿using Microsoft.AspNetCore.Http;
using System.Collections;
using System.Collections.Specialized;

namespace Webaby.Web
{
    public class HttpPostedFileCollection : NameObjectCollectionBase
    {
        public IFormFileCollection HttpFileCollectionBase { get; set; }

        public HttpPostedFileCollection()
        {

        }

        public HttpPostedFileCollection(IDictionary d, bool readOnly = true)
        {
            foreach (DictionaryEntry de in d)
            {
                BaseAdd((string)de.Key, de.Value);
            }
            IsReadOnly = readOnly;
        }

        public HttpPostedFileCollection(IFormFileCollection fs, bool readOnly = true)
        {
            HttpFileCollectionBase = fs;

            for (int i = 0; i < fs.Count; i++)
            {
                string key = fs[i].Name;
                BaseAdd(key, fs[i]);
            }
            IsReadOnly = readOnly;
        }

        public DictionaryEntry this[int index]
        {
            get
            {
                return (new DictionaryEntry(
                    BaseGetKey(index), BaseGet(index)));
            }
        }

        public object this[string key]
        {
            get
            {
                return (BaseGet(key));
            }
            set
            {
                BaseSet(key, value);
            }
        }

        public string[] AllKeys
        {
            get
            {
                return (BaseGetAllKeys());
            }
        }

        public Array AllValues
        {
            get
            {
                return (BaseGetAllValues());
            }
        }

        public string[] AllStringValues
        {
            get
            {
                return ((string[])BaseGetAllValues(typeof(string)));
            }
        }

        public bool HasKeys
        {
            get
            {
                return (BaseHasKeys());
            }
        }

        public void Add(string key, object value)
        {
            BaseAdd(key, value);
        }

        public void Remove(string key)
        {
            BaseRemove(key);
        }

        public void Remove(int index)
        {
            BaseRemoveAt(index);
        }

        public void Clear()
        {
            BaseClear();
        }
    }
}