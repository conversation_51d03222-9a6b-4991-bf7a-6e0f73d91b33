
/****** Object:  StoredProcedure [telesale].[GetCallResultByTMRsReport2_BK20161011]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO




CREATE PROCEDURE [telesale].[GetCallResultByTMRsReport2_BK20161011]
	@FromDate		DATETIME,
	@ToDate			DATETIME,
	@TeamId			UNIQUEIDENTIFIER
AS
BEGIN
	IF @FromDate IS NULL OR @ToDate IS NULL THROW 50001,'A date range (FromDate - ToDate) is required to process this report',1

	BEGIN TRANSACTION
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT *
	INTO #teams 
	FROM dbo.GetAllOffspringOrganizations(@TeamId,'TMR Team')

	SELECT ('Q'+cr.Code) AS code,	cr.Id, cr.IsConnected,cr.<PERSON>Contacted, cr.IsConsulted, cc.CreatedBy AgentId,COUNT(*) Total
	INTO #cte
	FROM	dbo.ContactCall cc
			JOIN dbo.CallResult cr ON cr.Id = cc.CallResultId
			JOIN dbo.UserProfiles up ON up.Id = cc.CreatedBy
			LEFT JOIN #teams o ON o.Id=up.OrganizationId
	WHERE	(cc.CreatedDate >= @FromDate) AND (cc.CreatedDate < DATEADD(DAY, 1, @ToDate)) 
			AND (@TeamId IS NULL or o.Id IS NOT NULL)  
	GROUP BY cr.Code, cr.Id, cr.IsConnected,cr.IsContacted, cr.IsConsulted,cc.CreatedBy


	;WITH 
				HenMoi AS 
				(
					SELECT	'NewApp' HenMoi, cc.CreatedBy AgentId,COUNT(*) Total
						FROM	dbo.Appointment cc
								JOIN dbo.UserProfiles up ON up.Id = cc.CreatedBy
								LEFT JOIN dbo.GetAllOffspringOrganizations(@TeamId,'TMR Team') o ON o.Id=up.OrganizationId
						WHERE	cc.CreatedDate >= @FromDate AND cc.CreatedDate < DATEADD(DAY, 1, @ToDate)
								AND cc.Status <> 4 AND cc.Status <> 5 AND cc.PreviousAppointmentId IS NULL
								AND (@TeamId IS NULL or o.Id IS NOT NULL) 
						GROUP BY cc.CreatedBy
				),
				TotalConnected AS (
					SELECT #cte.AgentId,sum(#cte.Total)  Total FROM #cte WHERE #cte.IsConnected=1 GROUP BY #cte.AgentId 
				)		
				SELECT IIF(po.id IS NULL,'',po.OrganizationName+' - ') + o.OrganizationName Team, up.FullName AgentName,up.AgentCode,y.* 
				FROM (
				SELECT * 
				FROM 
				(
					SELECT CAST(Code AS VARCHAR(100)) Code, AgentId,Total FROM #cte
					UNION all
					SELECT 'TotalContactableCases', #cte.AgentId, SUM(#cte.Total) FROM #cte WHERE #cte.IsContacted=1 GROUP BY #cte.AgentId
					UNION ALL
						SELECT 'TotalUnContactableCases', #cte.AgentId, SUM(#cte.Total) FROM #cte WHERE #cte.IsContacted IS NULL OR #cte.IsContacted!=1 GROUP BY #cte.AgentId
					UNION all
					SELECT 'PercentageTotalConnectedCallsOverTotalDials', #cte.AgentId, ROUND(cast( SUM(IIF(#cte.IsConnected=1,#cte.Total,0 )) AS FLOAT) *100 / SUM(#cte.Total),1) FROM #cte GROUP BY #cte.AgentId
					UNION  ALL
						SELECT * FROM HenMoi
					UNION ALL
						SELECT 'PercentageAppointmentMadeTotalConnectedCalls',hm.AgentId, ROUND(CAST( hm.Total AS FLOAT) *100 / tc.Total,1) Total 
						FROM HenMoi hm JOIN TotalConnected tc ON tc.AgentId = hm.AgentId
					UNION ALL
						SELECT 'TotalConnected' ,TotalConnected.AgentId,TotalConnected.Total FROM TotalConnected
				) c
				PIVOT (
					SUM(total) FOR Code IN ([Q01], [Q02], [Q03], [Q04], [Q05], [Q06], [Q07], [Q08], [Q09], [Q10], [Q11], [Q12],[NewApp],[TotalConnected]
					,[TotalUnContactableCases],[TotalContactableCases],[PercentageTotalConnectedCallsOverTotalDials],[PercentageAppointmentMadeTotalConnectedCalls])
				) pcte
				) y 
				JOIN dbo.UserProfiles up ON up.Id=y.AgentId
				JOIN dbo.Organization o ON o.Id=up.OrganizationId
				LEFT JOIN dbo.Organization po ON po.Id=o.ParentId
				ORDER BY o.ParentId, o.OrganizationName, up.FullName
				
		COMMIT

	--DECLARE @PivotColumns NVARCHAR(MAX)
	--SELECT	@PivotColumns = COALESCE(@PivotColumns + ', ', '') + '[' + CAST(up.Id AS NVARCHAR(50)) + ']'
	--FROM	dbo.UserProfiles up
	--		JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
	--WHERE	up.OrganizationId = @TeamId

END
GO