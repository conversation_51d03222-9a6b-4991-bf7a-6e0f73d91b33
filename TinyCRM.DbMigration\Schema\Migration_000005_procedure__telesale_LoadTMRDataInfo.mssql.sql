﻿
/****** Object:  StoredProcedure [telesale].[LoadTMRDataInfo]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- =============================================
CREATE PROCEDURE [telesale].[LoadTMRDataInfo]
	@UserId UNIQUEIDENTIFIER
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SET NOCOUNT ON;
    SELECT 
	COUNT(*) TotalKeep
	--, SUM( IIF(l.id IS NOT NULL ,1,0)) WithOpenLead
	--, SUM( IIF(l.id IS NULL ,1,0)) No_OpenLead
	--, SUM( IIF(p.HotListGroupId IS NOT NULL,1,0)) Hot
	--, SUM( IIF(p.HotListGroupId IS NULL,1,0)) Mass
	, SUM( IIF(p.HotListGroupId IS NULL AND l.id IS NULL,1,0)) Mass_NoOpenLead
	, SUM( IIF(p.HotListGroupId IS NULL AND l.id IS NOT NULL,1,0)) Mass_OpenLead
	, SUM( IIF(p.HotListGroupId IS NOT NULL AND l.id IS NULL,1,0)) Hot_NoOpenLead
	, SUM( IIF(p.HotListGroupId IS NOT NULL AND l.id IS NOT NULL,1,0)) Hot_OpenLead
	FROM dbo.ProspectAssignment pa
	JOIN dbo.Prospect p ON p.Id=pa.ProspectId
	JOIN dbo.Contact c ON c.Id=p.ContactId
	LEFT JOIN dbo.Lead l ON l.Status<4 AND l.CreatedByProspectAssignmentId=pa.Id
	LEFT JOIN dbo.LeadAssignment la ON la.Id=l.CurrentLeadAssignmentId
	JOIN dbo.aspnet_Users u ON u.UserId=pa.AssignedAgentId
	WHERE pa.Status<3
	AND u.UserId=@UserId
******/
END
GO