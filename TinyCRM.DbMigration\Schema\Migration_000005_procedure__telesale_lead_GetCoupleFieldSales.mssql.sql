
/****** Object:  StoredProcedure [telesale].[lead_GetCoupleFieldSales]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


--ALTER PROCEDURE [telesale].[lead_GetCoupleFieldSales]
CREATE PROCEDURE [telesale].[lead_GetCoupleFieldSales]

	@AgentId				UNIQUEIDENTIFIER,
	@ProvinceId				UNIQUEIDENTIFIER,
	@MeetDate				DATETIME

AS
BEGIN
	
	SELECT	DISTINCT afsc.FieldSaleId, up.FullName FieldSaleName, up.OrganizationId, ISNULL(temp.AppointmentCount,0) DayAppointmentCount
	FROM	dbo.AgentFieldSaleCouple afsc
			JOIN dbo.UserProfiles up ON up.Id = afsc.FieldSaleId
			JOIN dbo.aspnet_Membership m ON m.UserId = up.Id
			JOIN dbo.Organization team ON team.Id = up.OrganizationId
			JOIN dbo.OrganizationWorkingArea orgWork ON orgWork.OrganizationId = team.Id
			LEFT JOIN
			(
				SELECT	afsc.FieldSaleId,
						COUNT(*) AppointmentCount
				FROM	dbo.AgentFieldSaleCouple afsc
						JOIN dbo.LeadAssignment la ON la.AssignedFieldSaleId = afsc.FieldSaleId
						JOIN dbo.Appointment ap ON ap.LeadAssignmentId = la.Id
				WHERE	afsc.AgentId = @AgentId
						AND DATEDIFF(DAY, ap.MeetDate, GETDATE()) = 0
				GROUP BY afsc.FieldSaleId
			) temp ON temp.FieldSaleId = afsc.FieldSaleId
	WHERE	m.IsApproved = 1
			AND afsc.AgentId = @AgentId
			AND orgWork.ProvinceId = @ProvinceId

END
GO