
/****** Object:  StoredProcedure [telesale].[Contract_GetContractHistories]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[Contract_GetContractHistories]

	@DynamicFormValueId		UNIQUEIDENTIFIER

AS
BEGIN

	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	SELECT	*
	INTO	#TempAudits
	FROM	(
				SELECT	en.*, ROW_NUMBER() OVER (PARTITION BY en.AuditSessionId ORDER BY en.ModifiedDate) RowNumber
				FROM	dbo.AuditEntityChange en
						JOIN dbo.DynamicFieldValue dfv ON dfv.Id = en.KeyValue
						JOIN dbo.DynamicFormValue dfrv ON dfv.DynamicFormValueId = dfrv.Id
				WHERE	dfrv.Id = @DynamicFormValueId
			) temp
	WHERE	temp.RowNumber = 1
	
	INSERT	#TempAudits
	(
		AuditSessionId,
		ModifiedDate,
		ModifiedBy,
		KeyValue,
		TableName,
		Action,
		Id,
		RowNumber
	)
	SELECT	en.AuditSessionId,
			en.ModifiedDate,
			en.ModifiedBy,
			en.KeyValue,
			en.TableName,
			en.Action,
			en.Id,
			1 RowNumber
	FROM	dbo.AuditEntityChange en
			JOIN dbo.DynamicFormValue dfrv ON en.KeyValue = dfrv.Id
			LEFT JOIN #TempAudits temp ON temp.AuditSessionId = en.AuditSessionId
	WHERE	dfrv.Id = @DynamicFormValueId
			AND temp.Id IS NULL

	SELECT	tempEn.Id AuditId, tempEn.KeyValue, tempEn.[Action], tempEn.ModifiedBy, ISNULL(up.FullName, u.UserName) ModifiedByUserName, tempEn.ModifiedDate, dfd.[Order],
			ef.Id FieldId, 
			ef.MemberName,
			CASE
				WHEN ef.MemberName = 'Status' THEN N'Trạng thái'
				WHEN ef.MemberName = 'TempLockDate' THEN N'Ngày chốt hợp đồng'
				WHEN ef.MemberName = 'LockDate' THEN N'Ngày thanh toán hợp đồng'
                ELSE dfd.DisplayName
			END MemberDescription,
			dfd.DataType,
			CASE
				WHEN ef.MemberName = 'Status' THEN
					CASE
						WHEN ISNULL(ef.OldValue, '') = 'Open' THEN N'Mới tạo'
						WHEN ISNULL(ef.OldValue, '') = 'TempLock' THEN N'Chốt hợp đồng'
						WHEN ISNULL(ef.OldValue, '') = 'Lock' THEN N'Đã thanh toán'
					END
				WHEN dfd.ViewHint = 'Province' THEN pvOld.ProvinceName
				WHEN dfd.ViewHint = 'District' THEN dsOld.DistrictName
				WHEN dfd.ViewHint = 'Ward' THEN wOld.WardName
				WHEN dfd.ViewHint = 'File' THEN fOld.FileName
				ELSE ISNULL(ef.OldValue, '')
			END OldValue,
			CASE
				WHEN dfd.ViewHint = 'Province' THEN pvOld.Id
				WHEN dfd.ViewHint = 'District' THEN dsOld.Id
				WHEN dfd.ViewHint = 'Ward' THEN wOld.Id
				WHEN dfd.ViewHint = 'File' THEN fOld.Id
				ELSE NULL
			END OldValueId,
			CASE
				WHEN ef.MemberName = 'Status' THEN
					CASE
						WHEN ISNULL(ef.NewValue, '') = 'Open' THEN N'Mới tạo'
						WHEN ISNULL(ef.NewValue, '') = 'TempLock' THEN N'Chốt hợp đồng'
						WHEN ISNULL(ef.NewValue, '') = 'Lock' THEN N'Đã thanh toán'
					END
				WHEN dfd.ViewHint = 'Province' THEN pvNew.ProvinceName
				WHEN dfd.ViewHint = 'District' THEN dsNew.DistrictName
				WHEN dfd.ViewHint = 'Ward' THEN wNew.WardName
				WHEN dfd.ViewHint = 'File' THEN fNew.FileName
				ELSE ISNULL(ef.NewValue, '')
			END NewValue,
			CASE
				WHEN dfd.ViewHint = 'Province' THEN pvNew.Id
				WHEN dfd.ViewHint = 'District' THEN dsNew.Id
				WHEN dfd.ViewHint = 'Ward' THEN wNew.Id
				WHEN dfd.ViewHint = 'File' THEN fNew.Id
				ELSE NULL
			END NewValueId
	FROM	#TempAudits tempEn
			JOIN aspnet_Users u ON u.UserId = tempEn.ModifiedBy
			JOIN UserProfiles up ON u.UserId = up.Id
			JOIN AuditEntityChange en ON en.AuditSessionId = tempEn.AuditSessionId
			LEFT JOIN dbo.DynamicFieldValue dfv ON dfv.Id = en.KeyValue
			LEFT JOIN dbo.DynamicFieldDefinition dfd ON dfd.Id = dfv.DynamicFieldId
			JOIN AuditFieldChange ef ON en.Id = ef.AuditId
			--=============================================================================================================
			-- Old Province, District, Ward
			LEFT JOIN dbo.Province pvOld ON pvOld.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT) AND dfd.ViewHint = 'Province'
			LEFT JOIN dbo.District dsOld ON dsOld.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT) AND dfd.ViewHint = 'District'
			LEFT JOIN dbo.Ward wOld ON wOld.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT) AND dfd.ViewHint = 'Ward'
			-- New Province, District, Ward
			LEFT JOIN dbo.Province pvNew ON pvNew.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT) AND dfd.ViewHint = 'Province'
			LEFT JOIN dbo.District dsNew ON dsNew.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT) AND dfd.ViewHint = 'District'
			LEFT JOIN dbo.Ward wNew ON wNew.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT) AND dfd.ViewHint = 'Ward'
			--=============================================================================================================
			-- Old File
			LEFT JOIN dbo.[File] fOld ON fOld.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT) AND dfd.ViewHint = 'File'
			-- New File
			LEFT JOIN dbo.[File] fNew ON fNew.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT) AND dfd.ViewHint = 'File'
	WHERE	ISNULL(ef.OldValue,'') <> ''
			OR isnull(ef.NewValue,'') <> ''
	ORDER BY tempEn.ModifiedDate DESC

END
GO