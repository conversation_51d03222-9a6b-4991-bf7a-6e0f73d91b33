﻿using System;
using System.Linq;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Core.DueTime.Queries
{
    public class GetDueTimeReferenceByObjectIdQuery : QueryBase<DueTimeReferenceData>
    {
        public Guid Id { get; set; }

        public GetDueTimeReferenceByObjectIdQuery(Guid referenceObjectId)
        {
            Id = referenceObjectId;
        }
    }

    internal class GetDueTimeReferenceByObjectIdQueryHandler : QueryHandlerBase<GetDueTimeReferenceByObjectIdQuery, DueTimeReferenceData>
    {
        public GetDueTimeReferenceByObjectIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DueTimeReferenceData>> ExecuteAsync(GetDueTimeReferenceByObjectIdQuery query)
        {
            var queryable = EntitySet.Get<DueTimeReferenceEntity>();
            var result = await queryable.Where(duetime => duetime.ReferenceObjectId == query.Id).ToListAsync();

            return QueryResult.Create(result, x => Mapper.Map<DueTimeReferenceData>(x));
        }
    }
}

    