﻿using System;
using TinyCRM.Enums;
using Webaby.Data;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TinyCRM.RequestTicket
{
    [Table("ProductExchange", Schema = "dbo")]
    [AuditEntityChange]
    public class ProductExchangeEntity : IEntity, ICreatedDateEnabledEntity, ICreatedByEnabledEntity, IModifiedDateEnabledEntity, IModifiedByEnabledEntity, ISoftDeleteEnabledEntity, IDeletedByEnabledEntity
    {
        [Key]
        public Guid Id { get; set; }

        [Column]
        [AuditFieldChange]
        public Guid RequestTicketId { get; set; }

        [Column]
        [AuditFieldChange]
        public Guid ProductId { get; set; }

        [Column]
        [AuditFieldChange]
        public int Quantity { get; set; }

        [Column]
        [AuditFieldChange]
        public ExchangeSourceType SourceType { get; set; }

        [Column]
        [AuditFieldChange]
        public Guid? FactoryId { get; set; }

        [Column]
        [AuditFieldChange]
        public Guid? DistributorId { get; set; }

        [Column]
        [AuditFieldChange]
        public Guid? IntroStoreId { get; set; }

        [Column]
        [AuditFieldChange]
        public Guid? PartnerStoreId { get; set; }

        [Column]
        [AuditFieldChange]
        public Guid? CustomerCareId { get; set; }

        [Column]
        [AuditFieldChange]
        public Guid? WareHouseId { get; set; }

        [Column]
        public Guid? ExecutedByTaskId { get; set; }

        [Column]
        public DateTime CreatedDate { get; set; }

        [Column]
        public Guid CreatedBy { get; set; }

        [Column]
        public DateTime? ModifiedDate { get; set; }

        [Column]
        public Guid? ModifiedBy { get; set; }
        
        [Column]
        public bool Deleted { get; set; }

        [Column]
        public DateTime? DeletedDate { get; set; }

        [Column]
        public Guid? DeletedBy { get; set; }
    }
}

