
/****** Object:  StoredProcedure [telesale].[CallResultSumary]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[CallResultSumary]

	@CampaignId			UNIQUEIDENTIFIER,
	@FromDate			DATETIME,
	@ToDate				DATETIME,
	@OrganizationId		UNIQUEIDENTIFIER,
	@AssignedAgentId	UNIQUEIDENTIFIER

AS
BEGIN

	WITH cte(Id) AS
	(
		SELECT	Id
		FROM	dbo.Organization org
		WHERE	org.Id = @OrganizationId
		UNION ALL
		SELECT	org.Id
		FROM	dbo.Organization org
				JOIN cte temp ON temp.Id = org.ParentId
	)
	SELECT	cte.Id
	INTO	#TempOrgs
	FROM	cte

	DECLARE @WhereString NVARCHAR(MAX) = N''
	IF @CampaignId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' cc.CampaignId = @CampaignId '
	END
	IF @AssignedAgentId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' cc.CreatedBy = @AssignedAgentId '
	END

	IF @FromDate IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' cc.CreatedDate >= @FromDate '
	END
	IF @ToDate IS NOT NULL
	BEGIN
		SET @ToDate = DATEADD(DAY, 1, @ToDate)
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' cc.CreatedDate < @ToDate '
	END

	DECLARE @SelectString NVARCHAR(MAX) = N'
	SELECT	cr.Code CallResultCode, cr.EnglishDescription, cr.VietnameseDescription, cr.FollowUpStatus,
			org.Name OrganizationName, up.FullName TelesaleName,
			ISNULL(temp.TotalCount,0) TotalCount, ISNULL(temp.TotalDuration,0) TotalDuration
	'

	DECLARE @FromString NVARCHAR(MAX) = N'
	FROM	dbo.CallResult cr
			LEFT JOIN
			(
				SELECT	cc.CallResultId, cc.CreatedByTeamId AssignedTeamId, cc.CreatedBy AssignedAgentId, COUNT(*) TotalCount, SUM(cc.Duration) TotalDuration
				FROM	dbo.ContactCall cc '
						+ IIF (@OrganizationId IS NULL, N'', N' JOIN #TempOrgs tempOrg ON tempOrg.Id = cc.CreatedByTeamId ')  + N'
				' + @WhereString + N'
				GROUP BY cc.CallResultId, cc.CreatedByTeamId, cc.CreatedBy
			) temp ON cr.Id = temp.CallResultId
			LEFT JOIN dbo.Organization org ON org.Id = temp.AssignedTeamId
			LEFT JOIN dbo.UserProfiles up ON up.Id = temp.AssignedAgentId
	WHERE	cr.ResultCodeSuiteId = (SELECT ResultCodeSuiteId FROM dbo.Campaign WHERE Id = @CampaignId)
	'

	DECLARE @ParamDefs NVARCHAR(MAX) = N'
		@CampaignId			UNIQUEIDENTIFIER,
		@FromDate			DATETIME,
		@ToDate				DATETIME,
		@OrganizationId		UNIQUEIDENTIFIER,
		@AssignedAgentId	UNIQUEIDENTIFIER
	'

	DECLARE @FullExecString NVARCHAR(MAX) = @SelectString + @FromString

	EXECUTE sp_executesql @FullExecString, @ParamDefs,
											@CampaignId = @CampaignId,
											@FromDate = @FromDate,
											@ToDate = @ToDate,
											@OrganizationId = @OrganizationId,
											@AssignedAgentId = @AssignedAgentId

END
GO