
/****** Object:  StoredProcedure [telesale].[lead_GetContactAppointmentHistories]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [telesale].[lead_GetContactAppointmentHistories]
	@ContactId		UNIQUEIDENTIFIER
AS
BEGIN

	SELECT	ap.*, l.LeadCode, apr.Id AppointmentResultCodeId, apr.ResultCode, apr.DescriptionVn AppointmentResultDescription, apr.AppointmentStatus, apr.LeadStatus, ap.CreatedBy, up.FullName CreatedByName,
			w.Name <PERSON>, d.Name <PERSON>ame, pv.Name ProvinceName,
			dmoPr.FullName FieldSaleName, dmoPr.AgentCode FieldSaleCode, dmoPr.PhoneNumber FieldSalePhone,
			fileSaleLeader.FieldSaleLeaderName, fileSaleLeader.FieldSaleLeaderPhone,
			p.Name <PERSON>Name, ap.Product<PERSON>udget, la.AssignedField<PERSON>aleTeamId, la.AssignedFieldSaleId, la.SuggestedFieldSaleId, la.SuggestedFieldSaleTeamId, la.SuggestedFieldSaleReason
	FROM	dbo.Appointment ap
			JOIN dbo.UserProfiles up ON up.Id = ap.CreatedBy
			JOIN dbo.LeadAssignment la ON la.Id = ap.LeadAssignmentId
			JOIN dbo.Lead l ON l.Id = la.LeadId
			LEFT JOIN dbo.Geolocation w ON w.Id = ap.WardId
			LEFT JOIN dbo.Geolocation d ON d.Id = ap.DistrictId
			LEFT JOIN dbo.Geolocation pv ON pv.Id = ap.ProvinceId
			LEFT JOIN dbo.Product p ON p.Id = ap.ProductId
			LEFT JOIN dbo.UserProfiles dmoPr ON dmoPr.Id = la.AssignedFieldSaleId
			LEFT JOIN dbo.AppointmentResultCode apr ON apr.Id = ap.AppointmentResultCodeId
			LEFT JOIN
			(
				SELECT	org.Id FieldSaleTeamId, up.FullName FieldSaleLeaderName, up.PhoneNumber FieldSaleLeaderPhone
				FROM	dbo.Organization org
						JOIN dbo.UserProfiles up ON up.OrganizationId = org.Id
						JOIN dbo.UserInRoles uir ON uir.UserId = up.Id AND uir.RoleId = '94E5A159-A404-4D5C-9D6D-BE0C49ADC34C'
						JOIN dbo.aspnet_Membership m ON m.UserId = up.Id AND m.IsApproved = 1
				--WHERE	org.OrganizationType = 'DMO Team'
			) fileSaleLeader ON fileSaleLeader.FieldSaleTeamId = la.AssignedFieldSaleTeamId
	WHERE	ap.ContactId = @ContactId
	ORDER BY ap.CreatedDate DESC

END
GO