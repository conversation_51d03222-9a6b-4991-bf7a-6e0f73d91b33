﻿
/****** Object:  StoredProcedure [telesale].[GetTempLockDynamicFormValueReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[GetTempLockDynamicFormValueReport]

	@CampaignId		UNIQUEIDENTIFIER,
	@AgentId		UNIQUEIDENTIFIER,
	@TempLockDays	INT

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SELECT	tempPivot.DaysGroup,
			ISNULL(tempPivot.[0],0) [LeftDays_0],
			ISNULL(tempPivot.[1],0) [LeftDays_1],
			ISNULL(tempPivot.[2],0) [LeftDays_2],
			ISNULL(tempPivot.[3],0) [LeftDays_3],
			ISNULL(tempPivot.[4],0) [LeftDays_4],
			ISNULL(tempPivot.[5],0) [LeftDays_5],
			ISNULL(tempPivot.[6],0) [LeftDays_6],
			ISNULL(tempPivot.[7],0) [LeftDays_7],
			ISNULL(tempPivot.[8],0) [LeftDays_8],
			ISNULL(tempPivot.[9],0) [LeftDays_9],
			ISNULL(tempPivot.[10],0) [LeftDays_10],
			ISNULL(tempPivot.[11],0) [LeftDays_11],
			ISNULL(tempPivot.[12],0) [LeftDays_12],
			ISNULL(tempPivot.[13],0) [LeftDays_13],
			ISNULL(tempPivot.[14],0) [LeftDays_14],
			ISNULL(tempPivot.[15],0) [LeftDays_15],
			ISNULL(tempPivot.[16],0) [LeftDays_16],
			ISNULL(tempPivot.[17],0) [LeftDays_17],
			ISNULL(tempPivot.[18],0) [LeftDays_18],
			ISNULL(tempPivot.[19],0) [LeftDays_19],
			ISNULL(tempPivot.[20],0) [LeftDays_20],
			ISNULL(tempPivot.[21],0) [LeftDays_21]
	FROM	(
				SELECT	temp.DayLefts TempLockDayLefts,
						COUNT(*) TotalCount,
						CASE
							WHEN temp.DayLefts >= 15 THEN N'21-15'
							WHEN temp.DayLefts >= 8 THEN N'14-8'
							ELSE N'7-0'
						END DaysGroup,
						CASE
							WHEN temp.DayLefts >= 15 THEN 1
							WHEN temp.DayLefts >= 8 THEN 2
							ELSE 3
						END DaysGroupOrder
				FROM	(
							SELECT	@TempLockDays - DATEDIFF(DAY, dfv.TempLockDate, GETDATE()) DayLefts
							FROM	dbo.Prospect p
									JOIN dbo.ProspectAssignment pa ON pa.Id = p.CurrentAssignmentId
									JOIN dbo.DynamicFormValue dfv ON dfv.ProspectAssignmentId = pa.Id
							WHERE	dfv.Status = 2 AND dfv.ReferencerObjectType = 1
									AND p.CampaignId = @CampaignId
									AND pa.AssignedAgentId = @AgentId
						) temp
				GROUP BY temp.DayLefts
			) temp
			PIVOT
			(
				SUM(temp.TotalCount)
				FOR temp.TempLockDayLefts IN ([0], [1], [2], [3], [4], [5], [6], [7], [8], [9], [10], [11], [12], [13], [14], [15], [16], [17], [18], [19], [20], [21])
			) tempPivot
******/
END
GO