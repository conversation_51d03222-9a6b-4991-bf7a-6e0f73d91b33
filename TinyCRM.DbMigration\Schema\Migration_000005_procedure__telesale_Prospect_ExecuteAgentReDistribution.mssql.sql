
/****** Object:  StoredProcedure [telesale].[Prospect_ExecuteAgentReDistribution]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO




CREATE procedure [telesale].[Prospect_ExecuteAgentReDistribution]
	@selectAgentIds ObjectNumberList readonly,
	@selectProspectAssignmentIds IdList readonly,
	@hotListGroup uniqueidentifier,
	@lastAgentId uniqueidentifier,
	@openLead bit,
	@distributedDate datetime = null
	as
begin
	declare @findTable TABLE (
		ProspectAssignmentId uniqueidentifier,
		HotListGroupId uniqueidentifier,
		HotListGroupName nvarchar(max),
		ContactId uniqueidentifier,
		ContactFullName nvarchar(max),
		DataSource nvarchar(max),
		PreviousProspectAssingmentId uniqueidentifier,
		LastTMRId uniqueidentifier,
		LastTMRName nvarchar(max),
		AgentCode nvarchar(max),
		LastTeamId uniqueidentifier,
		LastTeamName nvarchar(max),
		MeetDate datetime,
		OpenLead bit,
		RowNumber int,
		TotalCount int
	)

	declare @teamId uniqueidentifier
	set @teamId = (select top 1 up.OrganizationId from @selectAgentIds a join dbo.UserProfiles up on up.Id=a.Id)
	if (select count(*) from @selectProspectAssignmentIds) = 0
	begin
	insert into @findTable exec dbo.SearchMovedProspectAssignment @hotListGroupId = @hotListGroup, 
																  @lastAgentId = @lastAgentId, 
																  @openLead = @openLead,
																  @startRow = NULL,
																  @endRow = NULL
	end

	select Id, x.AgentId
	into	#temptt
	from
			(
				select	Id AgentId, Number ToBeAssigned , sum(Number) over (order by Id desc) RowOrderUpper
				from	@selectAgentIds
			) x 
			join 
			(
				select	[@findTable].ProspectAssignmentId Id, row_number() over (order by ProspectAssignmentId) RowOrder
				from	@findTable
				union all
				select	Id, row_number() over (order by Id) RowOrder 
				from	@selectProspectAssignmentIds
			) pp on pp.RowOrder > x.RowOrderUpper - x.ToBeAssigned AND pp.RowOrder <= x.RowOrderUpper

	-- update Prospect Assignment 
	update	pa 
	set		AssignedAgentId=yy.AgentId, AssignedAgentDate=@distributedDate,
			AssignedTeamId=up.OrganizationId,AssignedTeamDate=@distributedDate,
			Ignore15DaysRule=1
	from	#temptt yy 
			JOIN dbo.ProspectAssignment pa ON pa.Id=yy.Id
			JOIN dbo.UserProfiles up ON up.Id=yy.AgentId

	-- update Prospect Assignment 
	update	p
	set		BackToCommonBasketDate=NULL, Status=2
	from	#temptt tt
			join dbo.ProspectAssignment pa ON pa.Id = tt.Id
			join dbo.Prospect p ON p.CurrentAssignmentId = pa.Id
end
GO