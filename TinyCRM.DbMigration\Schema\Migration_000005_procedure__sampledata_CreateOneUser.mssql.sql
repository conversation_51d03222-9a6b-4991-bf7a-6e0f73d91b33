
/****** Object:  StoredProcedure [sampledata].[<PERSON>reateOneUser]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [sampledata].[CreateOneUser] @UserName NVARCHAR(256), @Email NVARCHAR(256), @OrganizationId UNIQUEIDENTIFIER, @roleID UNIQUEIDENTIFIER, @fixedUserId UNIQUEIDENTIFIER NULL = NULL
AS BEGIN
    IF NOT EXISTS (SELECT * FROM dbo.aspnet_Users WHERE UserName=@UserName)
	BEGIN
        DECLARE @UserId UNIQUEIDENTIFIER
        DECLARE @UserInRoleId UNIQUEIDENTIFIER=NEWID();
        DECLARE @BusinessRole NVARCHAR(500);

		SET @UserId = ISNULL(@fixedUserId,NEWID())

        SELECT @BusinessRole=Name FROM dbo.Roles WHERE Id=@roleID;
        INSERT [dbo].[aspnet_Users]([ApplicationId], [UserId], [UserName], [LoweredUserName], [MobileAlias], [IsAnonymous], [LastActivityDate])
        VALUES(N'8acbf84f-0d86-4929-b0a8-72c5f1f6478a', @UserId, @UserName, @UserName, NULL, 0, CAST(N'2018-10-30 09:27:57.430' AS DATETIME));
        INSERT [dbo].[aspnet_Membership]([ApplicationId], [UserId], [Password], [PasswordFormat], [PasswordSalt], [MobilePIN], [Email], [LoweredEmail], [PasswordQuestion], [PasswordAnswer], [IsApproved], [IsLockedOut], [CreateDate], [LastLoginDate], [LastPasswordChangedDate], [LastLockoutDate], [FailedPasswordAttemptCount], [FailedPasswordAttemptWindowStart], [FailedPasswordAnswerAttemptCount], [FailedPasswordAnswerAttemptWindowStart], [Comment])
        VALUES(N'8acbf84f-0d86-4929-b0a8-72c5f1f6478a', @UserId, N'xpnBof1D4bDwUlSfwPwkohrkcwQ=', 1, N'Jxu1v8GHP7n9tof8spsAXg==', NULL, @Email, @Email, NULL, NULL, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CAST(N'1754-01-01 00:00:00.000' AS DATETIME), CURRENT_TIMESTAMP, 0, CAST(N'1754-01-01 00:00:00.000' AS DATETIME), 0, CAST(N'1754-01-01 00:00:00.000' AS DATETIME), NULL);
        INSERT [dbo].[UserInRoles]([Id], [RoleId], [UserId])
        VALUES(@UserInRoleId, @roleID, @UserId);
        INSERT [dbo].[UserProfiles]([Id], [OrganizationId], [FullName], [PhoneNumber], [AvayaAgentID], [eOfficeId], [eOfficeEnabled], [Email], [BusinessRole], [PictureId], [Extension], [AgentCode], [CompetenceLevel])
        VALUES(@UserId, @OrganizationId, @UserName, NULL, NULL, NULL, NULL, @Email, @BusinessRole, NULL, NULL, NULL, NULL);
    END;
END
GO