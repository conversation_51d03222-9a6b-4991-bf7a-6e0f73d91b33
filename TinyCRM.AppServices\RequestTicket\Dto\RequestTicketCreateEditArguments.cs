﻿using System;
using System.Collections.Generic;
using TinyCRM.Customer.Queries;
using TinyCRM.Enums;
using TinyCRM.RequestTicket;
using TinyCRM.TbCallback;
using Webaby.Core.DueTime.Queries;

namespace TinyCRM.AppServices.RequestTicket.Dto
{
    public class RequestTicketCreateEditArguments
    {
        public bool IsNew { get; set; }

        public Guid? Id { get; set; }

        public bool UsingIsoCode { get; set; }

        public string Code { get; set; }

        public string IsoCode { get; set; }

        public int SourceChannel { get; set; }

        public Boolean DelegatedTicket { get; set; }
        public TicketDelegatedRelationship? DelegatedRelationship { get; set; }
        public string DelegatedOtherRelationship { get; set; }

        public string RpName { get; set; }
        public string RpPhone { get; set; }
        public string RpEmail { get; set; }

        public List<Guid> BehaviorClassifications { get; set; }

        public Difficulty DifficultyDegree { get; set; }

        public RequestTicketStatus Status { get; set; }

        public Guid? OwnerId { get; set; }

        public Guid ServiceTypeId { get; set; }

        public RequestTicketCustomer Customer { get; set; }

        public Guid? TicketBusinessResultId { get; set; }

        public Guid? ProcessDueTimeId { get; set; }
        public DueTimeInfo ProcessDueTime { get; set; }
        public DateTime? ProcessDueDate { get; set; }

        public Guid? AcceptDueTimeId { get; set; }
        public DateTime? AcceptDueDate { get; set; }
        public DueTimeInfo AcceptDueTime { get; set; }

        public DateTime? OpenTicketDate { get; set; }

        public DateTime? AcceptedTicketDate { get; set; }
        public DateTime? FinishedTicketDate { get; set; }

        public string Notes { get; set; }

        public string Treatment { get; set; }

        public Guid? BudgetId { get; set; }

        public Guid? DepartmentId { get; set; }

        public Guid? OpenTask { get; set; }

        public DateTime? ExpiredDate { get; set; }

        public Guid? DynamicFormId { get; set; }

        public Guid? DynamicFormValueId { get; set; }

        public object DynamicFormValue { get; set; }

        public DynamicFormLoadData DynamicFormLoadModel { get; set; }

        public bool DynamicFormOverwrite { get; set; }

        public bool TurnOffWarning { get; set; }

        public string ContextToken { get; set; }

        public bool IsNoneCustomerTicket { get; set; }

        public DateTime? PlannedDate_Begin { get; set; }
        public DateTime? PlannedDate_End { get; set; }

        public bool NeedToCreateTaskWarning { get; set; }

        public string ibUCID { get; set; }

        public DateTime? CreatedDate { get; set; }

        public Guid? PartId { get; set; }

        public Guid? Level1Id { get; set; }
        public Guid? Level2Id { get; set; }
        public Guid? Level3Id { get; set; }
        public Guid? Level4Id { get; set; }

        public DateTime? PlannedDate { get; set; }

        public Guid? ProspectAssignmentId { get; set; }

        public Guid? ProspectId { get; set; }

        public Guid? CampaignId { get; set; }

        public RequestTicketInteraction RequestTicketInteraction { get; set; }

        public bool CreateAndFinish { get; set; }

        public CreateEditTbCallbackInfo TbCallbackData { get; set; }

        public bool IgnoreCreateFirstTask { get; set; }

        public Guid? ObjectApproveId { get; set; }

        public string ObjectApproveType { get; set; }

        public bool IsFromAPI { get; set; }
    }

    public class DynamicFormLoadData
    {
        public Guid? DynamicFormId { get; set; }

        public Guid? DynamicFormValueId { get; set; }

        public object DynamicFormValue { get; set; }

        public bool HasMapping360ViewField { get; set; }

        public Dictionary<string, string> Mapping360Definitions { get; set; }
    }

    public class InsertProductRequestTicketItem
    {
        public Guid Id { get; set; }

        public Guid RequestTicketId { get; set; }

        public Guid ProductId { get; set; }

        public Guid? FactoryId { get; set; }

        public DateTime? DueDate { get; set; }

        public Guid ServiceCategoryId { get; set; }

        public string BoughtFromStore { get; set; }

        public int? BoughtQuantity { get; set; }

        public int? LeftQuantity { get; set; }

        public int? RetrievalNumber { get; set; }

        public int AffectedQuantity { get; set; }

        public UsedStatus AffectedProductUsed { get; set; }

        public string MethodOfStorageInStore { get; set; }

        public string MethodOfStorageAtHome { get; set; }

        public string MethodOfUsingProduct { get; set; }

        public string TargetUserOfProduct { get; set; }

        public DateTime? DeliverDate { get; set; }

        public string DeliverCode { get; set; }

        public string ProductionLine { get; set; }

        public Guid? ExecutedByTaskId { get; set; }
    }

    public class CreateEditTbCallbackInfo
    {
        public Guid? Id { get; set; }

        public DateTime? CallbackDate { get; set; }

        public DateTime? NextReminderTime { get; set; }

        public string Notes { get; set; }

        public TbCallbackSatus? Status { get; set; }

        public Guid ReferenceObjectId { get; set; }

        public Guid? TbCallbackCallResultId { get; set; }

        public string CallbackCallResultNotes { get; set; }
    }

    public class ValidateRequestTicketCustomerResult
    {
        public bool IsSuccess { get; set; }

        public string ErrorMessage { get; set; }

        public CustomerData CustomerData { get; set; }

        public CustomerAlternativeAddressData CustomerAlternativeAddressData { get; set; }
    }

    public class CreateEditRequestTicketResult
    {
        public bool IsSuccess { get; set; }

        public string ErrorMessage { get; set; }

        public string SuccessMessage { get; set; }

        public List<string> WarningMessage { get; set; }

        public Guid RequestTicketId { get; set; }

        public string RequestTicketCode { get; set; }

        public string ReturnUrl { get; set; }

        public bool NeedToCreateTaskWarning { get; set; }
    }
}