
/****** Object:  StoredProcedure [telesale].[Prospect_AssignedHotProspectsTemp]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

--ALTER PROCEDURE [telesale].[Prospect_AssignedHotProspectsTemp] 'df67d415-c2a0-41bf-b9fc-c75a2ebc3b23', 'cb9c8425-ed75-9746-ea71-8faf5c376ebd', '2016-06-15', 'AC801310-589C-44C1-9551-A829B3080D3C'
CREATE PROCEDURE [telesale].[Prospect_AssignedHotProspectsTemp]

	@ImportSessionId			UNIQUEIDENTIFIER,
	@CampaignId					UNIQUEIDENTIFIER,
	@DistributedDate			DATETIME,
	@DistributedUserId			UNIQUEIDENTIFIER

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	BEGIN TRANSACTION DistributeImportedData
	
	BEGIN TRY

		-- Tạo Contact mới, nếu hệ thống chưa có
		INSERT INTO dbo.Contact
				( 
					Id,
					FullName,
					Address,
					ProvinceId,
					Phone,
					DOB,
					DataSource,
					CreatedDate,
					Status,
					Job,
					Inactive,
					Notes,
					CreatedBy,
					Income,
					MaritalStatus,
					Gender
				)
		SELECT	sc.NewContactId,
				sc.FullName,
				sc.Address,
				(SELECT TOP 1 Id FROM dbo.Province WHERE ImportCodeName LIKE N'%' + sc.Province + '%' OR sc.Province LIKE N'%' + ProvinceName + '%'),
				sc.Phone,
				sc.DOB,
				sc.Source,
				GETDATE(),
				1,
				sc.Job,
				0,
				sc.Notes,
				@DistributedUserId,
				sc.Income,
				CASE sc.Gender WHEN N'Unknown' THEN 0 WHEN N'Male' THEN 1 WHEN N'Female' THEN 2 ELSE 0 END,
				CASE sc.MaritalStatus WHEN N'Unknown' THEN 0 WHEN N'Single' THEN 1 WHEN N'Married' THEN 2 ELSE 0 END
		FROM	dbo.StagingContact sc
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.NewContactId IS NOT NULL
				AND ISNULL(sc.DataErrorMessage,'') = ''

		-- Tạo Prospect mới, nếu hệ thống chưa có
		INSERT	dbo.Prospect (Id, ContactId, CampaignId, Status, CreatedBy, CreatedDate, IsHot, CurrentAssignmentId)
		SELECT	sc.NewProspectId, sc.NewContactId, @CampaignId, 1, @DistributedUserId, GETDATE(), 1, sc.NewProspectAssignmentId
		FROM	dbo.StagingContact sc
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.NewProspectId IS NOT NULL
				AND ISNULL(sc.DataErrorMessage,'') = ''

		-- Tạo Prospect Assignment mới cho trường hợp chưa có Prospect
		INSERT	dbo.ProspectAssignment (Id, ProspectId, Status, CreatedDate, CreatedBy)
		SELECT	sc.NewProspectAssignmentId, sc.NewProspectId, 1, GETDATE(), @DistributedUserId
		FROM	dbo.StagingContact sc
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.NewProspectId IS NOT NULL
				AND ISNULL(sc.DataErrorMessage,'') = ''

		-- Tạo Prospect Assignment mới cho trường hợp có Prospect, nhưng chưa có ProspectAssignment
		INSERT	dbo.ProspectAssignment (Id, ProspectId, Status, CreatedDate, CreatedBy)
		SELECT	sc.NewProspectAssignmentId, sc.CurrentProspectId, 1, GETDATE(), @DistributedUserId
		FROM	dbo.StagingContact sc
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.NewProspectId IS NULL
				AND sc.CurrentProspectId IS NOT NULL
				AND sc.CurrentProspectAssignmentId IS NULL
				AND ISNULL(sc.DataErrorMessage,'') = ''

		UPDATE	p
		SET		CurrentAssignmentId = sc.NewProspectAssignmentId
		FROM	dbo.Prospect p
				JOIN dbo.StagingContact sc ON sc.CurrentProspectId = p.Id
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.NewProspectId IS NULL
				AND sc.CurrentProspectId IS NOT NULL
				AND sc.CurrentProspectAssignmentId IS NULL
				AND ISNULL(sc.DataErrorMessage,'') = ''

		-- Close các PA "Trong rổ sup" và "Trong rổ TMR, chưa gọi"
		UPDATE	pa
		SET		Status = 3, ClosedReason = 7, UnassignedBy = @DistributedUserId, UnassignedDate = GETDATE()
		FROM	dbo.StagingContact sc
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.CurrentProspectAssignmentId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.NeedClose=1
				AND ISNULL(sc.DataErrorMessage,'') = ''

		-- Tạo prospect Assignment mới cho các trường hợp close Prospect Assignment cũ
		--=============================================================================	
		INSERT	dbo.ProspectAssignment
			( 
				Id,
				ProspectId,
				Status,
				CreatedDate,
				CreatedBy
			)
		SELECT	sc.NewProspectAssignmentId, p.Id, 1, GETDATE(), @DistributedUserId
		FROM	dbo.Prospect p
				JOIN dbo.Contact c ON c.Id = p.ContactId
				JOIN dbo.StagingContact sc ON sc.Phone = c.Phone
		WHERE	--p.CampaignId = @CampaignId AND 
				sc.ImportSessionId = @ImportSessionId
				AND sc.NeedClose=1
				AND ISNULL(sc.DataErrorMessage,'') = ''

		UPDATE	p
		SET		CurrentAssignmentId = sc.NewProspectAssignmentId
		FROM	dbo.StagingContact sc
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.CurrentProspectAssignmentId
				JOIN dbo.Prospect p ON p.Id = pa.ProspectId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.NeedClose=1
				AND ISNULL(sc.DataErrorMessage,'') = ''

		-- Update Ưu tiên cho tất cả các Case được phân bổ
		UPDATE	pa
		SET		Ignore15DaysRule = 1
		FROM	dbo.StagingContact sc 
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND ISNULL(sc.DataErrorMessage,'') = ''
			
		-- Update IsHot cho tất cả các Case được phân bổ
		-- 90 ngày, set null ReprospectDate
		UPDATE	p
		SET		IsHot=1, p.ReprospectDate=NULL, p.Status=1
		FROM	dbo.StagingContact sc 
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
				JOIN dbo.Prospect p ON p.Id=pa.ProspectId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND ISNULL(sc.DataErrorMessage,'') = ''
				AND ISNULL(sc.DuplicatedCase,0) NOT IN (1, 5, 7)

		-- Không tiềm năng, update lại status contact
		--=============================================================================
		UPDATE	c
		SET		Status=1
		FROM	dbo.StagingContact sc 
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
				JOIN dbo.Prospect p ON p.Id=pa.ProspectId
				JOIN dbo.Contact c ON c.Id=p.ContactId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND ISNULL(sc.DataErrorMessage,'') = ''
				AND ISNULL(sc.DuplicatedCase,0) NOT IN (1, 5, 7)

		-- Update AssignedTMRId cho StagingContact
		UPDATE	sc
		SET		AssignedTMRId = up.Id
		FROM	dbo.StagingContact sc
				JOIN dbo.UserProfiles up ON up.AgentCode = sc.Job
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.ToBeHotDistributePaId IS NOT NULL
				AND ISNULL(sc.DuplicatedCase, 0) NOT IN (1, 5, 7)

		-- Update assigned cho ProspectAssignment
		UPDATE	pa
		SET		AssignedTeamId = up.OrganizationId,
				AssignedTeamDate = GETDATE(),
				AssignedAgentId = up.Id,
				AssignedAgentDate = GETDATE(),
				Ignore15DaysRule = 1
		FROM	dbo.StagingContact sc
				JOIN dbo.UserProfiles up ON up.AgentCode = sc.Job
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
		WHERE	sc.ImportSessionId = @ImportSessionId

		-- Update Prospect
		UPDATE	p
		SET		IsHot = 1, ReprospectDate = NULL
		FROM	dbo.StagingContact sc
				JOIN dbo.UserProfiles up ON up.AgentCode = sc.Job
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
				JOIN dbo.Prospect p ON p.Id = pa.ProspectId
		WHERE	sc.ImportSessionId = @ImportSessionId

	END TRY
    BEGIN CATCH
		ROLLBACK TRANSACTION DistributeImportedData;
		THROW
    END CATCH

	COMMIT TRANSACTION DistributeImportedData;
******/
END
GO