
/****** Object:  StoredProcedure [telesale].[SearchContactBySaleSupportAndDistributeAgent]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
  

CREATE PROCEDURE [telesale].[SearchContactBySaleSupportAndDistributeAgent]
	
	----- Search Model
	@CampaignId UNIQUEIDENTIFIER,
	
	@Status INT,
	@IsExistCallStatus INT = 0,
	@IncomeFrom INT=0,
	@IncomeTo INT=0,

	@PreviousAssignedTeamId UNIQUEIDENTIFIER =null,
	@PreviousAssignedAgentId UNIQUEIDENTIFIER =null,
	@PreviousPaCallResultId UNIQUEIDENTIFIER =null,
	@ImportSessionId UNIQUEIDENTIFIER,

	@LeadStatus INT = 0,

	@DataSource NVARCHAR(200) =null,
	@PhoneNumber VARCHAR(100)  = null,
	@EnterCampaignBasketDate DATETIME = NULL,
	@EnterCampaignBasketDateTo DATETIME = NULL,

	@ProvinceList IdList READONLY,
	
	-- Fixed dataset by individual selection
	@CustomerSelectedList IdList READONLY,

	----- Distribute Quantity Plan : quantity for each receiver
	@DistributionPlan CoupleIdNumber READONLY,

	-- Distribute Quality Plan? None, Income, DataQuality 
	@DataQualityBy NVARCHAR(MAX),

	---------Distribution Target : Team, TMR, Hot
	@Option NVARCHAR(MAX)   
AS
BEGIN
    SET NOCOUNT ON;

	DECLARE @Dresult DistributeData;
	DECLARE @Ddata DistributeData;
	DECLARE @Dplan DistributePlan;

	DECLARE @PageSize INT
    DECLARE @ContactList TABLE
        (
			rn INT,
			ContactId UNIQUEIDENTIFIER,
			ProspectAssignmentId UNIQUEIDENTIFIER
        );

	-- Decide the number of rows to be distribute
	SELECT  @PageSize = SUM(ISNULL(Number, 0))	FROM  @DistributionPlan;

	-- Get list of records to be distribute into @ContactList
    IF ( SELECT COUNT(*) FROM   @CustomerSelectedList) > 0
	BEGIN
		INSERT  INTO @ContactList
        SELECT  ROW_NUMBER() OVER ( ORDER BY (SELECT 1) ) rn ,
				c.Id ContactId,
                pa.Id ProspectAssignmentId
        FROM    @CustomerSelectedList s
		JOIN dbo.ProspectAssignment pa ON pa.Id=s.Id
		JOIN dbo.Prospect p ON pa.ProspectId=p.Id
		JOIN dbo.Customer c ON p.CustomerId=c.Id --AND c.Deleted = 0;
		DELETE @ContactList WHERE rn > @PageSize
	END
    ELSE
        INSERT  INTO @ContactList
		EXEC dbo.SearchToDistributeBySaleSupport
				@CampaignId = @CampaignId, -- uniqueidentifier
				@Status = @Status, -- int
				@IsExistCallStatus = @IsExistCallStatus, -- int
				@IncomeFrom = @IncomeFrom, -- int
				@IncomeTo = @IncomeTo, -- int
				@PreviousAssignedTeamId = @PreviousAssignedTeamId, -- uniqueidentifier
				@PreviousAssignedAgentId = @PreviousAssignedAgentId, -- uniqueidentifier
				@PreviousPaCallResultId = @PreviousPaCallResultId, -- uniqueidentifier
				@ImportSessionId = @ImportSessionId, -- uniqueidentifier
				@DataSource = @DataSource, -- nvarchar(200)
				@PhoneNumber = @PhoneNumber, -- varchar(100)
				@EnterCampaignBasketDate = @EnterCampaignBasketDate, -- datetime
				@EnterCampaignBasketDateTo = @EnterCampaignBasketDateTo, -- datetime
				@ProvinceList = @ProvinceList, -- IdList
				@PageIndex = 0, -- int
				@PageSize = @PageSize, -- int
				@ReturnBriefResultToDistribute = 1, -- bit
				@HasEmail = NULL,
				@ExportExcel = 0;

	IF @Option='Team'
	BEGIN --phân bổ team
        BEGIN TRY
            BEGIN TRANSACTION
			DELETE @Dresult;
			DELETE @Ddata;
			DELETE @Dplan;
			INSERT INTO @Dplan
					( AgentId ,
					  ExpectedNumber
					)
			SELECT Id1 AgentId, Number ExpectedNumber FROM @DistributionPlan
			WHERE Number>0
			INSERT INTO @Ddata
					( ID ,
					  Quality
					)
			SELECT ProspectAssignmentId ID, IIF(@DataQualityBy='Income', ISNULL(c.Income, 0), ISNULL(c.DataQuality, 0)) Quality
			FROM @ContactList r
			JOIN dbo.Customer c ON r.ContactId=c.Id --AND c.Deleted = 0

			INSERT INTO @Dresult
			EXEC dbo.DistributeDataToAgent @Ddata=@Ddata, @Dplan=@Dplan
			UPDATE pa SET pa.AssignedTeamId=d.AssignedTo, pa.AssignedTeamDate=GETDATE(), pa.Status=1, pa.CreatedReason=1, pa.Ignore15DaysRule=0 FROM @Dresult d
			JOIN dbo.ProspectAssignment pa ON d.ID=pa.Id
			WHERE d.AssignedTo IS NOT NULL
			UPDATE p SET p.Status=2, p.BackToCommonBasketDate=NULL, p.ReprospectDate=NULL FROM @Dresult d
			JOIN dbo.ProspectAssignment pa ON d.ID=pa.Id
			JOIN dbo.Prospect p ON pa.ProspectId=p.Id
			WHERE d.AssignedTo IS NOT NULL
			DELETE c FROM @ContactList c
			JOIN @Dresult d ON c.ProspectAssignmentId=d.ID
			WHERE d.AssignedTo IS NULL
            COMMIT
        END TRY
        BEGIN CATCH
            ROLLBACK
        END CATCH   
	END
	ELSE IF @Option='TMR'
	BEGIN -- phân bổ agent
		IF @DataQualityBy = 'None'
		BEGIN
			BEGIN TRY
				BEGIN TRANSACTION
				UPDATE  pa
				SET     pa.AssignedTeamId = t.OrganizationId ,
						pa.AssignedTeamDate = GETDATE() ,
						pa.AssignedAgentId = t.Id ,
						pa.AssignedAgentDate = GETDATE() ,
						pa.Status = 1 ,
						pa.CreatedReason = 1 ,
						pa.Ignore15DaysRule = 0  
				FROM    ( SELECT    up.Id, up.OrganizationId, a.Number ,
									SUM(Number) OVER ( ORDER BY Id1 ) RunningTotal   --** Calculate index row to assign for each team **
							FROM      @DistributionPlan a
							JOIN dbo.UserProfiles up ON a.Id1=up.Id
							WHERE     Number > 0
						) t
						JOIN @ContactList c ON c.rn > ( t.RunningTotal - t.Number )
												AND c.rn <= t.RunningTotal				--** Calculate index row to assign for each team **
						JOIN dbo.ProspectAssignment pa ON pa.Id = c.ProspectAssignmentId
		
				UPDATE  p
				SET     p.Status = 2 ,
						p.BackToCommonBasketDate = NULL ,
						p.ReprospectDate = NULL
				FROM    dbo.ProspectAssignment pa
						JOIN @ContactList tmp ON tmp.ProspectAssignmentId = pa.Id
						JOIN dbo.Prospect p ON p.Id = pa.ProspectId
				COMMIT
			END TRY
			BEGIN CATCH
				ROLLBACK
			END CATCH  
		END
		ELSE
		BEGIN
			BEGIN TRY
				BEGIN TRANSACTION
					DELETE @Dresult;
					DELETE @Ddata;
					DELETE @Dplan;
					INSERT INTO @Dplan
							( AgentId ,
							  ExpectedNumber
							)
					SELECT Id1 AgentId, Number ExpectedNumber FROM @DistributionPlan
					WHERE Number > 0
					INSERT INTO @Ddata
							( ID ,
							  Quality
							)
					SELECT ProspectAssignmentId ID, IIF(@DataQualityBy='Income', ISNULL(c.Income, 0), ISNULL(c.DataQuality, 0)) Quality
					FROM @ContactList r
					JOIN dbo.Customer c ON r.ContactId=c.Id --AND c.Deleted = 0

					INSERT INTO @Dresult
					EXEC dbo.DistributeDataToAgent @Ddata=@Ddata, @Dplan=@Dplan
					UPDATE pa SET pa.AssignedAgentId=d.AssignedTo, pa.AssignedAgentDate=GETDATE(), pa.AssignedTeamId=up.OrganizationId, pa.AssignedTeamDate=GETDATE(), pa.Status=1, pa.CreatedReason=1 FROM @Dresult d
					JOIN dbo.ProspectAssignment pa ON d.ID=pa.Id
					JOIN dbo.UserProfiles up ON d.AssignedTo=up.Id
					WHERE d.AssignedTo IS NOT NULL
					UPDATE p SET p.Status=2, p.BackToCommonBasketDate=NULL, p.ReprospectDate=NULL FROM @Dresult d
					JOIN dbo.ProspectAssignment pa ON d.ID=pa.Id
					JOIN dbo.Prospect p ON pa.ProspectId=p.Id
					WHERE d.AssignedTo IS NOT NULL
					DELETE c FROM @ContactList c
					JOIN @Dresult d ON c.ProspectAssignmentId=d.ID
					WHERE d.AssignedTo IS NULL
				COMMIT
			END TRY
			BEGIN CATCH
				ROLLBACK
			END CATCH
		END
	END
	ELSE IF @Option='Hot'
	BEGIN
       BEGIN TRY
        BEGIN TRANSACTION
			-- TODO1: Move transaction over search to make search data records are locked between search and distribute
			-- TODO2: throw exception to warn user if exception happen
        UPDATE  pa
        SET     pa.AssignedTeamId = up.OrganizationId ,
                pa.AssignedTeamDate = GETDATE() ,
                pa.AssignedAgentId = up.Id ,
                pa.AssignedAgentDate = GETDATE() ,
                pa.Status = 1 ,
                pa.CreatedReason = 1 ,
                pa.Ignore15DaysRule = 1
				--  SELECT rn,c.ProspectAssignmentId, t.RunningTotal - t.Number StartIndex, t.RunningTotal EndIndex
				--, t.Id1 AssignedAgentId, t.Number
        FROM    ( SELECT    * ,
                            SUM(Number) OVER ( ORDER BY Id1 ) RunningTotal   --** Calculate index row to assign for each team **
                  FROM      @DistributionPlan
                  WHERE     Number > 0
                ) t
                JOIN @ContactList c ON c.rn > ( t.RunningTotal - t.Number )
                                       AND c.rn <= t.RunningTotal				--** Calculate index row to assign for each team **
                JOIN dbo.UserProfiles up ON up.Id = t.Id1
                JOIN dbo.ProspectAssignment pa ON pa.Id = c.ProspectAssignmentId
					
        UPDATE  p
        SET     p.Status = 2 ,
                p.BackToCommonBasketDate = NULL ,
                p.ReprospectDate = NULL
        FROM    @ContactList tmp
                JOIN dbo.ProspectAssignment pa ON tmp.ProspectAssignmentId = pa.Id
                JOIN dbo.Prospect p ON p.Id = pa.ProspectId
        COMMIT
       END TRY
       BEGIN CATCH
        ROLLBACK
       END CATCH  
	 
	END
	SELECT COUNT(*) TotalAssigned FROM @ContactList
END
GO