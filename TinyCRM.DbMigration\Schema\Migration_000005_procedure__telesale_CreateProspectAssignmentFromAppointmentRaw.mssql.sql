
/****** Object:  StoredProcedure [telesale].[CreateProspectAssignmentFromAppointmentRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[CreateProspectAssignmentFromAppointmentRaw]
	@UserId UNIQUEIDENTIFIER,
	@SessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;

	UPDATE dbo.AppointmentRaw
    SET CodeDMO =  IIF(LTRIM(RTRIM(CodeDMO))='',NULL,LTRIM(RTRIM(CodeDMO))),
         CodeTMR =  IIF(LTRIM(RTRIM(CodeTMR))='',NULL,LTRIM(RTRIM(CodeTMR)))
    WHERE ImportSessionId=@SessionId

	INSERT INTO dbo.ProspectAssignment
	        ( Id ,
	          ProspectId ,
	          AssignedTeamId ,
	          AssignedTeamDate ,
	          AssignedAgentId ,
	          AssignedAgentDate ,
	          UnassignedDate ,
	          UnassignedBy ,
	          [Status] ,
	          CreatedDate ,
	          CreatedBy ,
	          ClosedReason ,
	          FieldSaleAppointmentId ,
	          FieldSaleFeedbackDate ,
	          ReminderId,
			  CreatedReason
	        )
	SELECT
			  ar.ProspectAssignmentId Id,
			  ar.ProspectId,
			  tmr.OrganizationId AssignedTeamId,
			  GETDATE() AssignedTeamDate,
			  tmr.Id AssignedAgentId,
			  GETDATE() AssignedAgentDate,
			  NULL UnassignedDate,
			  NULL UnassignedBy,
			  2 [Status],
			  GETDATE() CreatedDate,
			  @UserId CreatedBy,
			  NULL ClosedReason,
			  dmo.Id FieldSaleAppointmentId,
			  NULL FieldSaleFeedbackDate,
			  NULL ReminderId,
			  IIF(ar.IsDupPa=1, 5, 1) CreatedReason
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles tmr ON ar.CodeTMR=tmr.AgentCode
	LEFT JOIN dbo.UserProfiles dmo ON ar.CodeDMO=dmo.AgentCode
	--JOIN dbo.Organization tmr_team ON tmr.OrganizationId=tmr_team.Id
	WHERE ar.IsInvalid <> 1 AND ar.ImportSessionId=@SessionId
	AND (NOT (ar.IsDupPa=1 AND ar.IsDupApp=1))
END
GO