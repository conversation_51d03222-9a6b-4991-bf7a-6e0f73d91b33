﻿using System;
using System.Linq;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DynamicForm.Queries
    {
    public class GetDynamicFormByIdQuery : QueryBase<DynamicFormData>
    {
        public Guid? Id { get; set; }
    }

    internal class GetDynamicFormByIdQueryHandler : QueryHandlerBase<GetDynamicFormByIdQuery, DynamicFormData>
    {
        public GetDynamicFormByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DynamicFormData>> ExecuteAsync(GetDynamicFormByIdQuery query)
    {
            var mainQuery = await EntitySet.GetAsync<DynamicFormEntity>();
            if (query.Id.HasValue)
    {
                mainQuery = mainQuery.Where(x => x.Id == query.Id);
            }
            return QueryResult.Create(mainQuery, x => Mapper.Map<DynamicFormData>(x));
        }
    }
}

