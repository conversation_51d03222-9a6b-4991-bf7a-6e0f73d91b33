
/****** Object:  StoredProcedure [telesale].[CreateLeadAssignmentFromAppointmentRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[CreateLeadAssignmentFromAppointmentRaw]
	@UserId UNIQUEIDENTIFIER,
	@SessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	INSERT INTO dbo.LeadAssignment
	        ( Id ,
	          LeadId ,
	          [SuggestedFieldSaleId] ,
	          AssignedFieldSaleDate ,
	          [SuggestedFieldSaleTeamId] ,
	          AssignedFieldSaleTeamDate ,
	          CreatedDate ,
	          CreatedBy ,
	          [Status] ,
	          WaitingAppointmentId,
			  [SuggestedFieldSaleReason]
	        )
	SELECT
			  ar.LeadAssignmentId Id,
			  ar.LeadId,
			  dmo.Id [SuggestedFieldSaleId],
			  NULL AssignedFieldSaleDate,
			  dmo.OrganizationId [SuggestedFieldSaleTeamId],
			  NULL AssignedFieldSaleTeamDate,
			  GETDATE() CreatedDate,
			  @UserId CreatedBy,
			  1 [Status],
			  ar.AppointmentId WaitingAppointmentId,
			  IIF(dmo.Id IS NOT NULL, 6, NULL) [SuggestedFieldSaleReason]
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles dmo ON ar.CodeDMO=dmo.AgentCode
	WHERE ar.IsInvalid <> 1 AND ar.ImportSessionId=@SessionId
	AND (NOT (ar.IsDupPa=1 AND ar.IsDupApp=1))
END
GO