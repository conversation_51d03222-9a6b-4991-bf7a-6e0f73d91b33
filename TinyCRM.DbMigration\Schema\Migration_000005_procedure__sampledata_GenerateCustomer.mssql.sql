
/****** Object:  StoredProcedure [sampledata].[<PERSON>rateCust<PERSON>]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROC [sampledata].[GenerateCustomer] 	
	@NumOfCustomer INT = 100000, 
	@CoreCustomerPercentage INT = 80
as
BEGIN
SET NOCOUNT ON

-- Tối đa chỉ gen được là 4 triệu
IF (@NumOfCustomer>4000000) SET @NumOfCustomer=4000000


IF OBJECT_ID('SAMPLE_NAME') IS NOT NULL DROP TABLE SAMPLE_NAME
IF OBJECT_ID('SAMPLE_CITY_STATE_ZIP') IS NOT NULL DROP TABLE SAMPLE_CITY_STATE_ZIP
IF OBJECT_ID('SAMPLE_STREET') IS NOT NULL DROP TABLE SAMPLE_STREET
IF OBJECT_ID('SAMPLE_CUSTOMER') IS NOT NULL DROP TABLE SAMPLE_CUSTOMER

-- 1-Create list tables
CREATE TABLE SAMPLE_NAME 
(
	id INT IDENTITY(1, 1) PRIMARY KEY,
	first_name VARCHAR(50),
	mid_name VARCHAR(50),
	last_name VARCHAR(50)
)
CREATE TABLE SAMPLE_CITY_STATE_ZIP 
(
	id INT IDENTITY(1, 1) PRIMARY KEY,
	city VARCHAR(50),
	state_code CHAR(2),
	zip_code VARCHAR(5)
)
CREATE TABLE SAMPLE_STREET (id INT IDENTITY(1, 1) PRIMARY KEY, addr1 VARCHAR(100), addr2 VARCHAR(100))
CREATE TABLE SAMPLE_CUSTOMER (stt VARCHAR(25),randNumber BIGINT)


INSERT INTO SAMPLE_NAME VALUES('Raven', 'Salvador', 'Welch'), ('Jaime', 'Francesca', 'Madden'), ('Declan', 'Althea', 'Thomas'), ('Acton', 'Eleanor', 'Guthrie'), ('Shaeleigh', 'Nadine', 'Delaney'), ('Paula', 'Nora', 'Mcdowell'), ('Byron', 'Edward', 'Maldonado'), ('Josiah', 'Miranda', 'Best'), ('Thane', 'Nissim', 'Aguirre'), ('Nolan', 'Iola', 'Herring'), ('Emerald', 'Urielle', 'Chen'), ('Mohammad', 'Mallory', 'Stanton'), ('Alexis', 'Flynn', 'Farmer'), ('Haviva', 'Piper', 'Chase'), ('Willow', 'Yolanda', 'Osborn'), ('Bruno', 'Erica', 'Bryant'), ('Aimee', 'Zachary', 'Martinez'), ('Rudyard', 'Colt', 'Mcfarland'), ('Joel', 'Desiree', 'Rosario'), ('Jaden', 'Price', 'Savage'), ('Kenneth', 'Shay', 'Grant'), ('Jacqueline', 'Maia', 'Chambers'), ('Leslie', 'Grace', 'Gilbert'), ('Brenna', 'Jacqueline', 'House'), ('Ashton', 'Kyle', 'Hooper'), ('Raja', 'Autumn', 'Pruitt'), ('Kaye', 'Susan', 'Fischer'), ('Maxwell', 'Tyler', 'Oneil'), ('Merritt', 'Portia', 'Vinson'), ('Claudia', 'Lucas', 'Vang'), ('Galvin', 'Lydia', 'Hall'), ('Iona', 'Andrew', 'Perez'), ('Damian', 'Melanie', 'Beck'), ('Liberty', 'Hayley', 'Benton'), ('Clayton', 'William', 'Hurst'), ('Paul', 'Ignatius', 'Byrd'), ('Kennan', 'Megan', 'Barron'), ('Michelle', 'Jenna', 'Walters'), ('Walter', 'Quintessa', 'Roth'), ('Penelope', 'Clarke', 'Ewing'), ('Nicholas', 'Jaime', 'Marquez'), ('Quinn', 'Serina', 'Snyder'), ('Benjamin', 'Felicia', 'Goff'), ('Geoffrey', 'Pamela', 'Valenzuela'), ('Rafael', 'Erasmus', 'Watts'), ('William', 'Kenyon', 'Knapp'), ('Leroy', 'Melissa', 'Cross'), ('Amal', 'Kessie', 'Harmon'), ('Sandra', 'Raven', 'Mclean'), ('Ruby', 'Igor', 'Neal'), ('John', 'Madaline', 'Watkins'), ('Keaton', 'Noble', 'Hartman'), ('Upton', 'Shannon', 'Baxter'), ('Katelyn', 'Maite', 'Leach'), ('Macey', 'Hiram', 'Anthony'), ('Isadora', 'Slade', 'Valentine'), ('Lawrence', 'Fulton', 'Maxwell'), ('Marah', 'Aidan', 'Gould'), ('Keefe', 'Brianna', 'Poole'), ('Brock', 'Clark', 'Vance'), ('Vincent', 'Connor', 'Richard'), ('Gloria', 'Malachi', 'Webster'), ('Dominic', 'Xander', 'Odonnell'), ('Uta', 'Ishmael', 'Odom'), ('Alan', 'Candice', 'Carney'), ('Dai', 'Danielle', 'Pate'), ('Medge', 'Kieran', 'Crawford'), ('Dakota', 'Armando', 'Houston'), ('Ethan', 'Keely', 'Whitley'), ('Cheryl', 'Deanna', 'Fox'), ('Tasha', 'Lewis', 'Barker'), ('Wyoming', 'Chantale', 'Cameron'), ('Libby', 'Thaddeus', 'Reeves'), ('Clementine', 'Sharon', 'Mccoy'), ('Charles', 'Hanna', 'Moran'), ('Lara', 'Levi', 'Chapman'), ('Jamal', 'Donna', 'Riggs'), ('Clio', 'Wilma', 'Hudson'), ('Sean', 'Lesley', 'Holder'), ('Carlos', 'Zephr', 'Henson'), ('Chanda', 'Charde', 'Daugherty'), ('Price', 'Stephanie', 'Lawson'), ('Jasper', 'Fletcher', 'Schroeder'), ('Clare', 'Brett', 'Reid'), ('Gregory', 'Quamar', 'Obrien'), ('Riley', 'Lucius', 'Oliver'), ('Brooke', 'Malcolm', 'Witt'), ('Beverly', 'Hyatt', 'Mueller'), ('Arthur', 'Alea', 'Gilliam'), ('Ezra', 'Dalton', 'Wong'), ('Luke', 'Gregory', 'Mckinney'), ('Ivana', 'Xanthus', 'Mooney'), ('Adrian', 'Barbara', 'Huber'), ('Driscoll', 'Devin', 'Joyner'), ('Aline', 'Octavius', 'Henry'), ('Lamar', 'Ross', 'Battle'), ('Magee', 'Middleton', 'Bender'), ('Amaya', 'Chloe', 'Spence'), ('Philip', 'Clementine', 'Baird'), ('Kieran', 'Ann', 'Rollins')
-- CITY-STATE-ZIP
INSERT INTO SAMPLE_CITY_STATE_ZIP(city, state_code, zip_code) VALUES('Charleston', 'KS', '73262'), ('Palmdale', 'MS', '06568'), ('Phoenix', 'AZ', '68145'), ('Stillwater', 'TN', '92940'), ('Fairmont', 'OK', '31302'), ('Bell', 'NM', '39446'), ('Everett', 'OH', '59808'), ('Baltimore', 'WI', '20774'), ('Waterbury', 'DE', '20913'), ('Wheeling', 'NH', '85771'), ('New Bedford', 'NE', '61863'), ('The Dalles', 'MI', '33755'), ('Signal Hill', 'NE', '52010'), ('New Iberia', 'TN', '43899'), ('Anderson', 'SC', '60504'), ('Sunnyvale', 'SC', '65080'), ('Dodge City', 'VT', '50124'), ('Las Vegas', 'CT', '12059'), ('Chula Vista', 'CT', '07633'), ('Corvallis', 'MA', '60495'), ('Ketchikan', 'CA', '59300'), ('Milford', 'CO', '27397'), ('Bloomington', 'NV', '75502'), ('New Haven', 'MI', '82988'), ('Slidell', 'WY', '79522'), ('Eden Prairie', 'NJ', '67114'), ('Port Arthur', 'ND', '39148'), ('Huntington', 'MD', '28115'), ('Bozeman', 'NC', '93590'), ('Dover', 'AZ', '78271'), ('Coos Bay', 'TN', '20742'), ('Modesto', 'NM', '70909'), ('Florence', 'NH', '89556'), ('Hartford', 'SC', '92104'), ('Cary', 'RI', '35742'), ('San Dimas', 'MA', '79507'), ('Lafayette', 'NJ', '90540'), ('Rancho Palos Verdes', 'NM', '78112'), ('Oxnard', 'VA', '61248'), ('Cranston', 'WY', '45254'), ('Greenfield', 'CO', '85503'), ('Kingsport', 'DE', '84470'), ('Minot', 'AZ', '24329'), ('Torrington', 'MS', '86325'), ('West Lafayette', 'SD', '26895'), ('Cranston', 'ME', '93835'), ('Fort Worth', 'PA', '62701'), ('Eden Prairie', 'MD', '31464'), ('Clarksville', 'MS', '67997'), ('Gainesville', 'SC', '18735'), ('Alamogordo', 'DE', '50453'), ('Orangeburg', 'AK', '25592'), ('Georgetown', 'VT', '20211'), ('Brooklyn Park', 'TX', '86292'), ('Eureka', 'MI', '18720'), ('Dover', 'RI', '08610'), ('Pasco', 'MD', '59218'), ('Warner Robins', 'MN', '72933'), ('Saint Cloud', 'CA', '33774'), ('Manhattan Beach', 'MN', '10695'), ('Norton', 'ID', '11239'), ('Madison', 'KS', '03487'), ('Somerville', 'NM', '19961'), ('Woodward', 'LA', '55347'), ('Laguna Woods', 'PA', '21712'), ('Bossier City', 'NY', '47379'), ('Florence', 'OR', '79429'), ('Saint Joseph', 'NE', '01144'), ('Fernley', 'CO', '12707'), ('Leominster', 'KY', '62009'), ('West Hollywood', 'MT', '27357'), ('Lodi', 'ND', '92554'), ('Salisbury', 'IL', '77914'), ('Dana Point', 'DC', '10303'), ('Beaver Falls', 'LA', '85985'), ('Clairton', 'LA', '13060'), ('Stamford', 'NY', '84795'), ('Savannah', 'KY', '49927'), ('Maywood', 'DE', '39190'), ('Kingston', 'WY', '83712'), ('Columbia', 'WV', '75289'), ('Cortland', 'UT', '43546'), ('Marshall', 'NM', '05363'), ('Providence', 'ME', '55122'), ('Paducah', 'NH', '70774'), ('Boulder Junction', 'KY', '85256'), ('Sioux City', 'UT', '56751'), ('Fontana', 'NC', '30798'), ('Sandpoint', 'OR', '89257'), ('Santa Cruz', 'UT', '71797'), ('Georgetown', 'OR', '09062'), ('Beloit', 'KY', '19448'), ('Huntsville', 'NC', '42499'), ('Oklahoma City', 'NM', '96646'), ('New Rochelle', 'MO', '55047'), ('Bangor', 'WV', '38252'), ('Lakewood', 'AZ', '78217'), ('Tupelo', 'SD', '47732'), ('Hanahan', 'MA', '47230'), ('Biddeford', 'NV', '07544'), ('Green Bay', 'PA', '63327'), ('Auburn Hills', 'WI', '35049'), ('Seal Beach', 'OK', '88519'), ('Elkhart', 'OK', '01218'), ('Morgan City', 'MO', '59483'), ('Akron', 'WI', '75373'), ('Corona', 'IL', '52522'), ('Oneonta', 'MI', '09445'), ('Lawrence', 'VT', '93691'), ('Cedarburg', 'HI', '41956'), ('Council Bluffs', 'AZ', '73913'), ('Waco', 'ND', '16805'), ('Citrus Heights', 'NY', '77121'), ('Farmer City', 'HI', '47894'), ('Butte', 'VA', '83224'), ('Uniontown', 'CA', '21958'), ('Oro Valley', 'PA', '03498'), ('Chico', 'SC', '96245'), ('Manassas', 'NY', '58401'), ('Murrieta', 'AL', '64645'), ('Ada', 'VA', '03562'), ('Delta Junction', 'VT', '42548'), ('Hoboken', 'VT', '49295'), ('Rockford', 'NM', '69840'), ('Chester', 'WY', '25090'), ('New Prague', 'MN', '83138'), ('Alpharetta', 'NC', '44670'), ('Nevada City', 'NE', '74316'), ('Wahoo', 'DC', '50379'), ('Oxford', 'TX', '89243'), ('Pawtucket', 'CO', '76133'), ('Moline', 'OK', '66851'), ('Walla Walla', 'WA', '67105'), ('Beloit', 'ME', '01578'), ('Chattanooga', 'VA', '49976'), ('Manitowoc', 'NE', '41527'), ('Mackinac Island', 'LA', '42983'), ('Berlin', 'MA', '50205'), ('Decatur', 'WI', '57012'), ('Des Moines', 'OR', '98882'), ('Caguas', 'RI', '85581'), ('Miami Gardens', 'AK', '21216'), ('Attleboro', 'HI', '49757'), ('Macon', 'KS', '42990'), ('Macomb', 'MI', '09802'), ('Bell Gardens', 'KY', '36660'), ('East Hartford', 'KY', '88363'), ('Marlborough', 'NJ', '16576'), ('Lancaster', 'NC', '71155'), ('Sandpoint', 'DE', '44423'), ('Fresno', 'NV', '05877'), ('Mesquite', 'GA', '61048'), ('Camden', 'NJ', '35729'), ('Fallon', 'OR', '07668'), ('Dickinson', 'NV', '17434'), ('Moorhead', 'ME', '55819'), ('Joplin', 'WV', '29689'), ('Highland Park', 'MT', '72863'), ('Niagara Falls', 'CO', '60015'), ('Melrose', 'AZ', '46688'), ('Minot', 'MO', '82110'), ('Hermitage', 'WY', '68375'), ('Keene', 'PA', '37772'), ('Hoboken', 'MD', '96054'), ('Georgetown', 'OH', '06072'), ('Washington', 'DC', '67060'), ('Sioux City', 'KY', '71101'), ('San Gabriel', 'AK', '43974'), ('Louisville', 'TN', '73619'), ('Hollywood', 'GA', '09116'), ('Hartford', 'IA', '07231'), ('Muskegon', 'IL', '77254'), ('Norman', 'GA', '57298'), ('Chandler', 'FL', '52503'), ('El Segundo', 'KS', '99397'), ('Beverly', 'GA', '84140'), ('Temple City', 'ND', '82667'), ('Washington', 'DC', '41881'), ('Statesboro', 'FL', '22881'), ('Gold Beach', 'WA', '82450'), ('Peabody', 'NC', '09323'), ('Mechanicville', 'DE', '03766'), ('Wisconsin Rapids', 'MO', '83621'), ('Westminster', 'OH', '38889'), ('Portsmouth', 'GA', '73243'), ('Nanticoke', 'CO', '04712'), ('Bowie', 'VT', '81095'), ('Tallahassee', 'OR', '46953'), ('Durham', 'OR', '38459'), ('Vicksburg', 'IA', '30669'), ('Tok', 'OH', '55830'), ('Madison', 'KS', '52616'), ('Mobile', 'NM', '54778'), ('Sandy', 'MI', '32588'), ('Charleston', 'OH', '25711'), ('Yigo', 'SD', '38794'), ('Eugene', 'NC', '46719'), ('Areceibo', 'PA', '94106'), ('Pasadena', 'SD', '86049'), ('Tuscaloosa', 'GA', '45460')
-- STREET
INSERT INTO SAMPLE_STREET(addr1, addr2) VALUES('Shasta Way', '109'), ('Park Ln', '3'), ('Michigan Avenue', '3'), ('Ann St', 'A333'), ('Bailey St', 'Apt 6a'), ('Collingwood Dr', 'Apt 4B'), ('W Circle Dr', '#205'), ('Grand River Avenue', '#231'), ('Charles St', 'Apt 3B'), ('Beech St', '#109'), ('Burcham Dr', '112'), ('Albert Ave', '120'), ('Elisabeth St', '309'), ('Dormitory Rd', 'N316'), ('Snyder Road', 'A105'), ('Kedzie St', '332'), ('Orchard St', ''), ('River St', ''), ('Cedar St', 'Apt #1C'), ('Rogue St', '121'), ('Truscott St.', '32'), ('Trowbridge Rd', '99'), ('Wilson Road', ''), ('Shaw Lane', '109'), ('P.O. Box', '119'), ('P.O. Box', '10'), ('P.O. Box 635', '150'), ('PO Box', ''), ('Red Cedar Road', ''), ('Hassinger Road', ''), ('Century Oaks Way', '34'), ('Century Meadow Ct', '44'), ('Pebbletree Way', ''), ('Northgate Dr', '2C'), ('Crosslees Dr', ''), ('South Hwy 101', '108'), ('West County Road D', ''), ('William St', ''), ('Water St', ''), ('Trinity Place', ''), ('Paseo De Peralta', '33'), ('W Buena Vista St', ''), ('Canyon Road', ''), ('Mountain View Lane', '6D'), ('Forest View Rd', '#203'), ('Coronado Road', '4a'), ('Old Santa Fe Trail', '5A'), ('Linda Vista Rd', '145'), ('Regents Park', 'C205'), ('Thousand Oaks Dr', 'C105'), ('Spyglass Plaza', '217'), ('Rae Dell Ave', '155'), ('Annie St. W', ''), ('Union St', '34'), ('Hammond St', ''), ('Silver Road', '155'), ('Webster Ave', '155'), ('Buck St', '155'), ('Stillwater Ave', ''), ('Mt Hope Ave', 'A558'), ('State St', '155'), ('N Main St', ''), ('Essex St', 'A108'), ('Yale St', 'A256'), ('Harvard St', 'Apt 501c'), ('Valley View Road', '155'), ('Anderson Lakes Parkway', '33'), ('Bloomington Ferry Road', '23'), ('Watchmaker St', '52'), ('Kirkwood Ave', '21'), ('Wedgewood Ave', '33'), ('Franklin Place', '121'), ('Thompson Ln', '22'), ('Pioneer Trail', '280'), ('France Ave', 'Apt 6D'), ('Broadland Cove', 'B123'), ('Woodland Valley', '112'), ('Whitefish Way', '107'), ('Eisenhower Road', '105'), ('Raymond Heights Road', '101')


DECLARE @countStreet INT, @countCity INT
SELECT @countStreet = COUNT(*) FROM dbo.SAMPLE_STREET
SELECT @countCity = COUNT(*) FROM dbo.SAMPLE_CITY_STATE_ZIP


-- variables
DECLARE @nubmerOfCoreCustomer INT
		,@aBatchNumber INT
		,@runningBatchIndex INT

IF (@NumOfCustomer<5000) SET @aBatchNumber = 1000
ELSE SET @aBatchNumber=5000

SET @runningBatchIndex=0
SET @nubmerOfCoreCustomer = @aBatchNumber * @CoreCustomerPercentage / 100;
;
WITH e1(n) AS (SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               ), -- 10
e2(n) AS (SELECT 1 FROM e1 CROSS JOIN e1 AS b), -- 10*10
e3(n) AS (SELECT 1 FROM e1 CROSS JOIN e2), -- 10*100
e4(n) AS (SELECT 1 FROM e2 CROSS JOIN e3) -- 1000*100 
--,e5(n) AS (SELECT 1 FROM e4 CROSS JOIN e1) -- 100.000  *10   =   1 Million
INSERT TOP (@aBatchNumber) dbo.SAMPLE_CUSTOMER(stt,randNumber)
SELECT n=ROW_NUMBER() OVER (ORDER BY n) , ABS(CHECKSUM(NewId()))
FROM e4 
ORDER BY n

DECLARE @startingInBatch INT

TRUNCATE TABLE dbo.Customer
ALTER FULLTEXT INDEX ON dbo.Customer DISABLE

WHILE @runningBatchIndex*@aBatchNumber < @NumOfCustomer
begin

		SET @startingInBatch = @runningBatchIndex*@aBatchNumber+1

		INSERT INTO dbo.Customer
		(
			Id,
			Name,
			Code,
			B2BCode,
			Phone1,
			Phone2,
			Email,
			IsBackendCustomer,
			CMND,
			Deleted,
			Type,
			Notes,
			SourceClassificationId,
			Address,
			CustomerClass,
			Sex,
			Dob,
			CreatedDate,
			CreatedBy,
			IsDisabled
		)
		SELECT NEWID(),
			   CONCAT(fullname.first_name,' ',fullname.mid_name,' ',fullname.last_name) Fullname,
			   IIF(c.stt<=@nubmerOfCoreCustomer,'CO','VL') + cast(c.stt+@startingInBatch AS VARCHAR(max)) ,
			   IIF(c.stt<=@nubmerOfCoreCustomer,'CO','VL') + cast(c.stt+@startingInBatch AS VARCHAR(max)) ,
			   RIGHT('000' + CAST(c.randNumber % 88 + 10 AS VARCHAR(max)),3) + RIGHT('0000000' + cast (c.stt+@startingInBatch +  500000 AS VARCHAR(max)),7) Phone1 ,
			   RIGHT('000' + CAST(c.randNumber % 88 + 10 AS VARCHAR(max)),3) + RIGHT('0000000' + cast (c.stt+@startingInBatch + 5500000 AS VARCHAR(max)),7) Phone2,
				CONCAT('user' , (c.stt+@startingInBatch) ,'@test.poptech') Email,
			   IIF(c.stt<=@nubmerOfCoreCustomer,1,0) IsBackEndCustomer,
			   c.stt+@startingInBatch,
			   0,
			   1,
			   NULL,
			   '{00000000-0000-0000-0000-000000000000}',
			   CONCAT(street.addr2, ' ',street.addr1),   -- TODO
			   IIF(c.randNumber%10=0,1,0),
			   1,
			   DATEADD(MONTH, -1 * ABS(CONVERT(VARBINARY, NEWID())%(90 * 12)), GETDATE()) AS Dob ,
			   GETDATE(),
			   '{21303c71-71f8-4967-9486-b68cd3106f9d}',
			   0
		FROM dbo.SAMPLE_CUSTOMER c
		JOIN dbo.SAMPLE_NAME fullname ON c.randNumber % 100  = fullname.id-1
		JOIN dbo.SAMPLE_STREET street ON c.randNumber % @countStreet  = street.id-1

		PRINT CONCAT('from ',@aBatchNumber*@runningBatchIndex,' to <=',(@aBatchNumber*(@runningBatchIndex+1)))
		SET @runningBatchIndex = @runningBatchIndex+1

END

ALTER FULLTEXT INDEX ON dbo.Customer ENABLE

IF OBJECT_ID('SAMPLE_NAME') IS NOT NULL DROP TABLE SAMPLE_NAME
IF OBJECT_ID('SAMPLE_CITY_STATE_ZIP') IS NOT NULL    DROP TABLE SAMPLE_CITY_STATE_ZIP
IF OBJECT_ID('SAMPLE_STREET') IS NOT NULL DROP TABLE SAMPLE_STREET
IF OBJECT_ID('SAMPLE_CUSTOMER') IS NOT NULL DROP TABLE SAMPLE_CUSTOMER

END
GO