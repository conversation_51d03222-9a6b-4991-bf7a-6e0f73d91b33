
/****** Object:  StoredProcedure [import].[Report_DuplicationPerUser]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[Report_DuplicationPerUser]
		@username VARCHAR(max)  
AS
BEGIN
	SELECT DataSource,PhoneNumber [SoDienThoai],ContactName 'TenKhachHang',Address 'Dia<PERSON>hi', ProvinceName 'Tinh' , DO<PERSON> 'NgaySinh'
	,<PERSON> 'GioiTinh', <PERSON> 'NgheNghiep', <PERSON><PERSON> '<PERSON>hTrang<PERSON>on<PERSON>han',  Income'ThuNhap' , Note 'GhiChu'
	 FROM import.TMRContact 
	WHERE IsInDbContact=0 AND username=@username
	AND DataSource IS NOT NULL AND PhoneNumber IS NOT NULL
	AND ( PhoneNumberValid IS NULL
    OR (PhoneNumberValid IS NOT NULL AND Dup_InternalUserOrder>1 AND Dup_SelfInDb=0)
	OR (Dup_CrossUserGroupCount IS NOT NULL AND Dup_CrossUserGroupCount>1)
	)
end
GO