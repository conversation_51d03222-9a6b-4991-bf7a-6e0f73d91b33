
/****** Object:  StoredProcedure [telesale].[ImportAppointmentResult_GetResult]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE procedure [telesale].[ImportAppointmentResult_GetResult]
	@errorCode bigint
as
begin
	set nocount on;
	select LeadCode Lead, 
			[Ngày hẹn] AppointmentDate, 
			[DMO Sup Name] NameOfSupDMO, 
			[DMO code] CodeDMO, 
			IIF(ErrorCode & 32 <> 0 AND ErrorCode & 256 = 0, 'Successfully Import', IIF(ErrorCode & 4 <> 0, 'No match Field Sale Code', '')) DMOImportStatus, 
			Result, 
			IIF(ErrorCode & 64 <> 0 AND ErrorCode & 256 = 0, 'Successfully Import', IIF(ErrorCode & 8 <> 0, 'No match Result Code','')) ResultCodeImportStatus,
			FeedbackNotes,
			IIF(ErrorCode & 128 <> 0 AND ErrorCode & 256 = 0, 'Successfully Import', '') FeedbackNotesImportStatus,
			STUFF(CONCAT('; ' + IIF(ErrorCode & 1 <> 0, 'Blank LeadCode or Meetdate', NULL), 
				'; ' + IIF(ErrorCode & 2 <> 0, 'Duplicate app with same code or MeetDate in file', NULL), 
				'; ' + IIF(ErrorCode & 16 <> 0, 'No match Appointment with LeadCode and MeetDate', NULL),
				'; ' + IIF(ErrorCode & 256 <> 0, 'Could NOT update because appointment has been updated result already', NULL)),
				1,2,'') AppointmentImportStatus
	from dbo.ImportAppointmentFB
	where @errorCode is null or (ErrorCode & @errorCode <> 0 AND @errorCode <> 0 AND @errorCode <> -1) or (@errorCode = 0 AND (ErrorCode & 32 <> 0 OR ErrorCode & 64 <> 0 OR ErrorCode & 128 <> 0))
		or (@errorCode = -1 AND (ErrorCode & 1 <> 0 OR ErrorCode & 2 <> 0 OR ErrorCode & 4 <> 0 OR ErrorCode & 8 <> 0 OR ErrorCode & 16 <> 0)) 
end
GO