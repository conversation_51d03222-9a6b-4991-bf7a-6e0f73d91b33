﻿using System.Data;

namespace Webaby
{
    public static class DataRowExtensions
    {
        public static object GetValue(this DataRow row, string column)
        {
            return row.Table.Columns.Contains(column) ? row[column] : null;
        }

        public static string GetString(this DataRow row, string column)
        {
            return row.Table.Columns.Contains(column) ? row[column].ToString() : string.Empty;
        }

        public static IEnumerable<DataRow> Where(this DataRowCollection rowCollection, Func<DataRow, bool> predicate)
        {
            foreach (DataRow dr in rowCollection)
            {
                if (predicate.Invoke(dr))
                {
                    yield return dr;
                }
            }
        }

        public static IEnumerable<T> Select<T>(this DataRowCollection rowCollection, Func<DataRow, T> selector)
        {
            foreach (DataRow dr in rowCollection)
            {
                yield return selector.Invoke(dr);
            }
        }

        public static void Each(this DataRowCollection rowCollection, Func<DataRow, bool> invoker)
        {
            foreach (DataRow dr in rowCollection)
            {
                if (!invoker.Invoke(dr))
                {
                    break;
                }
            }
        }

        public static IEnumerable<DataColumn> Where(this DataColumnCollection columnCollection, Func<DataColumn, bool> predicate)
        {
            foreach (DataColumn dc in columnCollection)
            {
                if (predicate.Invoke(dc))
                {
                    yield return dc;
                }
            }
        }

        public static IEnumerable<T> Select<T>(this DataColumnCollection columnCollection, Func<DataColumn, T> selector)
        {
            foreach (DataColumn dc in columnCollection)
            {
                yield return selector.Invoke(dc);
            }
        }

        public static void Each(this DataColumnCollection columnCollection, Func<DataColumn, bool> invoker)
        {
            foreach (DataColumn dc in columnCollection)
            {
                if (!invoker.Invoke(dc))
                {
                    break;
                }
            }
        }
    }
}