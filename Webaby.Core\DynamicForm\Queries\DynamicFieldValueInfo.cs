﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Webaby.Core.DynamicForm.Queries
{
    public class DynamicFieldValueInfo : IDynamicProperty
    {
        public Guid? Id { get; set; }

        public Guid ObjectId { get; set; }

        public Guid FormId { get; set; }

        public Guid FieldId { get; set; }

        public Guid? DynamicFormValueId { get; set; }

        public Guid? DynamicFieldSectionId { get; set; }

        public Guid? DynamicDefinedTableSchemaId { get; set; }

        public Guid? DynamicDefinedTableColumnId { get; set; }

        public string DynamicFieldSectionName { get; set; }

        public string Name { get; set; }

        public string DisplayName { get; set; }

        public string DataType { get; set; }

        public string AdditionalFilter { get; set; }

        public int Order { get; set; }

        public string Value { get; set; }

        public object ObjectValue { get; set; }

        public bool BusinessValidationResult { get; set; }

        public string ViewHint { get; set; }

        public bool Display { get; set; }

        public bool IsReadOnly { get; set; }

        public bool IsRequired { get; set; }

        public string Validation { get; set; }

        public string BusinessValidation { get; set; }

        public string AdditionalMetadata { get; set; }

        public Type Type { get; set; }

        public FieldType FieldType { get; set; }

        public string DefaultValue { get; set; }

        public string FomularByFieldName { get; set; }

        public string CoreFormula { get; set; }

        public string SelectOptions { get; set; }

        public string SourceTableName { get; set; }

        public string SourceFieldName { get; set; }

        public string SpecialFieldType { get; set; }

        public string InputFieldGroup { get; set; }

        public string RequiredDependency { get; set; }

        public string Inject { get; set; }

        public string Mapping360FieldName { get; set; }

        public string Color { get; set; }

        public string BackgroundColor { get; set; }
        public Mapping360Type? Mapping360FieldType { get; set; }

        public string Mapping360RowOptions { get; set; }

        public string GlobalCalculatedScriptJson { get; set; }

        public Guid? PaymentType { get; set; }

        public Guid? RepresentationDynamicFieldId { get; set; }

        public string VersionCode { get; set; }

        public bool FreezeValue { get; set; }

        public bool IsExportByConditionBoolean { get; set; }

        public bool IsExportExcel { get; set; }

        public string CustomDependencies { get; set; }

        public bool IsCheckDuplicate { get; set; }

        public IEnumerable<DynamicValidationInfo> GetValidation()
        {
            return Validation.IsNotNullOrEmpty() ? JsonConvert
                .DeserializeObject<IEnumerable<DynamicValidationInfo>>
                (
                    Validation
                ) : new List<DynamicValidationInfo>();
        }

        public IEnumerable<DynamicValidationInfo> GetBusinessValidation()
        {
            return BusinessValidation.IsNotNullOrEmpty() ? JsonConvert
                .DeserializeObject<IEnumerable<DynamicValidationInfo>>
                (
                    BusinessValidation
                ) : new List<DynamicValidationInfo>();
        }

        public Dictionary<string, string> GetAdditionalMetadata()
        {
            var metadatas = AdditionalMetadata.IsNotNullOrEmpty() ? JsonConvert
                .DeserializeObject<IEnumerable<DynamicAdditionalMetadataInfo>>
                (
                    AdditionalMetadata
                ) : new List<DynamicAdditionalMetadataInfo>();

            var dict = new Dictionary<string, string>();
            foreach (var itm in metadatas)
            {
                if (!dict.ContainsKey(itm.Key))
                {
                    dict.Add(itm.Key, itm.Value);
                }
            }
            return dict;
        }

        public IEnumerable<DynamicCustomDependency> GetCustomDependencies()
        {
            return CustomDependencies.IsNotNullOrEmpty() ? JsonConvert.DeserializeObject<IEnumerable<DynamicCustomDependency>>(CustomDependencies) : new List<DynamicCustomDependency>();
        }
    }
}