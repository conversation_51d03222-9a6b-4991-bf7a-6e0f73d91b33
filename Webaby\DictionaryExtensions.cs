﻿using System.Collections;
using System.Dynamic;

namespace Webaby
{
    public static class DictionaryExtensions
    {
        public static ExpandoObject ToExpando(this IDictionary<string, object> dictionary)
        {
            var expando = new ExpandoObject();
            var expandoDic = (IDictionary<string, object>)expando;
            foreach (var kvp in dictionary)
            {
                if (kvp.Value is IDictionary<string, object>)
                {
                    var expandoValue = ((IDictionary<string, object>)kvp.Value).ToExpando();
                    expandoDic.Add(kvp.Key, expandoValue);
                }
                else if (kvp.Value is ICollection)
                {
                    var itemList = new List<object>();
                    foreach (var item in (ICollection)kvp.Value)
                    {
                        if (item is IDictionary<string, object>)
                        {
                            var expandoItem = ((IDictionary<string, object>)item).ToExpando();
                            itemList.Add(expandoItem);
                        }
                        else
                        {
                            itemList.Add(item);
                        }
                    }
                    expandoDic.Add(kvp.Key, itemList);
                }
                else
                {
                    expandoDic.Add(kvp);
                }
            }
            return expando;
        }

        public static Dictionary<TValue, TKey> Reverse<TKey, TValue>(this Dictionary<TKey, TValue> sourceDict)
        {
            var dictionary = new Dictionary<TValue, TKey>();
            foreach (var entry in sourceDict)
            {
                if (!dictionary.ContainsKey(entry.Value))
                {
                    dictionary.Add(entry.Value, entry.Key);
                }
            }
            return dictionary;
        }

        public static void AddRange<TKey, TValue>(this Dictionary<TKey, List<TValue>> destDict,
            Dictionary<TKey, List<TValue>> srcDict)
        {
            foreach (var kv in srcDict)
            {
                if (destDict.ContainsKey(kv.Key))
                {
                    destDict[kv.Key].AddRange(kv.Value);
                }
                else
                {
                    destDict.Add(kv.Key, kv.Value);
                }
            }
        }
    }
}