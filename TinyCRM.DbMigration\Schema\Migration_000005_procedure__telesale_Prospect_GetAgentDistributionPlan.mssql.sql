
/****** Object:  StoredProcedure [telesale].[Prospect_GetAgentDistributionPlan]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [telesale].[Prospect_GetAgentDistributionPlan]
    @CampaignId UNIQUEIDENTIFIER, @SelectedAgentIds IdList READONLY, @TotalProspectToDistribute INT, @DistributedDate DATETIME
AS
BEGIN


    IF @DistributedDate IS NULL SET @DistributedDate = GETDATE();

    -- Find TeamId by an agent
    DECLARE @TeamId UNIQUEIDENTIFIER;
    SET @TeamId = (SELECT TOP 1 up.OrganizationId FROM @SelectedAgentIds a JOIN dbo.UserProfiles up ON up.Id = a.Id);

    --------------------------------------------------------------------------------
    --------------------------------------------------------------------------------
    SELECT yy.IsSelected,
           yy.AgentId,
           yy.AgentName,
           yy.AgentCode,
           yy.TargetContact,
           yy.AssignedCount,
           CASE WHEN @TotalProspectToDistribute >= yy.TotalDistributeIfAlwaysEnough THEN yy.NeedAssign ELSE
                                                                                                           CASE WHEN @TotalProspectToDistribute - (yy.TotalDistributeIfAlwaysEnough - yy.NeedAssign) > 0 THEN
                                                                                                                    @TotalProspectToDistribute - (yy.TotalDistributeIfAlwaysEnough - yy.NeedAssign) ELSE
                                                                                                                                                                                                        0 END END ToBeAssigned
    FROM (   SELECT *, SUM(NeedAssign) OVER (ORDER BY AgentId DESC) TotalDistributeIfAlwaysEnough
             FROM (   SELECT targetModel.AgentId,
                             up.FullName AgentName,
                             up.AgentCode,
                             ISNULL(targetModel.TargetContact, 0) TargetContact,
                             ISNULL(assigned.AssignedCount, 0) AssignedCount,
                             CASE WHEN su.Id IS NULL THEN 0 ELSE 1 END IsSelected,
                             CASE WHEN su.Id IS NULL
                                       OR (assigned.AssignedCount >= targetModel.TargetContact) THEN 0 ELSE ISNULL(targetModel.TargetContact, 0) - ISNULL(assigned.AssignedCount, 0)END NeedAssign
                      FROM (
                               -- Calculate TargetModel for each agent
                               SELECT up.Id AgentId, SUM(tm.TargetContact) TargetContact
                               FROM dbo.Organization o
                               JOIN dbo.UserProfiles up ON up.OrganizationId = o.Id
                               JOIN dbo.AgentTargetByCompetenceModel tm ON tm.CompetenceLevel = up.CompetenceLevel
                               JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
                               WHERE up.OrganizationId = @TeamId
                               GROUP BY up.Id) targetModel
                      LEFT JOIN (
                                    -- Calculate Assigned for each agent
                                    SELECT pa.AssignedAgentId AgentId, COUNT(*) AssignedCount
                                    FROM dbo.Prospect p
                                    JOIN dbo.ProspectAssignment pa ON p.CurrentAssignmentId = pa.Id
                                    WHERE pa.AssignedAgentId IS NOT NULL
                                          AND AssignedAgentDate IS NOT NULL
                                          AND CAST(AssignedAgentDate AS DATE) = CAST(@DistributedDate AS DATE)
                                    GROUP BY pa.AssignedAgentId) assigned ON assigned.AgentId = targetModel.AgentId
                      JOIN @SelectedAgentIds su ON su.Id = targetModel.AgentId
                      JOIN dbo.UserProfiles up ON up.Id = targetModel.AgentId) xx ) yy
    ORDER BY yy.AgentName;
END;
GO