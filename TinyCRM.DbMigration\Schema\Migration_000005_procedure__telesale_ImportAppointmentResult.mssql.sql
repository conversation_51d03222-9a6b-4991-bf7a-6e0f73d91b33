
/****** Object:  StoredProcedure [telesale].[ImportAppointmentResult]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[ImportAppointmentResult]
	@RawTable RawTableParam READONLY,
	@UserId UNIQUEIDENTIFIER=NULL
AS
BEGIN

	SET NOCOUNT ON;
	DECLARE @Old AppointmentAudit;
	DECLARE @New AppointmentAudit;

	-------------------------------------------
	-- INPUT DATA INTO STAGING TABLE
	--------------------------------------------
	TRUNCATE TABLE dbo.ImportAppointmentFB

	INSERT INTO dbo.ImportAppointmentFB (LeadCode, [DMO code], [<PERSON><PERSON><PERSON> hẹn], Result, [DMO Sup Name], FeedbackNotes)
	SELECT	p1 LeadCode, p4 [DMO code], pd1 [Ngày hẹn], p5 Result, p3 [DMO Sup Name], p6 FeedbackNotes
	FROM	@RawTable

	------ WHY : just to cleanup data in the following fields, which is to hold scanning and calculated, pre-matched values
	UPDATE	dbo.ImportAppointmentFB
	SET		SLeadId = NULL,
			SDmoId  = NULL,
			SDmoTeamId  = NULL,
			SAppResultCodeId = null,
			SAppointmentId = NULL,
			SLeadAssignmentId = NULL,
			ErrorCode = 0,
			RowId = NULL

	UPDATE dbo.ImportAppointmentFB SET RowId=NEWID()

	----------------------------------------------------------------------------------------------------
	-- SCANNING DATA - PRE-Fetch Guid for matched values like App, Field sale Code, Result code
	------------------------------------------------------------------------------------------------------

	-- MATCH 1: APPOINTMENT

	-- ERROR BIT 1: Không có LeadCode hoặc Ngày hẹn
	UPDATE  dbo.ImportAppointmentFB
	SET     LeadCode = LTRIM(RTRIM(LeadCode)),
			[DMO code] = LTRIM(RTRIM([DMO code])),
			Result  = LTRIM(RTRIM(Result))
	FROM    dbo.ImportAppointmentFB

	UPDATE  dbo.ImportAppointmentFB
	SET     LeadCode = IIF(LeadCode='',NULL,LeadCode),
			[DMO code]= IIF([DMO code]='',NULL,[DMO code]),
			Result= IIF(Result='',NULL,Result)
	FROM    dbo.ImportAppointmentFB

	UPDATE dbo.ImportAppointmentFB SET ErrorCode=ErrorCode | 1 WHERE LeadCode IS NULL OR [Ngày hẹn] IS NULL


	-- Xét lỗi trùng nhiều dòng cùng LeadCode trong file excel
	--> Chỉ cho dòng có ngày hẹn mới nhất trong nhóm Lead đó được xử lý tiếp, báo lỗi các dòng có ngày hẹn cũ hơn

	-- ERROR BIT 2: Trùng hẹn cùng lead code trong file excel
	UPDATE	dbo.ImportAppointmentFB 
	SET		ErrorCode = i.ErrorCode | 2
	FROM	(
				SELECT	*, ROW_NUMBER() OVER (PARTITION BY LeadCode ORDER BY [Ngày hẹn] DESC) rowNo
				FROM	dbo.ImportAppointmentFB
			) yy 
			JOIN dbo.ImportAppointmentFB i ON i.RowId = yy.RowId
	WHERE	yy.rowNo > 1  -- không phải hẹn cuối

	-- Match Dmo, CallResult
	UPDATE	dbo.ImportAppointmentFB 
	SET		SDmoId= dmo.UserId,
			SDmoTeamId=dmo.DmoTeamId
	FROM	dbo.ImportAppointmentFB i
			LEFT JOIN 
			(
				SELECT	up.AgentCode, u.UserId, up.OrganizationId DmoTeamId,u.LoweredUserName, ROW_NUMBER() OVER (PARTITION BY up.AgentCode ORDER BY m.isApproved DESC,m.userid) rowNo
				FROM	dbo.UserProfiles up
						JOIN dbo.UserInRoles ur ON ur.UserId=up.Id
						JOIN dbo.aspnet_Users u ON u.UserId=up.Id
						JOIN dbo.aspnet_Membership m ON m.UserId=u.UserId
						--JOIN dbo.Roles r ON r.Id=ur.RoleId
				--WHERE	r.Name = 'Field Sale'  -- HARDCODE THAT KINH KHUNG
			) dmo ON dmo.LoweredUserName=LOWER(i.[DMO code])-- AND dmo.rowNo=1

	-- ERROR BIT 3: No match DMO code
	UPDATE	dbo.ImportAppointmentFB 
	SET		ErrorCode = ErrorCode| 4
	WHERE	SDmoId IS NULL
			AND [DMO code] IS NOT NULL
			AND [DMO code] <> 'NA'


	-- Match AppCallResultId
	UPDATE	dbo.ImportAppointmentFB 
	SET		SAppResultCodeId = rc.Id
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.AppointmentResultCode rc ON rc.ResultCode=i.Result

	-- ERROR BIT 4: No match App result code
	UPDATE	dbo.ImportAppointmentFB
	SET		ErrorCode = ErrorCode | 8
	WHERE	SAppResultCodeId IS NULL 
			AND Result IS NOT NULL
			AND Result <> 'NA'

	-- Prepare all valid Appointment which is waiting for Feedback
	SELECT	l.Id LeadId,a.Id AppId, la.Id LaId, l.LeadCode,a.MeetDate, up.AgentCode DmoCode,l.CreatedDate LeadCreatedDate,
			la.AssignedFieldSaleId, la.AssignedFieldSaleTeamId
	INTO	#AppToBeUpdateCodeResult 
	FROM	dbo.Lead l
			JOIN dbo.LeadAssignment la ON l.CurrentLeadAssignmentId=la.Id
			JOIN dbo.Appointment a ON a.Id=la.WaitingAppointmentId
			JOIN dbo.ImportAppointmentFB ia ON ia.LeadCode=l.LeadCode AND DATEDIFF(DAY, ia.[Ngày hẹn], a.MeetDate) =0 AND (ia.ErrorCode=0 OR (ia.ErrorCode & 1 = 0 AND ia.ErrorCode & 2 = 0))
			LEFT JOIN dbo.UserProfiles up ON up.Id=la.AssignedFieldSaleId

	-- Match Appointment by LeadCode and MeetDate
	UPDATE	i
	SET		SLeadId=l.LeadId, 
			SAppointmentId=IIF(DATEDIFF(DAY,l.MeetDate,i.[Ngày hẹn])=0,l.AppId,NULL)
	FROM	dbo.ImportAppointmentFB i
			JOIN #AppToBeUpdateCodeResult l ON l.LeadCode=i.LeadCode AND DATEDIFF(DAY, i.[Ngày hẹn], l.MeetDate) =0
	WHERE	(i.ErrorCode =0 or i.ErrorCode & 1 = 0 or i.ErrorCode & 2 = 0)

	-- ERROR BIT 5: Không thể match được Appointment nào với LeadCode và Ngày hẹn
	UPDATE	dbo.ImportAppointmentFB
	SET		ErrorCode=ErrorCode | 16 
	WHERE	SAppointmentId IS NULL
			AND ErrorCode & 1 = 0

	UPDATE	i
	SET		SLeadAssignmentId=l.LaId
	FROM	dbo.ImportAppointmentFB i
			JOIN #AppToBeUpdateCodeResult l ON i.SAppointmentId = l.AppId
	WHERE	i.SAppointmentId IS NOT NULL

	-- Success DMO Update
	UPDATE	i
	SET		ErrorCode = i.ErrorCode | 32
	FROM	dbo.ImportAppointmentFB i
			JOIN #AppToBeUpdateCodeResult l on i.SAppointmentId = l.AppId
			LEFT JOIN dbo.LeadAssignment la ON la.LeadId = l.LeadId
	WHERE	i.SAppointmentId is NOT NULL
			AND ((i.SDmoId is NOT NULL and i.[DMO Code] IS NOT NULL AND (i.SDmoId <> la.AssignedFieldSaleId OR la.AssignedFieldSaleId IS NULL)) OR i.[DMO Code] = 'NA')

	-- Success ResultCode Update
	UPDATE	i 
	SET		ErrorCode = i.ErrorCode | 64
	FROM	dbo.ImportAppointmentFB i
			JOIN #AppToBeUpdateCodeResult l ON i.SAppointmentId = l.AppId
			LEFT JOIN dbo.Appointment app ON app.Id = l.AppId
	WHERE	i.SAppointmentId IS NOT NULL 
			AND ((i.SAppResultCodeId is NOT NULL and i.[Result] IS NOT NULL AND (i.SAppResultCodeId <> app.AppointmentResultCodeId OR app.AppointmentResultCodeId IS NULL)) OR i.Result = 'NA')

	-- Success Feedback Notes Update
	UPDATE	i
	SET		ErrorCode = i.ErrorCode | 128
	FROM	dbo.ImportAppointmentFB i
			JOIN #AppToBeUpdateCodeResult l ON i.SAppointmentId = l.AppId
			LEFT JOIN dbo.Appointment app ON app.Id = l.AppId
	WHERE	i.SAppointmentId IS NOT NULL
			AND i.FeedbackNotes is NOT NULL AND (i.FeedbackNotes <> app.FeedbackNotes OR app.FeedbackNotes IS NULL)

	--============================================================================
	-- Kiểm tra xem Appointment hiện tại đã có Kết quả làm thay đổi WORK chưa?
	-- Nếu có, KHÔNG cho cập nhật kết quả hẹn nữa
	--============================================================================
	UPDATE	i
	SET		ErrorCode = i.ErrorCode | 256
	FROM	dbo.ImportAppointmentFB i
			JOIN #AppToBeUpdateCodeResult l ON i.SAppointmentId = l.AppId
			JOIN dbo.Appointment app ON app.Id = l.AppId
			JOIN dbo.AppointmentResultCode appRC ON appRC.Id = app.AppointmentResultCodeId
	WHERE	i.SAppointmentId IS NOT NULL
			AND appRC.ProspectResultId IS NOT NULL


	BEGIN TRANSACTION

	INSERT INTO @Old (LeadAssignmentId, AppointmentId, AssignedFieldSaleId, AssignedFieldSaleTeamId, LeadAssignmentStatus,
					MeetAddress, MeetDate, AppointmentResultCodeId, RetryCount, Notes, AppointmentStatus, FeedbackNotes)
	SELECT	la.Id LeadAssignmentId, ap.Id AppointmentId, la.AssignedFieldSaleId, la.AssignedFieldSaleTeamId, la.Status LeadAssignmentStatus,
			ap.MeetAddress, ap.MeetDate, ap.AppointmentResultCodeId, ap.RetryCount, ap.Notes, ap.Status AppointmentStatus, ap.FeedbackNotes
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.LeadAssignment la ON i.SLeadAssignmentId=la.Id
			JOIN dbo.Appointment ap ON ap.LeadAssignmentId=la.Id
	WHERE	i.SLeadAssignmentId IS NOT NULL
			AND ((i.ErrorCode & 32 <> 0 OR i.ErrorCode & 64 <> 0 OR i.ErrorCode & 128 <> 0) OR i.ErrorCode = 0)
			AND i.ErrorCode & 256 <> 256

	INSERT INTO @Old(LeadAssignmentId, AppointmentId, AssignedFieldSaleId, AssignedFieldSaleTeamId, LeadAssignmentStatus, 
					MeetAddress, MeetDate, AppointmentResultCodeId, RetryCount, Notes, AppointmentStatus, FeedbackNotes)
	SELECT	la.Id LeadAssignmentId, ap.Id AppointmentId, la.AssignedFieldSaleId, la.AssignedFieldSaleTeamId, la.Status LeadAssignmentStatus,
			ap.MeetAddress, ap.MeetDate, ap.AppointmentResultCodeId, ap.RetryCount, ap.Notes, ap.Status AppointmentStatus, ap.FeedbackNotes
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.Appointment ap ON ap.Id=i.SAppointmentId
			JOIN dbo.LeadAssignment la ON la.Id=ap.LeadAssignmentId
			JOIN dbo.AppointmentResultCode rc ON rc.Id = i.SAppResultCodeId
			LEFT JOIN @Old o ON o.AppointmentId=ap.Id
	WHERE	((i.ErrorCode & 32 <> 0 OR i.ErrorCode & 64 <> 0 OR i.ErrorCode & 128 <> 0) OR i.ErrorCode = 0)
			AND o.AppointmentId IS NULL
			AND i.ErrorCode & 256 <> 256

	-- update Lead Assignment
	UPDATE	la
	SET		AssignedFieldSaleId=i.SDmoId,
			AssignedFieldSaleDate=Convert(date, getdate()),
			AssignedFieldSaleTeamId=i.SDmoTeamId,
			AssignedFieldSaleTeamDate=Convert(date, getdate()),
			Status=2
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.LeadAssignment la ON i.SLeadAssignmentId=la.Id
	WHERE	i.SLeadAssignmentId IS NOT NULL
			AND i.ErrorCode & 32 <> 0
			AND i.ErrorCode & 256 <> 256

	UPDATE	la 
	SET		AssignedFieldSaleId = NULL,
			AssignedFieldSaleDate = NULL,
			AssignedFieldSaleTeamId = NULL,
			Status = 1
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.LeadAssignment la ON i.SLeadAssignmentId = la.Id
	WHERE	i.SLeadAssignmentId IS NOT NULL
			AND i.[DMO code] = 'NA'
			AND i.ErrorCode & 256 <> 256

	-- Update Appointment result
	UPDATE	a 
	SET		AppointmentResultCodeId=IIF(i.ErrorCode & 64 <> 0 AND i.SAppResultCodeId IS NOT NULL, i.SAppResultCodeId, a.AppointmentResultCodeId),
			UpdatedResultDate=GETDATE(),
			UpdatedResultBy=@UserId,
			Status= IIF(i.ErrorCode & 64 <> 0 AND rc.Id IS NOT NULL, rc.AppointmentStatus, a.Status),
			a.FeedbackNotes = IIF(i.ErrorCode & 128 <> 0, i.FeedbackNotes, a.FeedbackNotes)
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.Appointment a ON a.Id=i.SAppointmentId
			LEFT JOIN dbo.AppointmentResultCode rc ON rc.Id = i.SAppResultCodeId
	WHERE	(i.ErrorCode & 64 <> 0 OR i.ErrorCode & 128 <> 0)
			AND i.ErrorCode & 256 <> 256

	UPDATE	a
	SET		AppointmentResultCodeId = NULL,
			UpdatedResultDate = GETDATE(),
			UpdatedResultBy = @UserId,
			a.FeedbackNotes = i.FeedbackNotes
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.Appointment a ON a.Id = i.SAppointmentId
	WHERE	i.Result = 'NA'
			AND i.ErrorCode & 256 <> 256

	PRINT 'AppId update FB ' + CAST(@@ROWCOUNT AS VARCHAR(max))

	UPDATE	l 
	SET		l.Status=rc.LeadStatus
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.AppointmentResultCode rc ON rc.Id = i.SAppResultCodeId
			JOIN dbo.Lead l ON l.Id=i.SLeadId
	WHERE	((i.ErrorCode & 32 <> 0 AND i.ErrorCode & 64 <> 0 AND i.ErrorCode & 128 <> 0) OR i.ErrorCode = 0)
			AND i.ErrorCode & 256 <> 256

	-- Close LeadAssignment, nếu WIN hoặc LOST
	UPDATE	la
	SET		la.Status=3 -- Done
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.LeadAssignment la ON la.Id=i.SLeadAssignmentId
			JOIN dbo.AppointmentResultCode apr ON apr.Id = i.SAppResultCodeId AND (apr.LeadStatus = 4 OR apr.LeadStatus = 5)
	WHERE	((i.ErrorCode & 32 <> 0 AND i.ErrorCode & 64 <> 0 AND i.ErrorCode & 128 <> 0) OR i.ErrorCode = 0)
			AND i.ErrorCode & 256 <> 256

	--============================================================================
	-- Cập nhật "WORK" ProspectResultId nếu:
	--  + AppointmentResultCode.ProspectResultId IS NOT NULL
	--  + WORK chưa "SUBMITTED"
	--============================================================================
	SELECT	a.ProspectId, a.ProspectAssignmentId, cr.Id CallResultId
	INTO	#TempWorks
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.Appointment a ON a.Id=i.SAppointmentId
			JOIN dbo.AppointmentResultCode rc ON rc.Id = i.SAppResultCodeId
			JOIN dbo.CallResult cr ON cr.Id = rc.ProspectResultId
			JOIN dbo.Prospect p ON p.Id = a.ProspectId
			LEFT JOIN dbo.CallResult pcurrentResult ON pcurrentResult.Id = p.CallResultId
	WHERE	i.ErrorCode & 256 <> 256
			AND ISNULL(pcurrentResult.FollowUpStatus,0) <> 4

	UPDATE	p
	SET		CallResultId = temp.CallResultId
	FROM	#TempWorks temp
			JOIN dbo.Prospect p ON p.Id = temp.ProspectId

	UPDATE	pa
	SET		CallResultId = temp.CallResultId
	FROM	#TempWorks temp
			JOIN dbo.ProspectAssignment pa ON pa.Id = temp.ProspectAssignmentId

	SELECT	CONVERT(VARCHAR(max), LeadCode) Lead, [Ngày hẹn] AppointmentDate, [DMO Sup Name] NameOfSupDMO, [DMO code],  Result, FeedbackNotes [Feedback Notes],
			IIF(ErrorCode & 1 <>0, 'Blank LeadCode or Meetdate',
			IIF(ErrorCode & 2 <> 0,'Duplicate app with same Code or MeetDate in file',
			IIF(ErrorCode & 4 <> 0,'No match Field Sale Code',
			IIF(ErrorCode & 8 <> 0,'No match Result Code',
			IIF(ErrorCode & 16 <> 0,'No match Appointment with LeadCode and MeetDate',
			IIF(ErrorCode & 256 <> 0,'Appointment has been updated result already',
			'')))))) ErrorDescription
	FROM	dbo.ImportAppointmentFB-- WHERE ErrorCode IS NOT NULL

	INSERT INTO @New(LeadAssignmentId, AppointmentId, AssignedFieldSaleId, AssignedFieldSaleTeamId, LeadAssignmentStatus, 
					MeetAddress, MeetDate, AppointmentResultCodeId, RetryCount, Notes, AppointmentStatus, FeedbackNotes)
	SELECT	la.Id LeadAssignmentId, ap.Id AppointmentId, la.AssignedFieldSaleId, la.AssignedFieldSaleTeamId, la.Status LeadAssignmentStatus,
			ap.MeetAddress, ap.MeetDate, ap.AppointmentResultCodeId, ap.RetryCount, ap.Notes, ap.Status AppointmentStatus, ap.FeedbackNotes
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.LeadAssignment la ON i.SLeadAssignmentId=la.Id
			JOIN dbo.Appointment ap ON ap.LeadAssignmentId=la.Id
	WHERE	i.SLeadAssignmentId IS NOT NULL
			AND ((i.ErrorCode & 32 <> 0 AND i.ErrorCode & 64 <> 0 AND i.ErrorCode & 128 <> 0) OR i.ErrorCode = 0)
			AND i.ErrorCode & 256 <> 256

	INSERT INTO @New(LeadAssignmentId, AppointmentId, AssignedFieldSaleId, AssignedFieldSaleTeamId, LeadAssignmentStatus, 
					MeetAddress, MeetDate, AppointmentResultCodeId, RetryCount, Notes, AppointmentStatus, FeedbackNotes)
	SELECT	la.Id LeadAssignmentId, ap.Id AppointmentId, la.AssignedFieldSaleId, la.AssignedFieldSaleTeamId, la.Status LeadAssignmentStatus,
			ap.MeetAddress, ap.MeetDate, ap.AppointmentResultCodeId, ap.RetryCount, ap.Notes, ap.Status AppointmentStatus, ap.FeedbackNotes
	FROM	dbo.ImportAppointmentFB i
			JOIN dbo.Appointment ap ON ap.Id=i.SAppointmentId
			JOIN dbo.LeadAssignment la ON la.Id=ap.LeadAssignmentId
			JOIN dbo.AppointmentResultCode rc ON rc.Id = i.SAppResultCodeId
			LEFT JOIN @New o ON o.AppointmentId=ap.Id
	WHERE	((i.ErrorCode & 32 <> 0 AND i.ErrorCode & 64 <> 0 AND i.ErrorCode & 128 <> 0) OR i.ErrorCode = 0)
			AND o.AppointmentId IS NULL
			AND i.ErrorCode & 256 <> 256

	EXEC dbo.AppointmentAudit @_Old = @Old, @_New = @New, @UserId = @UserId -- uniqueidentifier

	COMMIT

END
GO