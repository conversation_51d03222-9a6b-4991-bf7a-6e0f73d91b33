
/****** Object:  StoredProcedure [telesale].[CreateContactCallFromAppointmentRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[CreateContactCallFromAppointmentRaw]
	@UserId UNIQUEIDENTIFIER,
	@SessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	INSERT INTO dbo.ContactCall
	        ( Id ,
	          ProspectAssignmentId ,
	          CallResultId ,
	          Duration ,
	          CreatedDate ,
	          CreatedBy ,
	          PhoneNumber ,
	          Notes ,
	          ProductId ,
	          ProductBudget ,
	          ContactId
	        )
	SELECT
			  ar.ContactCallId Id,
			  ar.ProspectAssignmentId,
			  (SELECT TOP 1 cr.Id FROM dbo.CallResult cr WHERE cr.Code=N'12') CallResultId,
			  45 Duration,
			  IIF(ar.CallDate IS NOT NULL, ar.CallDate, GETDATE()) CreatedDate,
			  tmr.Id CreatedBy,
			  IIF(ar.CellPhone IS NOT NULL, ar.CellPhone, ar.HomePhone) PhoneNumber,
			  N'' Notes,
			  NULL ProductId, --------------------------------------------NOTE
			  ar.ProductValue ProductBudget,
			  ar.ContactId
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles tmr ON ar.CodeTMR=tmr.AgentCode
	WHERE ar.IsInvalid <> 1 AND ar.ImportSessionId=@SessionId
	AND (NOT (ar.IsDupPa=1 AND ar.IsDupApp=1))
END
GO