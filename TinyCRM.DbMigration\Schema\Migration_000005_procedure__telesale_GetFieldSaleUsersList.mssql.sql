
/****** Object:  StoredProcedure [telesale].[GetFieldSaleUsersList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


--ALTER PROCEDURE [telesale].[GetFieldSaleUsersList]
CREATE PROCEDURE [telesale].[GetFieldSaleUsersList]

	@UserLogin UNIQUEIDENTIFIER,
	@FieldSaleTeamId	UNIQUEIDENTIFIER

AS
BEGIN

	DECLARE @TeamList IdList;
	 IF (SELECT r.Name FROM dbo.UserInRoles uir
	 JOIN dbo.Roles r ON r.Id=uir.RoleId
	 WHERE uir.UserId=@UserLogin)='DMO Manager'
		IF @FieldSaleTeamId IS NULL
			INSERT INTO @TeamList SELECT o.Id FROM dbo.UserProfiles up JOIN dbo.Organization o ON up.OrganizationId=o.ParentId WHERE up.Id=@UserLogin;
		ELSE
			INSERT INTO @TeamList SELECT @FieldSaleTeamId Id;
	ELSE
		INSERT INTO @TeamList SELECT @FieldSaleTeamId Id;

	SELECT	DISTINCT u.UserId FieldSaleId, up.FullName FieldSaleName, mbs.IsApproved,
			STUFF((	SELECT	';' + LOWER(CAST(hotU.HotListGroupId AS VARCHAR(50)))
					FROM	telesale.HotListGroupUser hotU
					WHERE	hotU.UserId = u.UserId
					FOR XML PATH('')), 1, 1, '') HotListGroupIds, o.Name OrganizationName
	FROM	dbo.aspnet_Membership mbs
			JOIN dbo.aspnet_Users u ON u.UserId = mbs.UserId
			JOIN dbo.UserInRoles uir ON uir.UserId = mbs.UserId
			JOIN dbo.Roles r ON r.Id = uir.RoleId
			JOIN dbo.UserProfiles up ON up.Id = mbs.UserId
			JOIN dbo.Organization o ON o.Id=up.OrganizationId
	WHERE	uir.RoleId = 'A4095631-C225-4644-B39A-F5152F4A525C'
			AND up.OrganizationId IN (SELECT Id FROM @TeamList)
	ORDER BY mbs.IsApproved DESC, o.Name, up.FullName

END
GO