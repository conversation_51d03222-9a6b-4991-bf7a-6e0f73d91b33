﻿using Webaby.Data;
using Webaby.EntityData;

namespace Webaby
{
    public interface IGridData
    {
        IEnumerable<string> GetPropertyValues(string name);

        Type GetPropertyType(string name);

        IEnumerable<IEntity> CloneReferenceEntities(Guid sourceValueGroupId, Guid destinationValueGroupId);

        IEntityData CopyDynamicFieldValue();
    }

    [AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
    public class GridDataColumnAttribute : Attribute
    {
        public string Name { get; set; }

        public string DisplayName { get; set; }

        public int ColumnOrder { get; set; }
    }
}