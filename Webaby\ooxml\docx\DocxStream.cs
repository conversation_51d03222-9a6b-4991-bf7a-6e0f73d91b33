﻿using DocumentFormat.OpenXml.Packaging;
using System.IO;

namespace Webaby.OOXML.docx
{
    public class DocxStream : MemoryStream
    {
        public DocxStream(byte[] buffer)
        {
            using (var temp = new MemoryStream(buffer))
            {
                temp.CopyTo(this);
            }
        }

        public WordprocessingDocument AsWordprocessingDocument(bool isEditable)
        {
            return WordprocessingDocument.Open(this, isEditable);
        }
    }
}
