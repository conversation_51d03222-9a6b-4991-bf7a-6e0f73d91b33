﻿
/****** Object:  StoredProcedure [telesale].[GetAppointmentDetailInfo]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



--ALTER PROCEDURE [telesale].[GetAppointmentDetailInfo]
CREATE PROCEDURE [telesale].[GetAppointmentDetailInfo]

	@AppointmentId			UNIQUEIDENTIFIER,
	@LeadAssignmentId		UNIQUEIDENTIFIER,
	@LeadId					UNIQUEIDENTIFIER

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SELECT	supTMR.Phone SupTMRPhone,
			c.Phone,
			c.Phone2,
			c.Phone3,
			ap.Id AppointmentId,
			c.Id ContactId,
			la.Id LeadAssignmentId,
			c.FullName CustomerName,
			c.<PERSON>,
			c.Income,
			UserDefinedFormatCode,
			l.Status LeadStatus,
            Meet<PERSON><PERSON>,
            <PERSON><PERSON><PERSON><PERSON>,
            ap.ProvinceId,
            ProvinceName,
            ap.DistrictId,
            DistrictName,
            ap.WardId,
            WardName,
            ap.ProductId,
            ProductName,
            ap.ProductBudget,
			ap.Notes,
            SuggestedFieldSaleTeamId,
            sugOrg.OrganizationName SuggestedFieldSaleTeamName,
            SuggestedFieldSaleId,
            ISNULL(sugUp.FullName,sugU.UserName) SuggestedFieldSaleName,
            SuggestedFieldSaleReason,
            ap.Status AppointmentStatus,
            ap.CreatedDate,
            ap.CreatedBy,
            ISNULL(createdUp.FullName, createdU.UserName) CreatedName,
			createdOrg.Id CreatedByTeamId,
			createdOrg.OrganizationName CreatedByTeamName,
            AssignedFieldSaleId,
            ISNULL(assignedUp.FullName,assignedU.UserName) AssignedFieldSaleName,
            AssignedFieldSaleDate,
            AssignedFieldSaleTeamId,
            assignedOrg.OrganizationName AssignedFieldSaleTeamName,
            AssignedFieldSaleTeamDate,
            la.Status LeadAssignmentStatus,
            AppointmentResultCodeId,
            aprc.ResultCode AppointmentResultCode,
            UpdatedResultDate,
            UpdatedResultBy,
            ISNULL(updateUp.FullName, updateU.UserName) UpdatedResultByAsName,
            FeedbackNotes
	FROM	dbo.Appointment ap
			JOIN dbo.Contact c ON c.Id = ap.ContactId
			JOIN dbo.LeadAssignment la ON ap.LeadAssignmentId = la.Id
			JOIN dbo.Lead l ON l.Id = la.LeadId
			JOIN dbo.Province p ON p.Id = ap.ProvinceId
			LEFT JOIN dbo.District d ON d.Id = ap.DistrictId
			LEFT JOIN dbo.Ward w ON w.Id = ap.WardId
			LEFT JOIN dbo.Product pr ON pr.Id = ap.ProductId
			LEFT JOIN dbo.Organization sugOrg ON sugOrg.Id = la.SuggestedFieldSaleTeamId
			LEFT JOIN dbo.aspnet_Users sugU ON sugU.UserId = la.SuggestedFieldSaleId
			LEFT JOIN dbo.UserProfiles sugUp ON sugUp.Id = sugU.UserId
			JOIN dbo.aspnet_Users createdU ON createdU.UserId = ap.CreatedBy
			JOIN dbo.UserProfiles createdUp ON createdUp.Id = createdU.UserId
			LEFT JOIN dbo.Organization createdOrg ON createdOrg.Id = createdUp.OrganizationId
			LEFT JOIN dbo.UserProfiles supTMR ON supTMR.OrganizationId=createdOrg.Id
			LEFT JOIN dbo.UserInRoles supTMRrole ON supTMRrole.UserId=supTMR.Id
			LEFT JOIN dbo.aspnet_Users assignedU ON assignedU.UserId = la.AssignedFieldSaleId
			LEFT JOIN dbo.UserProfiles assignedUp ON assignedUp.Id = assignedU.UserId
			LEFT JOIN dbo.Organization assignedOrg ON assignedOrg.Id = la.AssignedFieldSaleTeamId
			LEFT JOIN dbo.AppointmentResultCode aprc ON aprc.Id = ap.AppointmentResultCodeId
			LEFT JOIN dbo.aspnet_Users updateU ON updateU.UserId = ap.UpdatedResultBy
			LEFT JOIN dbo.UserProfiles updateUp ON updateUp.Id = updateU.UserId
	WHERE	(@AppointmentId IS NULL OR ap.Id = @AppointmentId)
			AND (@LeadAssignmentId IS NULL OR la.Id = @LeadAssignmentId)
			AND (@LeadId IS NULL OR l.Id = @LeadId)
			AND supTMRrole.RoleId='62217D0B-F045-4C4C-ACC4-B1D4DC9F1AAC' --teamlead
			SELECT * FROM dbo.Roles
 ******/
END
GO