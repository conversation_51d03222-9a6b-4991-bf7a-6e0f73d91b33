﻿using System;
using System.Linq;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DueTime.Queries
    {
    public class GetDueTimeReferenceByIdQuery : QueryBase<DueTimeData>
    {
        public Guid Id { get; set; }

        public GetDueTimeReferenceByIdQuery(Guid id)
    {
            Id = id;
        }
    }

    internal class GetDueTimeReferenceByIdQueryHandler : QueryHandlerBase<GetDueTimeReferenceByIdQuery, DueTimeData>
    {
        public GetDueTimeReferenceByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DueTimeData>> ExecuteAsync(GetDueTimeReferenceByIdQuery query)
    {
            var result = from dueRef in await EntitySet.GetAsync<DueTimeReferenceEntity>()
                         where dueRef.Id == query.Id
                         select new DueTimeData
    {
                             Id = dueRef.Id,
                             Name = dueRef.Name,
                             Duration = dueRef.Duration,
                             WorkingSaturday = dueRef.WorkingSaturday,
                             WorkingSunday = dueRef.WorkingSunday,
                             StartWorkingTime = dueRef.StartWorkingTime,
                             EndWorkingTime = dueRef.EndWorkingTime,
                             StartBreakTime = dueRef.StartBreakTime,
                             EndBreakTime = dueRef.EndBreakTime,
                             IsWorkingTime = dueRef.IsWorkingTime,
                             VacationDays = dueRef.VacationDays,
                             OvertimeDays = dueRef.OvertimeDays,
                             PercentPassedWhenAlertDue = dueRef.PercentPassedWhenAlertDue,
                             DefaultInheritedFromGlobalSettings = false
                         };
            return new QueryResult<DueTimeData>(result);
        }
    }
}

