
/****** Object:  StoredProcedure [telesale].[GetFieldSaleQuotaList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO




--ALTER PROCEDURE [telesale].[GetFieldSaleQuotaList]
CREATE PROCEDURE [telesale].[GetFieldSaleQuotaList]
	
	@UserLogin UNIQUEIDENTIFIER,
	@TeamId		UNIQUEIDENTIFIER,
	@MeetDate	DATETIME

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******	
	DECLARE @TeamList IdList;
	 IF (SELECT r.Name FROM dbo.UserInRoles uir
	 JOIN dbo.Roles r ON r.Id=uir.RoleId
	 WHERE uir.UserId=@UserLogin)='DMO Manager'
		IF @TeamId IS NULL
			INSERT INTO @TeamList SELECT o.Id FROM dbo.UserProfiles up JOIN dbo.Organization o ON up.OrganizationId=o.ParentId WHERE up.Id=@UserLogin;
		ELSE
			INSERT INTO @TeamList SELECT @TeamId Id;
	ELSE
		INSERT INTO @TeamList SELECT @TeamId Id;

	SELECT	ap.SeenByFieldSale, la.Id LeadAssignmentId, ap.Id AppointmentId, ap.ContactId, ap.AppointmentResultCodeId, l.UserDefinedFormatCode LeadCode, u.UserId FieldSaleId, ISNULL(up.FullName,u.UserName) FieldSaleName, ap.MeetDate, aprc.ResultCode, aprc.MeetStatus, aprc.LeadStatus AppointmentStatus, aprc.MaxRetryCount, d.DistrictName, p.ProvinceName, la.SuggestedFieldSaleId, la.SuggestedFieldSaleReason, ap.Status AppointmentStatus, aprc.ResultCode AppointmentResultCode, pr.IsHot, pr.HotListGroupId
	FROM	dbo.aspnet_Users u
			JOIN dbo.UserProfiles up ON up.Id = u.UserId
			JOIN dbo.UserInRoles uir ON uir.UserId = u.UserId
			JOIN dbo.LeadAssignment la ON la.AssignedFieldSaleId = u.UserId
			JOIN dbo.Lead l ON l.Id = la.LeadId
			JOIN dbo.Appointment ap ON ap.Id = la.WaitingAppointmentId
			JOIN dbo.Prospect pr ON pr.ContactId = l.ContactId
			LEFT JOIN dbo.AppointmentResultCode aprc ON aprc.Id = ap.AppointmentResultCodeId
			LEFT JOIN dbo.District d ON d.Id = ap.DistrictId
			LEFT JOIN dbo.Province p ON p.Id = ap.ProvinceId
	WHERE	up.OrganizationId IN (SELECT Id FROM @TeamList)
			AND uir.RoleId = 'A4095631-C225-4644-B39A-F5152F4A525C'
			AND ap.Status <> 4 AND ap.Status <> 5 -- Hẹn bị hủy, bị trả
			AND DATEDIFF(DAY, ap.MeetDate, @MeetDate) = 0
	ORDER BY ISNULL(up.FullName,u.UserName), ap.MeetDate
******/
END
GO