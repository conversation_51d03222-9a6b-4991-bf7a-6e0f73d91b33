
/****** Object:  StoredProcedure [testUtil].[CustomerImport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROC [testUtil].[CustomerImport]
	@GenerateExcelFileData INT = 0x0000,
	@GenerateExcelFileData_FillAllInfoFields bit = 1,

	@936ExistingCases_ExecuteUpdate BIT =1,
	@936ExistingCases_ExecuteUpdate_FillAllInfoFields BIT =1,
	@936ExistingCases_Show BIT =1,
	@replica INT = 1,
	@KeepTempTablesAfterFinish INT = 0
as
BEGIN

---- STEP 1. Create All cases matrix

IF OBJECT_ID('TestCustomer_Cases') IS NOT NULL DROP TABLE dbo.[TestCustomer_Cases]
CREATE TABLE [dbo].[TestCustomer_Cases](
       [Cif] [varchar](50) NULL,
       [Phone1] [varchar](50) NULL,
       [Phone2] [varchar](50) NULL,
       [Email] [varchar](50) NULL
) ON [PRIMARY]

INSERT [TestCustomer_Cases] ([Cif],[Phone1],[Phone2],[Email])
VALUES	 ('New','Existing.VL.P1','Existing.VL.P1','Existing.VL') 
		,('Existing','Existing.VL.P2','Existing.VL.P2','Existing.Core')
		,('Blank','Existing.Core.P1','Existing.Core.P1','New')
		,(NULL,'Existing.Core.P2','Existing.Core.P2','Blank')
		,(NULL,'Blank','Blank',NULL)
		,(NULL,'New','New',NULL)




---- STEP 2. Create records to import to cover all cases   432 cases



IF OBJECT_ID('TestCustomer_ImportedData') IS NOT NULL DROP TABLE dbo.TestCustomer_ImportedData
CREATE TABLE [dbo].TestCustomer_ImportedData
(
       [stt] [INT] IDENTITY(1,1) NOT NULL,
	   rep		 INT NULL,
	   --cases description fields
       [iCif] [VARCHAR](50) NULL,
       [iPhone1] [VARCHAR](50) NULL,
       [iPhone2] [VARCHAR](50) NULL,
       [iEmail] [VARCHAR](50) NULL,
	   -- sample value fields
       [Cif] [VARCHAR](20) NULL,
       [Phone1] [VARCHAR](50) NULL,
       [Phone2] [VARCHAR](50) NULL,
       [Email] [NVARCHAR](50) NULL
) ON [PRIMARY]

-- cross join all single field cases to have 432 cases
insert TestCustomer_ImportedData (icif, iphone1, iphone2, iEmail,rep)
select * FROM
	(SELECT Cif FROM TestCustomer_Cases WHERE Cif IS NOT NULL  ) c
CROSS JOIN
	(SELECT Phone1 FROM TestCustomer_Cases WHERE Phone1 IS NOT NULL) p1
CROSS JOIN
	(SELECT Phone2 FROM TestCustomer_Cases WHERE Phone2 IS NOT NULL) p2
CROSS JOIN
	(SELECT Email FROM TestCustomer_Cases  WHERE Email  IS NOT NULL) e
CROSS JOIN 
    (SELECT TOP (@replica) n AS n FROM sampledata.numbers ORDER BY n ) rep 
	
--SELECT COUNT(*) FROM dbo.TestCustomer_ImportedData
-- Generate sample data for each cases
-- make sure Phone1, Phone2 and Email is not duplicated

--UPDATE dbo.TestCustomer_ImportedData SET Cif=10000+stt WHERE iCif != 'Blank'
--UPDATE dbo.TestCustomer_ImportedData SET Phone1= CONCAT('0',909310000 +stt) WHERE iPhone1 != 'Blank'
--UPDATE dbo.TestCustomer_ImportedData SET Phone2= CONCAT('0',918665800 +stt) WHERE iPhone2 != 'Blank'
--UPDATE dbo.TestCustomer_ImportedData SET Email= CONCAT('cus', stt,'@test.com') WHERE iEmail != 'Blank'

UPDATE dbo.TestCustomer_ImportedData SET Cif=iif(iCif != 'Blank', 10000+stt,NULL)
										, Phone1= IIF(iPhone1 != 'Blank', CONCAT('0',909100000 +stt),NULL)    --CONCAT('0',909100000 +stt*rep)
										, Phone2= IIF(iPhone2 = 'Blank',NULL,CONCAT('0',918250000 +stt) )    --CONCAT('0',918250000 +stt*rep)
										, Email= IIF( iEmail = 'Blank',NULL,CONCAT('cus', stt,'@test.com')) 



IF (@GenerateExcelFileData>0)
begin

		------  Prepare user-friendly NAMES for data Sample 
		IF OBJECT_ID('SAMPLE_NAME') IS NOT NULL DROP TABLE SAMPLE_NAME
		CREATE TABLE SAMPLE_NAME
			(
				id         INT IDENTITY(1, 1) PRIMARY KEY,
				first_name NVARCHAR(max),
				mid_name   NVARCHAR(max),
				last_name  NVARCHAR(max)
			)
		INSERT INTO SAMPLE_NAME VALUES (N'Phùng',N'Lan',N'Chi'), (N'Vũ',N'Minh',N'Anh'), (N'Phạm',N'Hữu',N'Tân'), (N'Kim',N'Hải',N'Quân'), (N'Triệu',N'Tuấn',N'Hải'), (N'Chung',N'Duy',N'Thanh'), (N'Uất',N'Việt',N'Chính'), (N'Đỗ',N'Gia',N'Uy'), (N'Phó',N'Trung',N'Chính'), (N'Bùi',N'Khắc',N'Duy'), (N'Châu',N'Vĩnh',N'Hưng'), (N'Lê',N'Ngọc',N'Ẩn'), (N'Đặng',N'Mỹ',N'Dung'), (N'Tiêu',N'Vân',N'Khanh'), (N'Ngô',N'Tuyết',N'Lan'), (N'Nguyễn',N'Bích',N'Huệ'), (N'Đỗ',N'Thu',N'Huệ'), (N'Nguyễn',N'Thục',N'Vân'), (N'Hồ',N'Diễm',N'Châu'), (N'Nguyễn',N'Hoa',N'Thiên'), (N'Quang',N'Phương',N'Quyên'), (N'Ngô',N'Ái',N'Khanh'), (N'Đặng',N'Thiện',N'Dũng'), (N'Nguyễn',N'Thiếu',N'Anh'), (N'Châu',N'Chấn',N'Hùng'), (N'Tạ',N'Thế',N'Bình'), (N'Đỗ',N'Công',N'Lập'), (N'Đặng',N'Việt',N'Dũng'), (N'Uất',N'Quảng',N'Thông'), (N'Lạc',N'Khải',N'Hòa'), (N'Nguyễn',N'Tuấn',N'Chương'), (N'Nguyễn',N'Tiến',N'Hiệp')
		 IF OBJECT_ID('tempdb..#temp') IS NOT NULL DROP TABLE #temp

		CREATE TABLE #temp(fullname nvarchar(max))
		INSERT INTO #temp
		SELECT top 432 'test ' + st.first_name + N' ' + nd.mid_name + N' ' + rd.last_name 
		FROM ( SELECT first_name FROM dbo.SAMPLE_NAME) st
		CROSS JOIN (SELECT mid_name FROM dbo.SAMPLE_NAME) nd
		CROSS JOIN(SELECT last_name FROM dbo.SAMPLE_NAME) rd
		order by newid()

		--- Generate additional fields to match the import excel template
		IF OBJECT_ID('CustomerExcel') IS NOT NULL DROP TABLE CustomerExcel
		

		CREATE TABLE CustomerExcel
			(
				Name          NVARCHAR(50),
				CODE          INT,
				B2BCODE       INT,
				PHONE1        VARCHAR(50),
				PHONE2        VARCHAR(50),
				PHONE3        VARCHAR(50),
				CONTACTPHONE  VARCHAR(50),
				EMAIL         VARCHAR(50),
				FACEBOOKID    VARCHAR(50),
				WORKADDRESS   VARCHAR(50),
				JOB           VARCHAR(50),
				TYPE          INT,
				NOTES         VARCHAR(500),
				SUBNAME       VARCHAR(50),
				ADDRESS       VARCHAR(50),
				CUSTOMERCLASS VARCHAR(50),
				CREDITLIMIT   VARCHAR(50),
				SEX           VARCHAR(50),
				DOB           DATE,
				TAXNUMBER     VARCHAR(50),
				LICENSETYPE   VARCHAR(50),
				LICENSE       VARCHAR(50),
				LICENSEDATE   VARCHAR(50),
				AVATAR        VARCHAR(50),
				BACKGROUND    VARCHAR(50),
				LICENSEEXPIRE VARCHAR(50),
				LICENSEPLACE  VARCHAR(50),
				ORIGINNATION  VARCHAR(50),
				NATION        VARCHAR(50),
				BANKID        VARCHAR(50),
				LOCATIONID    VARCHAR(50),
				RESIDENCE     VARCHAR(50),
				STATUS        INT,
				CMND          VARCHAR(50),
				CifCase			VARCHAR(max)
			)

IF (@GenerateExcelFileData_FillAllInfoFields=1)
begin
		INSERT INTO CustomerExcel(Name, CODE, B2BCODE, PHONE1, PHONE2, PHONE3, CONTACTPHONE, EMAIL, FACEBOOKID, WORKADDRESS, JOB, TYPE, NOTES, SUBNAME, ADDRESS, CUSTOMERCLASS, CREDITLIMIT, SEX, DOB, TAXNUMBER, LICENSETYPE, LICENSE, LICENSEDATE, AVATAR, BACKGROUND, LICENSEEXPIRE, LICENSEPLACE, ORIGINNATION, NATION, BANKID, LOCATIONID, RESIDENCE, STATUS, CMND,CifCase)
		SELECT f.fullname+' ' + cast(c.stt/432 AS NVARCHAR(10) ) ,Cif,Cif, c.Phone1,c.Phone2,c.stt, c.stt, Email, c.stt, c.stt, c.stt, 1, CONCAT(c.iCif,':',c.iPhone1,':',c.iPhone2,':',c.iEmail) , c.stt, c.stt, c.stt, c.stt, 'Nam',dateadd(month, -1 * abs(convert(varbinary, newid()) % (90 * 12)), getdate()) , c.stt, c.stt, c.stt, c.stt, c.stt, c.stt, c.stt, c.stt, c.stt, c.stt, c.stt, c.stt, c.stt, 1, c.stt,c.iCif
		FROM dbo.TestCustomer_ImportedData c 
		JOIN 
			(SELECT TOP 432 ROW_NUMBER() OVER ( ORDER BY newid())  stt, fullname FROM #temp ) f ON f.stt%432 = c.stt%432
end
ELSE begin
		INSERT INTO CustomerExcel(CODE, PHONE1, PHONE2, EMAIL,Name,CifCase)
		SELECT Cif, c.Phone1,c.Phone2, Email,f.fullname+' ' + cast(c.stt/432 AS NVARCHAR(10)),c.iCif
		FROM dbo.TestCustomer_ImportedData c 
		JOIN 
			(SELECT TOP 432 ROW_NUMBER() OVER ( ORDER BY newid())  stt, fullname FROM #temp ) f ON f.stt%432 = c.stt%432
END

			
	
	PRINT ( @GenerateExcelFileData & 1)
	IF ( @GenerateExcelFileData & 1 =1)
	BEGIN
		SELECT '144 NEW CIF'
		SELECT * FROM CustomerExcel WHERE CifCase = 'New'
	END 

	IF ( @GenerateExcelFileData & 2 =2)
	BEGIN
		SELECT '144 EXISTING CIF'
		SELECT * FROM CustomerExcel WHERE CifCase = 'Existing'
	END
    
	IF ( @GenerateExcelFileData & 4 =4)
	BEGIN
		SELECT '144 VANG LAI'
		SELECT * FROM CustomerExcel WHERE CifCase = 'blank'
	END
    
	IF ( @GenerateExcelFileData & 8 =8)
	BEGIN
		SELECT 'ALL 432'
		SELECT * FROM CustomerExcel 
	END
    

		
		IF OBJECT_ID('SAMPLE_NAME') IS NOT NULL DROP TABLE SAMPLE_NAME

		IF @KeepTempTablesAfterFinish=0 IF OBJECT_ID('CustomerExcel') IS NOT NULL DROP TABLE CustomerExcel
END


-- STEP 4.  Modify database for 'existing' cases
-- Precondition: Current database have no duplicated cif, phone1, phone2, email with these testing customer data

IF @936ExistingCases_Show=1
BEGIN
    SELECT * FROM (
		SELECT stt excelRow ,'Phone1' excelColumn,iPhone1 CaseDescription, iif( substring(iPhone1,10,2) = 'VL',0,1) IsCoreCustomer, iif(iPhone1 like '%P2', 'Phone2', 'Phone1') Field, Phone1 ExistingValue from TestCustomer_ImportedData where iPhone1 like 'Existing%'
		union all
		select stt excelRow ,'Phone2' excelColumn,iPhone2 CaseDescription,iif( substring(iPhone2,10,2) = 'VL',0,1) IsCoreCustomer,  iif(iPhone2 like '%P2', 'Phone2', 'Phone1') Field, Phone2 ExistingValue from TestCustomer_ImportedData where iPhone2 like 'Existing%'
		union all
		select stt excelRow ,'Email' excelColumn,iEmail CaseDescription,iif( substring(iEmail,10,2) = 'VL',0,1) IsCoreCustomer,  'Email' Field, Email ExistingValue from TestCustomer_ImportedData where iEmail like 'Existing%'
		union all
		select stt excelRow ,'Cif' excelColumn,iCif CaseDescription,1, 'Code' Field, Cif ExistingCif from TestCustomer_ImportedData where iCif='Existing'
		)xx
end

		UPDATE cc 
		SET		cc.Notes = xx.UpdateStatement,  --CONCAT('row',xx.excelRow,' ',xx.excelColumn,' ',xx.CaseDescription), 
				cc.IsBackendCustomer=xx.IsCoreCustomer,
				cc.Phone1 = IIF(xx.Field='Phone1',xx.ExistingValue,cc.Phone1),
				cc.Phone2 = IIF(xx.Field='Phone2',xx.ExistingValue,cc.Phone2),
				cc.Email = IIF(xx.Field='Email',xx.ExistingValue,cc.Email),
				cc.Code = IIF(xx.Field='Code',xx.ExistingValue,cc.Code)


,B2BCODE		= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL, 'existing in db value'         )
,PHONE3			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'0909999999'					   )
,CONTACTPHONE	= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'0908888888'					   )
,FACEBOOKID		= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,WORKADDRESS	= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,JOB			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,TYPE			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,1,2							   )
--,NOTES			IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,= 'existing in db value'	   )
,SUBNAME		= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,ADDRESS		= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,CUSTOMERCLASS	= 0--IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,0							   )
,CREDITLIMIT	= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,10000000						   )
,SEX			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'Nu'							   )
,DOB			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,GETDATE()		   )
,TAXNUMBER		= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,LICENSETYPE	= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,LICENSE		= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,LICENSEDATE	= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,GETDATE()					   )
,AVATAR			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,BACKGROUND		= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,LICENSEEXPIRE	= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,GETDATE()					   )
,LICENSEPLACE	= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,ORIGINNATION	= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,NATION			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,BANKID			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,LOCATIONID		= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,RESIDENCE		= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )
,STATUS			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,1							   )
,CMND			= IIF(@936ExistingCases_ExecuteUpdate_FillAllInfoFields=0,NULL,'existing in db value'		   )







        FROM dbo.Customer cc JOIN
		( select top (936*@replica) id, ROW_NUMBER() over (order by (select 1)) RowNo from Customer c) c ON c.Id=cc.Id
		join
		(
		select x.*, ROW_NUMBER() over (order by (select 1) ) RowNo,  CONCAT('row ' ,excelrow,' ',excelColumn,' cif',x.iCif,' ',caseDescription) UpdateStatement
		from
		(
		select stt excelRow,iCif ,'Phone1' excelColumn,iPhone1 CaseDescription, iif( substring(iPhone1,10,2) = 'VL',0,1) IsCoreCustomer, iif(iPhone1 like '%P2', 'Phone2', 'Phone1') Field, Phone1 ExistingValue from TestCustomer_ImportedData where iPhone1 like 'Existing%'
		union all
		select stt excelRow,iCif ,'Phone2' excelColumn,iPhone2 CaseDescription,iif( substring(iPhone2,10,2) = 'VL',0,1) IsCoreCustomer,  iif(iPhone2 like '%P2', 'Phone2', 'Phone1') Field, Phone2 ExistingValue from TestCustomer_ImportedData where iPhone2 like 'Existing%'
		union all
		select stt excelRow,iCif ,'Email' excelColumn,iEmail CaseDescription,iif( substring(iEmail,10,2) = 'VL',0,1) IsCoreCustomer,  'Email' Field, Email ExistingValue from TestCustomer_ImportedData where iEmail like 'Existing%'
		union all
		select stt excelRow,iCif ,'Cif' excelColumn,iCif CaseDescription,1, 'Code' Field, Cif ExistingCif from TestCustomer_ImportedData where iCif='Existing'
		) x
		) xx on xx.rowNo = c.RowNo

	

IF OBJECT_ID('TestCustomer_Cases') IS NOT NULL DROP TABLE dbo.[TestCustomer_Cases]

END
GO