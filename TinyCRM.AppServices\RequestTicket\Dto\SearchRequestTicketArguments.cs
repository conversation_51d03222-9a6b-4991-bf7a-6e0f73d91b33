﻿using System;
using System.Collections.Generic;
using TinyCRM.Enums;

namespace TinyCRM.AppServices.RequestTicket.Dto
{
    public class SearchRequestTicketArguments
    {
        public Guid? Id { get; set; }

        public string Code { get; set; }

        public string CustomerCode { get; set; }

        public string CustomerName { get; set; }

        public string CMND { get; set; }

        public string ContactInfo { get; set; }

        public int? SourceChannel { get; set; }

        public Guid? CreatedBy { get; set; }

        public Guid? OwnerId { get; set; }

        public Guid? OwnerByOrganizationId { get; set; }

        public DateTime? CreatedDateFrom { get; set; }

        public DateTime? CreatedDateTo { get; set; }

        public DateTime? DueTimeProcessFrom { get; set; }

        public DateTime? DueTimeProcessTo { get; set; }

        public DateTime? TaskPlannedDateFrom { get; set; }

        public DateTime? TaskPlannedDateTo { get; set; }

        public DateTime? TaskPlannedProcessDueTimeFrom { get; set; }

        public DateTime? TaskPlannedProcessDueTimeTo { get; set; }

        public List<RequestTicketSearchStatus> TicketStatuses { get; set; }

        public Difficulty? DifficultyDegree { get; set; }

        public List<Guid> BehaviorClassifications { get; set; }

        public Guid? RegionId { get; set; }

        public Guid? AreaId { get; set; }

        public Guid? ProvinceId { get; set; }

        public Guid? DistrictId { get; set; }

        public Guid? WardId { get; set; }

        public Guid? Level1Id { get; set; }

        public Guid? Level2Id { get; set; }

        public Guid? Level3Id { get; set; }

        public Guid? Level4Id { get; set; }

        public Guid? ServiceTypeId { get; set; }

        public List<Guid> TicketBusinessResultId { get; set; }

        public List<Guid> TaskBusinessResultId { get; set; }

        public Guid? PartId { get; set; }

        public string PartCode { get; set; }

        public List<Guid> TaskTypeId { get; set; }

        public bool IsSearchTask { get; set; }
        public bool IsQueryActionTicket { get; set; }

        public List<TinyCRM.Enums.TaskStatus> TaskStatuses { get; set; }

        public Guid? TaskProcessByOrganizationId { get; set; }

        public Guid? TaskProcessByUserId { get; set; }

        public Guid CurrentUserId { get; set; }

        public int PageIndex { get; set; }

        public int PageSize { get; set; }

        public Guid? CampaignId { get; set; }

        public Guid? SearchCampaignId { get; set; }

        public bool? IncludeDataInCampaign { get; set; }

        public string SelectedDynamicFieldList { get; set; }

        public string SelectedDynamicColumnList { get; set; }

        public string TicketBusinessResultName { get; set; }

        public string TaskBusinessResultName { get; set; }

        public string SelectedTaskDynamicFieldList { get; set; }

        public string SelectedTaskDynamicColumnList { get; set; }

        public string Notes { get; set; }

        public Guid? EntityLinkBusinessSpecific { get; set; }

        public string DynamicFilterQuery { get; set; }
        public string SelectedDynamicFieldForEntity { get; set; }
        public string DynamicFilterTableQuery { get; set; }

        public string TaskDynamicFilterQuery { get; set; }
        public string TaskDynamicFilterTableQuery { get; set; }
        public string SearchOrderQuery { get; set; }
        public string PathService { get; set; }
        public Guid? GetTicketLinkedWithEntityLink { get; set; }
    }
}