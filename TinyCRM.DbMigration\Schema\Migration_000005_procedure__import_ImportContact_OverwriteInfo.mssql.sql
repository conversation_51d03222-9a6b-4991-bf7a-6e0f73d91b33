﻿
/****** Object:  StoredProcedure [import].[ImportContact_OverwriteInfo]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[ImportContact_OverwriteInfo]
	@importSessionId UNIQUEIDENTIFIER,
	@userId UNIQUEIDENTIFIER,
	@update AS dbo.stringList READONLY
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SET NOCOUNT ON;
	DECLARE @auditTime DATETIME = GETDATE();
	
	--audit entity
	INSERT INTO dbo.AuditEntityChange
	SELECT NEWID() Id, 'Update' Action, 'dbo.Contact' TableName, c.Id KeyValue, @userId ModifiedBy, @auditTime ModifiedDate, NULL
	FROM import.ContactRaw cr
	JOIN dbo.Contact c ON cr.ContactId = c.Id
	LEFT JOIN dbo.Prospect p ON p.ContactId = c.Id
	LEFT JOIN dbo.ProspectAssignment pa ON pa.Id = p.CurrentAssignmentId
	WHERE cr.ImportSessionId = @importSessionId
	AND cr.ErrorCode IN (SELECT Value FROM @update)
	AND (cr.[Source] IS NOT NULL AND (c.DataSource IS NULL OR cr.[Source] <> c.DataSource)
		OR cr.Note IS NOT NULL AND (c.Notes IS NULL OR cr.Note <> c.Notes))
	AND (p.Id IS NULL
		OR pa.Id IS NULL
		OR pa.AssignedTeamId IS NULL AND pa.AssignedAgentId IS NULL)

	--audit field DataSource
	INSERT INTO dbo.AuditFieldChange
	SELECT NEWID() Id, au.Id AuditId, 'DataSource' MemberName, c.DataSource OldValue, cr.Source NewValue
	FROM dbo.AuditEntityChange au
	JOIN dbo.Contact c ON au.KeyValue = c.Id
	JOIN import.ContactRaw cr ON c.Id = cr.ContactId
	WHERE au.TableName = 'dbo.Contact'
	AND au.Action = 'Update'
	AND au.ModifiedBy = @userId
	AND au.ModifiedDate = @auditTime
	AND cr.[Source] IS NOT NULL AND (c.DataSource IS NULL OR cr.[Source] <> c.DataSource)

	--audit field Note
	INSERT INTO dbo.AuditFieldChange
	SELECT NEWID() Id, au.Id AuditId, 'Notes' MemberName, c.Notes OldValue, cr.Note NewValue
	FROM dbo.AuditEntityChange au
	JOIN dbo.Contact c ON au.KeyValue=c.Id
	JOIN import.ContactRaw cr ON c.Id =cr.ContactId
	WHERE au.TableName = 'dbo.Contact'
	AND au.Action = 'Update'
	AND au.ModifiedBy = @userId
	AND au.ModifiedDate = @auditTime
	AND cr.Note IS NOT NULL AND (c.Notes IS NULL OR cr.Note <> c.Notes)

	--update data info
	UPDATE c SET
	c.FullName = IIF(cr.FullName IS NOT NULL, cr.FullName, c.FullName),
	c.ProvinceId = IIF(cr.ProvinceValid IS NOT NULL, cr.ProvinceValid, c.ProvinceId),
	c.Address = IIF(cr.Address IS NOT NULL, cr.Address, c.Address),
	c.Job = IIF(cr.Job IS NOT NULL, cr.Job, c.Job),
	c.DOB = IIF(cr.DOBValid IS NOT NULL, cr.DOBValid, c.DOB),
	c.Phone = IIF(cr.PhoneValid IS NOT NULL, cr.PhoneValid, c.Phone),
	c.Email = IIF(cr.EmailValid IS NOT NULL, cr.EmailValid, c.Email),
	c.CMND = IIF(cr.CMND IS NOT NULL, cr.CMND, c.CMND),
	c.MaritalStatus = IIF(cr.MaritalStatusValid IS NOT NULL, cr.MaritalStatusValid, c.MaritalStatus),
	c.Gender = IIF(cr.GenderValid IS NOT NULL, cr.GenderValid, c.Gender),
	c.BackSystemContactId = IIF(cr.BackSystemContactId IS NOT NULL, cr.BackSystemContactId, c.BackSystemContactId),
	c.QualifiedProgram = IIF(cr.QualifiedProgram IS NOT NULL, cr.QualifiedProgram, c.QualifiedProgram),
	c.CompanyName = IIF(cr.CompanyName IS NOT NULL, cr.CompanyName, c.CompanyName),
	c.CompanyAddress = IIF(cr.CompanyAddress IS NOT NULL, cr.CompanyAddress, c.CompanyAddress),
	c.CompanyType = IIF(cr.CompanyType IS NOT NULL, cr.CompanyType, c.CompanyType),
	c.CompanyPhone = IIF(cr.CompanyPhone IS NOT NULL, cr.CompanyPhone, c.CompanyPhone),
	c.Income = IIF(cr.IncomeValid IS NOT NULL, cr.IncomeValid, c.Income),
	c.IncomeSource = IIF(cr.IncomeSource IS NOT NULL, cr.IncomeSource, c.IncomeSource),
	c.Nationality = IIF(cr.Nationality IS NOT NULL, cr.Nationality, c.Nationality),
	c.AddressMailing = IIF(cr.AddressMailing IS NOT NULL, cr.AddressMailing, c.AddressMailing),
	c.AddressPermanent = IIF(cr.AddressPermanent IS NOT NULL, cr.AddressPermanent, c.AddressPermanent),
	c.DataSource = IIF(cr.Source IS NOT NULL, cr.Source, c.DataSource),
	c.Notes = IIF(cr.Note IS NOT NULL, cr.Note, c.Notes),
	c.AdditionalData = IIF(cr.AdditionalData IS NOT NULL, cr.AdditionalData, c.AdditionalData),
	c.AdditionalTemplateId = IIF(cr.AdditionalTemplateId IS NOT NULL, cr.AdditionalTemplateId, c.AdditionalTemplateId),
	c.DataQuality = IIF(cr.DataQualityValid IS NOT NULL, cr.DataQualityValid, c.DataQuality),
	c.CustomerId = IIF(cr.CustomerId IS NOT NULL, cr.CustomerId, c.CustomerId)
	FROM import.ContactRaw cr
	JOIN dbo.Contact c ON cr.ContactId = c.Id
	LEFT JOIN dbo.Prospect p ON p.ContactId = c.Id
	LEFT JOIN dbo.ProspectAssignment pa ON pa.Id = p.CurrentAssignmentId
	WHERE cr.ImportSessionId = @importSessionId
	AND (cr.ErrorCode IN (SELECT Value FROM @update))
	AND (p.Id IS NULL
		OR pa.Id IS NULL
		OR pa.AssignedTeamId IS NULL AND pa.AssignedAgentId IS NULL)
	
	--- Clean AdditionalTemplate

	DELETE FROM dbo.AdditionalDataTemplate
	WHERE Id IN 
		(SELECT t.Id FROM dbo.AdditionalDataTemplate t 
		LEFT JOIN dbo.Contact c ON t.Id = c.AdditionalTemplateId 
		WHERE c.Id IS NULL)
 ******/
END
GO