
/****** Object:  StoredProcedure [telesale].[SearchAssignedCampaignList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[SearchAssignedCampaignList]

	@AssignedTeamId		UNIQUEIDENTIFIER,
	@AssignedAgentId	UNIQUEIDENTIFIER,
	@CampaignName		NVARCHAR(500),
	@StartDate			DATETIME,
	@EndDate			DATETIME,
	@Status				INT,
	@StartRow			INT,
	@EndRow				INT,
	@CampaignType		INT

AS
BEGIN

	DECLARE @WhereString NVARCHAR(MAX) = N''
	SET @WhereString = @WhereString + IIF(@AssignedTeamId IS NULL, '', N' AND pa.AssignedTeamId = @AssignedTeamId ')
	SET @WhereString = @WhereString + IIF(@AssignedAgentId IS NULL, '', N' AND pa.AssignedAgentId = @AssignedAgentId ')
	SET @WhereString = @WhereString + IIF(ISNULL(@CampaignName,'')='', '', N' AND c.CampaignName LIKE N''%'' + @CampaignName + ''%'' ')
	SET @WhereString = @WhereString + IIF(@Status IS NULL, '', N' AND c.Status = @Status ')
	SET @WhereString = @WhereString + IIF(@CampaignType IS NULL, '', N' AND c.CampaignType = @CampaignType ')
	SET @WhereString = @WhereString + IIF(@StartDate IS NULL, '', N' AND c.StartDate >= @StartDate ')
	SET @WhereString = @WhereString + IIF(@EndDate IS NULL, '', N' AND c.EndDate < DATEADD(day, 1, @EndDate) ')

	DECLARE @ExecutedString NVARCHAR(MAX) = N'
	WITH cte AS
	(
		SELECT	c.Id CampaignId, c.Description ,c.CampaignName Name,c.CampaignType, c.StartDate, c.EndDate, c.Status, ISNULL(temp.TotalWork,0) TotalWork, ISNULL(temp.NewWork,0) NewWork, ISNULL(temp.AssignedTeamWork,0) AssignedTeamWork, ISNULL(temp.AssignedAgentWork,0) AssignedAgentWork, ISNULL(temp.ProgressWork,0) ProgressWork, ISNULL(temp.ClosedWork,0) ClosedWork,
				ROW_NUMBER() OVER (ORDER BY c.StartDate DESC, c.CreatedDate DESC) RowNumber
		FROM	dbo.Campaign c
				' + IIF(@AssignedTeamId IS NULL AND @AssignedAgentId IS NULL, 'LEFT', '') + ' JOIN
				(
					SELECT	c.Id CampaignId,
							COUNT(*) TotalWork,
							SUM(CASE WHEN pa.Status=1 THEN 1 ELSE 0 END) NewWork,
							SUM(CASE WHEN pa.AssignedTeamId IS NOT NULL THEN 1 ELSE 0 END) AssignedTeamWork,
							SUM(CASE WHEN pa.AssignedAgentId IS NOT NULL THEN 1 ELSE 0 END) AssignedAgentWork,
							SUM(CASE WHEN cr.FollowUpStatus IN (1,3) THEN 1 ELSE 0 END) ProgressWork,
							SUM(CASE WHEN cr.FollowUpStatus IN (2,4) THEN 1 ELSE 0 END) ClosedWork
					FROM	dbo.Campaign c
							JOIN dbo.ProspectAssignment pa ON pa.CampaignId = c.Id
							LEFT JOIN dbo.CallResult cr ON cr.Id = pa.CallResultId
					WHERE	c.Deleted = 0
							AND pa.IsNotCurrent = 0 
							' + @WhereString + '
					GROUP BY c.Id
				) temp ON temp.CampaignId = c.Id
		WHERE	c.Deleted = 0
				' + IIF(@AssignedTeamId IS NULL AND @AssignedAgentId IS NULL, @WhereString, '') + '
	)
	SELECT	*, (SELECT COUNT(*) FROM cte) TotalCount
	FROM	cte
	WHERE	cte.RowNumber BETWEEN @StartRow AND @EndRow'

	DECLARE @ParamDefinations NVARCHAR(MAX) = N'
	@AssignedTeamId		UNIQUEIDENTIFIER,
	@AssignedAgentId	UNIQUEIDENTIFIER,
	@CampaignName		NVARCHAR(500),
	@StartDate			DATETIME,
	@EndDate			DATETIME,
	@Status				INT,
	@StartRow			INT,
	@CampaignType		INT,
	@EndRow				INT'

	EXEC sp_executesql @ExecutedString, @ParamDefinations,
										@AssignedTeamId,	
										@AssignedAgentId,
										@CampaignName,	
										@StartDate,		
										@EndDate,		
										@Status	,		
										@StartRow,
										@CampaignType,
										@EndRow			

END
GO