﻿namespace Webaby
{
    public interface IDynamicProperty
    {
        Guid ObjectId { get; set; }

        Guid FormId { get; set; }

        Guid FieldId { get; set; }

        Guid? DynamicDefinedTableSchemaId { get; set; }

        string Name { get; set; }

        string DisplayName { get; set; }

        string AdditionalFilter { get; set; }

        string DataType { get; set; }

        Type Type { get; set; }

        string Value { get; set; }

        string ViewHint { get; set; }

        string SelectOptions { get; set; }

        int Order { get; set; }

        FieldType FieldType { get; set; }

        bool Display { get; set; }

        bool IsReadOnly { get; set; }

        bool IsRequired { get; set; }

        bool FreezeValue { get; set; }

        bool IsExportByConditionBoolean { get; set; }

        bool IsExportExcel { get; set; }

        string RequiredDependency { get; set; }

        string Inject { get; set; }

        Guid? DynamicFieldSectionId { get; set; }

        string DynamicFieldSectionName { get; set; }

        string Color { get; set; }

        string BackgroundColor { get; set; }

        Guid? RepresentationDynamicFieldId { get; set; }

        string VersionCode { get; set; }

        string CustomDependencies { get; set; }

        Dictionary<string, string> GetAdditionalMetadata();
    }
}