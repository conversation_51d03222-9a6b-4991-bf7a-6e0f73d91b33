
/****** Object:  StoredProcedure [telesale].[Contact_SearchContactList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[Contact_SearchContactList]

	@ProvinceId			UNIQUEIDENTIFIER,
	@DataSource			NVARCHAR(500),
	@IncomeFrom			INT,
	@IncomeTo			INT,
	@MaritalStatus		INT,
	@Gender				INT,
	@StartRow			INT,
	@EndRow				INT

AS
BEGIN

	WITH cte AS
	(
		SELECT	c.*, p.ProvinceName,
				ROW_NUMBER() OVER (ORDER BY c.ContactID) RowNumber
		FROM	dbo.Contact c
				LEFT JOIN dbo.Province p ON p.Id = c.ProvinceId
		WHERE	(@ProvinceId IS NULL OR c.ProvinceId = @ProvinceId)
				AND (@IncomeFrom IS NULL OR c.Income >= @IncomeFrom)
				AND (@IncomeTo IS NULL OR c.Income <= @IncomeTo)
				AND (@MaritalStatus IS NULL OR c.MaritalStatus = @MaritalStatus)
				AND (@Gender IS NULL OR c.Gender = @Gender)
				AND (@DataSource IS NULL OR @DataSource='' OR c.DataSource=@DataSource)
	)
	SELECT	*, (SELECT COUNT(*) FROM cte) TotalCount
	FROM	cte
	WHERE	cte.RowNumber BETWEEN @StartRow AND @EndRow

END
GO