﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;
using Webaby.Security;

namespace Webaby.Core.Access.Queries
{
    public class GetRoleBusinessPermissionByRoleIdQuery : QueryBase<RoleBusinessPermissionData>
    {
        public GetRoleBusinessPermissionByRoleIdQuery(Guid roleId)
        {
            RoleId = roleId;
        }

        public Guid RoleId { get; private set; }
    }

    internal class GetRoleBusinessPermissionByRoleIdQueryHandler :
        QueryHandlerBase<GetRoleBusinessPermissionByRoleIdQuery, RoleBusinessPermissionData>
    {
        public GetRoleBusinessPermissionByRoleIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<RoleBusinessPermissionData>> ExecuteAsync(GetRoleBusinessPermissionByRoleIdQuery query)
        {
            var entities = await EntitySet.Get<RoleBusinessPermissionEntity>().Where(x => x.RoleId == query.RoleId).ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<RoleBusinessPermissionData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
