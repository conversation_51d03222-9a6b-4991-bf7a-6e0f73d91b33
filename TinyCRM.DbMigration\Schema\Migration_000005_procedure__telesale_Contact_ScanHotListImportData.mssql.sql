
/****** Object:  StoredProcedure [telesale].[Contact_ScanHotListImportData]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[Contact_ScanHotListImportData]

	@ImportSessionId	UNIQUEIDENTIFIER

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	DELETE dbo.StagingContact WHERE	 ImportSessionId <> @ImportSessionId

	DELETE dbo.StagingContact WHERE ImportSessionId = @ImportSessionId AND ISNULL(FullName,'')='' AND ISNULL(Phone,'')=''

	-- Internal duplicated
	UPDATE	sc
    SET		DuplicatedCase = 1
    FROM	dbo.StagingContact sc
		    JOIN
		    (
			    SELECT	Id,
					    ROW_NUMBER() OVER (PARTITION BY Phone ORDER BY Id) RowNumber
			    FROM	dbo.StagingContact
			    WHERE	ImportSessionId = @ImportSessionId
						AND DuplicatedCase IS NULL
		    ) temp ON temp.Id = sc.Id
    WHERE	sc.ImportSessionId = @ImportSessionId
			AND temp.RowNumber > 1

	-- Scan imported data
	UPDATE sc 
	SET	DuplicatedCase = CASE
							WHEN p.Status = 4										THEN 3 -- Không tiềm năng
							WHEN p.Status = 3										THEN 2 -- Phân bổ lại
							WHEN pa.AssignedTeamId IS NOT NULL 
								 AND pa.AssignedAgentId IS NULL						THEN 4 -- Trong rổ sup
							WHEN pa.Ignore15DaysRule = 1
								 AND pa.AssignedTeamId IS NOT NULL
								 AND pa.AssignedAgentId IS NOT NULL					THEN 5 -- Trong rổ TMR, Ưu tiên
							WHEN pa.Ignore15DaysRule = 0
								 AND pa.AssignedTeamId IS NOT NULL
								 AND pa.AssignedAgentId IS NOT NULL
								 AND pa.Status = 1									THEN 6 -- Trong rổ TMR, Thường - chưa gọi
							WHEN pa.Ignore15DaysRule = 0
								 AND pa.AssignedTeamId IS NOT NULL
								 AND pa.AssignedAgentId IS NOT NULL
								 AND pa.Status = 2									THEN 7 -- Trong rổ TMR, Thường - đang theo
						END,
			CurrentContactId = c.Id,
			CurrentProspectId = p.Id,
			CurrentProspectAssignmentId = pa.Id,
			NewContactId = IIF(c.Id IS NULL, NEWID(), NULL),
			NewProspectId = IIF(p.Id IS NULL, NEWID(), NULL),
			NewProspectAssignmentId = IIF(pa.Id IS NULL, NEWID(), NULL)
	FROM	dbo.StagingContact sc
			LEFT JOIN dbo.Contact c ON c.Phone = sc.Phone
			LEFT JOIN dbo.Prospect p ON p.ContactId = c.Id
			LEFT JOIN dbo.ProspectAssignment pa ON pa.Id = p.CurrentAssignmentId
			--LEFT JOIN dbo.ContactCall cc ON cc.ProspectAssignmentId = pa.Id
	WHERE	sc.ImportSessionId = @ImportSessionId
			AND sc.DuplicatedCase IS NULL

	-- Các ProspectAssignment cần close, hoặc Prospect chưa có Current
	UPDATE	dbo.StagingContact
	SET		NewProspectAssignmentId = NEWID()
	WHERE	ImportSessionId = @ImportSessionId 
			AND DuplicatedCase IN (3, 4, 6) -- Các trường hợp lấy về

	UPDATE	dbo.StagingContact 
	SET		ToBeHotDistributePaId = ISNULL(NewProspectAssignmentId, CurrentProspectAssignmentId)
	WHERE	ImportSessionId = @ImportSessionId
			AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1, 5, 7)
******/
END
GO