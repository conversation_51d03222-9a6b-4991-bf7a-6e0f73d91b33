
/****** Object:  StoredProcedure [sampledata].[GenTicket_OwnershipData]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [sampledata].[GenTicket_OwnershipData]
	@replicateNumber INT=0,
	@exactNumerTicketToCreate INT = 0,
	@numberOfCustomer INT =1000
AS
BEGIN

IF(@exactNumerTicketToCreate=0) SET @exactNumerTicketToCreate= @replicateNumber*15
ELSE SET @replicateNumber = CEILING(   CAST(@exactNumerTicketToCreate AS FLOAT) / 15   )

DECLARE @customerReplica INT

SET @customerReplica = CEILING(   CAST(@exactNumerTicketToCreate AS FLOAT) / @numberOfCustomer   )

PRINT CONCAT('@replica ticket ',@replicateNumber,' replica customer ',@customerReplica)

EXEC sampledata.CreateNumberTable @maxNumber = @exactNumerTicketToCreate -- int

;
WITH cte AS
(    
				-- 5 một nhóm phiếu lưu hành nội bộ trong 150801 ,  150802
				-- 7 một nhóm phiếu lưu hành nội bộ trong 1509
		SELECT  1 AS GroupOrder, '0_cv01'		ti_createdBy ,'15_cvdd'		ti_ownedBy ,'1508_ptb'		ta1_asssignedTo ,'150801_cv02'  ta2_asssignedTo ,'150801_cvdd'	ta1_reviewedBy  
		
		UNION ALL
        SELECT  2				, '0_cv02'		ti_createdBy ,'15_cvdd'		ti_ownedBy ,'1508_ptb'		ta1_asssignedTo,'150801_cv01'	ta2_asssignedTo,'150801_cvdd'	ta1_reviewedBy  UNION all
        SELECT  3				, '0_cv01'		ti_createdBy ,'15_cvdd'		ti_ownedBy ,'1508_ptb'		ta1_asssignedTo ,'150802_cv02'	ta2_asssignedTo ,'150802_cvdd'	ta1_reviewedBy	UNION ALL 
        SELECT  4				, '0_cv02'		ti_createdBy ,'15_cvdd'		ti_ownedBy ,'1508_ptb'		ta1_asssignedTo ,'150802_cv01'	ta2_asssignedTo ,'150802_cvdd'  ta1_reviewedBy	UNION all
        SELECT  5				, '150801_cv01' ti_createdBy ,'150801_cv01' ti_ownedBy ,'150901_cv01'	ta1_asssignedTo ,'150901_cv01'	ta2_asssignedTo ,'150901_cvdd'  ta1_reviewedBy  UNION all
        SELECT  6				, '150802_cv01' ti_createdBy ,'150802_cv01' ti_ownedBy ,'150802_cv01'	ta1_asssignedTo ,'150802_cv01'	ta2_asssignedTo ,'150802_cvdd'  ta1_reviewedBy	Union all
        SELECT  7				, '1509_cv01'	ti_createdBy ,'1509_cv01'	ti_ownedBy ,'150901_cv01'	ta1_asssignedTo ,'150901_cv02'	ta2_asssignedTo ,'150901_cvdd'  ta1_reviewedBy	Union all
        SELECT  8				, '0_cv01'		ti_createdBy ,'14_cvdd'		ti_ownedBy ,'1408_ptb'		ta1_asssignedTo ,'140801_cv02'	ta2_asssignedTo ,'140801_cvdd'  ta1_reviewedBy	Union all
        SELECT  9				, '0_cv02'		ti_createdBy ,'14_cvdd'		ti_ownedBy ,'1408_ptb'		ta1_asssignedTo ,'140801_cv01'	ta2_asssignedTo ,'140801_cvdd'  ta1_reviewedBy	Union all
        SELECT 10				, '0_cv01'		ti_createdBy ,'14_cvdd'		ti_ownedBy ,'1408_ptb'		ta1_asssignedTo ,'140802_cv02'	ta2_asssignedTo , '140802_cvdd' ta1_reviewedBy	Union all
        SELECT 11				, '0_cv02'		ti_createdBy ,'14_cvdd'		ti_ownedBy ,'1408_ptb'		ta1_asssignedTo ,'140802_cv01'	ta2_asssignedTo ,'140802_cvdd'  ta1_reviewedBy	Union all
        SELECT 12				, '0_cv01'		ti_createdBy ,'14_cvdd'		ti_ownedBy ,'1408_ptb'		ta1_asssignedTo ,'140803_cv02'	ta2_asssignedTo ,'140803_cvdd'  ta1_reviewedBy	Union all
        SELECT 13				, '0_cv02'		ti_createdBy ,'14_cvdd'		ti_ownedBy ,'1408_ptb'		ta1_asssignedTo ,'140803_cv01'	ta2_asssignedTo ,'140803_cvdd'  ta1_reviewedBy	Union all
        SELECT 14				, '15_cv02'		ti_createdBy ,'15_ptb'		ti_ownedBy ,'15_cv01'		ta1_asssignedTo ,'15_cv01'		ta2_asssignedTo ,'15_cvdd'		ta1_reviewedBy	Union all
        SELECT 15				, '0_cv01'		ti_createdBy ,'14_cvdd'		ti_ownedBy ,'150802_cv01'	ta1_asssignedTo ,'140802_cv02'	ta2_asssignedTo , '140802_cvdd' ta1_reviewedBy
)
		SELECT 
				ti_createdBy ,
				ti_ownedBy ,
				ta1_asssignedTo ,
				ta2_asssignedTo ,
				ta1_reviewedBy, 
				CUS.Id,
				NEWID() ti_id,
				'00000000' ti_code ,
				NEWID() ti_acceptDue ,
				NEWID() ti_processDue ,
				NEWID() phaseId ,
				NEWID() ta1_Id ,
				NEWID() ta2_Id  

		FROM 
		(
			SELECT u1.UserId as ti_createdBy ,
				   u2.UserId as ti_ownedBy ,
				   u3.UserId as ta1_asssignedTo , 
				   u4.UserId as ta2_asssignedTo , 
		 		   u5.UserId as ta1_reviewedBy, 
				   ROW_NUMBER() OVER (ORDER BY (SELECT 1)) RowNo
			FROM cte c
			JOIN dbo.aspnet_Users u1 ON u1.UserName=c.ti_createdBy 
			JOIN dbo.aspnet_Users u2 ON u2.UserName=c.ti_ownedBy 
			JOIN dbo.aspnet_Users u3 ON u3.UserName=c.ta1_asssignedTo
			JOIN dbo.aspnet_Users u4 ON u4.UserName=c.ta2_asssignedTo
			JOIN dbo.aspnet_Users u5 ON u5.UserName=c.ta1_reviewedBy
			CROSS JOIN ( SELECT TOP (@replicateNumber) n FROM sampledata.numbers ORDER BY n )   nn
		) tic
		JOIN (
				SELECT c.Id, ROW_NUMBER() OVER (ORDER BY (SELECT 1) ) RowNo 
				FROM 
					(SELECT TOP (@numberOfCustomer) Id FROM dbo.Customer) c
					CROSS JOIN 
					(SELECT TOP (@replicateNumber) n FROM sampledata.numbers ORDER BY n) x
		) cus ON cus.RowNo = tic.RowNo

END
GO