﻿using System;

namespace TinyCRM.UserTaskAssignmentRouting.Queries
{
    public class UserTaskAssignmentRoutingListItem
    {
        public Guid Id { get; set; }

        public string TicketCreatedOrganizationType { get; set; }

        public string AssignmentType { get; set; }

        public Guid? TaskTypeId { get; set; }
        public string TaskType { get; set; }

        public Guid? Level1Id { get; set; }
        public string Level1 { get; set; }

        public Guid? Level2Id { get; set; }
        public string Level2 { get; set; }

        public Guid? Level3Id { get; set; }
        public string Level3 { get; set; }

        public Guid? Level4Id { get; set; }
        public string Level4 { get; set; }

        public Guid AssignedUserId { get; set; }
        public string AssignedUser { get; set; }

        public double? CalculatedPriority { get; set; }
    }
}