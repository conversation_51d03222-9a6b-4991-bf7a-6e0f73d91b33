﻿using System;
using System.ComponentModel;
using TinyCRM.Outbound.Lead;
using TinyCRM.Outbound.Prospect;
using Webaby.Data;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TinyCRM.Outbound.AppointmentResultCode
{
    [Table("AppointmentResultCode", Schema = "dbo")]
    public class AppointmentResultCodeEntity : IEntity {
        [Key]
        public Guid Id
        {
            get;
            set;
        }

        [Column]
        public string ResultCode
        {
            get;
            set;
        }

        [Column]
        public string DescriptionVn
        {
            get;
            set;
        }

        [Column]
        public string DescriptionEn
        {
            get;
            set;
        }

        [Column]
        public AppointmentStatus AppointmentStatus
        {
            get;
            set;
        }

        [Column]
        public LeadStatus LeadStatus
        {
            get;
            set;
        }

        [Column]
        public FollowUpAction FollowUpAction
        {
            get;
            set;
        }

        [Column]
        public int? MaxRetryCount
        {
            get;
            set;
        }

        [Column]
        public int DisplayOrder
        {
            get;
            set;
        }

        [Column]
        public Guid? ProspectResultId
        {
            get;
            set;
        }
    }

    public enum AppointmentStatus
    {
        [Description("Hủy")]
        Cancel = 1,
        [Description("Chờ kết quả")]
        PendingResult = 2,
        [Description("Bị trả, chưa gặp")]
        NotMeetReturn = 3,

        [Description("Chưa gặp")]
        NotMeet = 4, //3p
        [Description("Gặp - không tư vấn được")]
        MeetNotInterested = 5,
        [Description("Gặp - KH tiềm năng")]
        MeetInterested = 6, //1c

        [Description("Đóng hợp đồng")]
        MeetWin = 7,// 1W
        [Description("Thất bại")]
        Failed = 8 //2F
    }

    public enum FollowUpAction
    {
        ReturnTMR = 1,
        DMOFollowUp = 2
    }
}
