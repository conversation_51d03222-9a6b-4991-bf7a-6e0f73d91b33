
/****** Object:  StoredProcedure [telesale].[Contact_SearchContactsToAddToCampaign]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[Contact_SearchContactsToAddToCampaign]
	@CampaignId				UNIQUEIDENTIFIER,

	@ProvinceId				UNIQUEIDENTIFIER,
	@DataSource				NVARCHAR(500),
	@IncomeFrom				INT,
	@IncomeTo				INT,
	@MaritalStatus			INT,
	@Gender					INT,
	@Job					NVARCHAR(MAX),

	@SelectedCampaignId		UNIQUEIDENTIFIER,
	@ExcludeCampaignIds IdList	READONLY,
	@ResultCodeIds IdList	READONLY,

	@ImportSessionId		UNIQUEIDENTIFIER,

	@Limit					INT = NULL,
	@StartRow				INT,
	@EndRow					INT
AS
BEGIN
	SELECT * INTO #ResultCodeIds FROM @ResultCodeIds;
	SELECT * INTO #ExcludeCampaignIds FROM @ExcludeCampaignIds;
	DECLARE @fullQuery NVARCHAR(MAX)=
	N'
		SELECT '+IIF(@Limit IS NULL, '', ' TOP '+CAST(@Limit AS NVARCHAR(MAX))+' ')+'
		c.Id '+IIF(@StartRow IS NULL, '', ' ,ROW_NUMBER() OVER (ORDER BY (SELECT 1)) rn ')+'
		FROM dbo.Customer c
		'+IIF(@SelectedCampaignId IS NOT NULL OR (SELECT COUNT(Id) FROM #ResultCodeIds)>0,
			+IIF(@SelectedCampaignId=dbo.GuidEmpty(),
				' LEFT JOIN dbo.Prospect selectedCampaign ON c.Id=selectedCampaign.CustomerId ',
				' JOIN dbo.Prospect selectedCampaign ON c.Id=selectedCampaign.CustomerId AND selectedCampaign.CampaignId='''+CAST(@SelectedCampaignId AS NVARCHAR(MAX))+''' '),
			'')+'
		'+IIF((SELECT COUNT(Id) FROM #ExcludeCampaignIds)=0, '', ' LEFT JOIN dbo.Prospect excCampaign on c.Id=excCampaign.CustomerId LEFT JOIN #ExcludeCampaignIds exc on exc.Id=excCampaign.CampaignId ')+'
		'+IIF((SELECT COUNT(Id) FROM #ResultCodeIds)>0, ' JOIN #ResultCodeIds rc ON rc.Id=selectedCampaign.CallResultId ', '')+'
		'+IIF(@ImportSessionId IS NOT NULL, ' JOIN dbo.ImportCustomerRawLog crl ON c.Id=crl.CustomerId AND crl.ImportSessionId='''+CAST(@ImportSessionId AS NVARCHAR(MAX))+''' ', '')+'
		LEFT JOIN dbo.Prospect ownCampaign ON c.Id=ownCampaign.CustomerId AND ownCampaign.CampaignId='''+CAST(@CampaignId AS NVARCHAR(MAX))+'''
		WHERE c.IsDisabled = 0 AND ownCampaign.Id IS NULL
		'+IIF(@ProvinceId IS NULL, '', ' AND c.ProvinceId = '''+CAST(@ProvinceId AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@Job IS NULL, '', ' AND c.Job In (''' + REPLACE(@Job,',',''',''') + ''') ')+'
		'+IIF(@IncomeFrom IS NULL, '', ' AND c.Income >= '''+CAST(@IncomeFrom AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@IncomeTo IS NULL, '', ' AND c.Income <= '''+CAST(@IncomeTo AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@MaritalStatus IS NULL, '', ' AND c.MaritalStatus = '''+CAST(@MaritalStatus AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@Gender IS NULL, '', ' AND c.Gender = '''+CAST(@Gender AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@DataSource IS NULL OR @DataSource='', '', ' AND c.DataSource='''+CAST(@DataSource AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@SelectedCampaignId=dbo.GuidEmpty(), ' AND selectedCampaign.Id IS NULL ', '')+'
		'+IIF((SELECT COUNT(Id) FROM #ExcludeCampaignIds)>0, ' AND (excCampaign.Id IS NULL OR exc.Id IS NULL) ', '')+'
	';
	DECLARE @pagingQuery NVARCHAR(MAX)=
	N'
		WITH cte AS
		(
			'+@fullQuery+'
		)
		SELECT c.*, pv.ProvinceName, (SELECT COUNT(Id) FROM cte) TotalCount
		FROM cte
		JOIN dbo.Customer c ON c.Id = cte.Id
		LEFT JOIN dbo.Province pv ON c.ProvinceId=pv.Id
		WHERE cte.rn BETWEEN '''+CAST(@StartRow AS NVARCHAR(MAX))+''' AND '''+CAST(@EndRow AS NVARCHAR(MAX))+'''
	';
	--SELECT @fullQuery
	IF @StartRow IS NULL
		EXEC(@fullQuery);
	ELSE
		EXEC(@pagingQuery);
END
GO