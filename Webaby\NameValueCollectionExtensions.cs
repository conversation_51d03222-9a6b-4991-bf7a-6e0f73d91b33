﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Dynamic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Webaby
{
    public static class NameValueCollectionExtensions
    {
        public static String ToString(this NameValueCollection collection, string pairSeparator, string valueSeparator)
        {
            return string.Join(pairSeparator, (from string name in collection select string.Concat(name, valueSeparator, collection[name])).ToArray());
        }

        public static String Join(this NameValueCollection collection, Func<string, string> selector, string separator)
        {
            return String.Join(separator, collection.Cast<string>().Select(selector));
        }

        public static IDictionary<string, string> ToDictionary(this NameValueCollection col)
        {
            return col.AllKeys.ToDictionary(x => x, x => col[x]);
        }

        public static T Mapping<T>(this NameValueCollection col, T mapObject, Dictionary<string, string> mappings = null)
        {
            var objT = mapObject != null ? mapObject : Activator.CreateInstance(typeof(T));
            Type t = typeof(T);
            foreach (KeyValuePair<string, string> kvp in col.ToDictionary())
            {
                var key = kvp.Key;
                if (mappings != null && mappings.ContainsKey(kvp.Key))
                {
                    key = mappings[kvp.Key];
                }
                PropertyInfo pi = t.GetProperty(key);
                if (pi != null)
                {
                    var propType = pi.PropertyType;
                    try
                    {
                        object convertValue = propType.GetTypeConverter(string.IsNullOrEmpty(kvp.Value))
                            .ConvertFromString(null, CultureInfo.CurrentCulture, kvp.Value);
                        pi.SetValue(objT, convertValue, null);
                    }
                    catch
                    {
                        pi.SetValue(objT, propType.Default(), null);
                    }
                }
            }
            return (T)objT;
        }

        public static IEnumerable<NameValueCollection> SplitByKey(this NameValueCollection nv, string splitKey)
        {
            var result = new List<NameValueCollection>();
            var items = nv.AllKeys.Where(key => nv.GetValues(key) != null).SelectMany(nv.GetValues, (k, v) => new { key = k, value = v }).ToList();
            foreach (var item in items.Where(x => x.key.IsEqualIgnoreCase(splitKey)))
            {
                NameValueCollection nvn = new NameValueCollection();
                var values = item.value.Split(',');
                foreach (string value in values)
                {
                    nvn.Add(item.key, value);
                }
                result.Add(nvn);
            }
            return result;
        }

        public static void AddKeyValuePairs(this NameValueCollection nvc, Dictionary<string, object> dict, string prefix = null)
        {
            foreach (var k in dict)
            {
                var key = prefix == null ? k.Key : prefix + "[" + k.Key + "]";
                if (k.Value != null)
                    AddKeyValuePair(nvc, key, k.Value);
            }
        }

        private static void AddKeyValuePair(NameValueCollection nvc, string key, object value)
        {
            if (value is string || value.GetType().IsPrimitive)
            {
                nvc.Add(key, value.ToString());
            }
            else if (value is DateTime)
            {
                nvc.Add(key, ((DateTime)value).ToString("g"));
            }
            else
            {
                AddNonPrimitiveValue(nvc, key, value);
            }
        }

        private static void AddNonPrimitiveValue(NameValueCollection nvc, string key, object value)
        {
            var a = value as JArray;
            if (a != null)
            {
                for (int i = 0; i < a.Count; i++)
                    AddKeyValuePair(nvc, key + "[" + i + "]", a[i]);
            }
            else
            {
                var v = value as JValue;
                if (v != null)
                {
                    AddKeyValuePair(nvc, key, v.Value);
                }
                else
                {
                    var j = value as JObject;
                    if (j != null)
                        AddKeyValuePairs(nvc, j.ToObject<Dictionary<string, object>>(), key);
                }
            }
        }

        public static ExpandoObject ToExpando(this NameValueCollection col)
        {
            var dict = col.ToDictionary();
            var newDict = dict.ToDictionary(pair => pair.Key, pair => (object)pair.Value);
            var expandoObject = newDict.ToExpando();
            return expandoObject;
        }
    }
}