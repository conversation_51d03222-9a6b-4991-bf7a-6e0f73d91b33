
/****** Object:  StoredProcedure [telesale].[GetAppointmentTrackings]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[GetAppointmentTrackings]

	@AppointmentId		UNIQUEIDENTIFIER

AS
BEGIN

	SELECT	en.Id, en.KeyValue, en.Action, en.ModifiedBy ModifiedByUserName, ISNULL(up.FullName,u.UserName) ModifiedByUserName, en.ModifiedDate
	FROM	dbo.AuditEntityChange en
			JOIN dbo.aspnet_Users u ON u.UserId = en.ModifiedBy
			JOIN dbo.UserProfiles up ON up.Id = u.UserId
	WHERE	en.KeyValue = @AppointmentId
	UNION
	SELECT	en.Id, en.KeyValue, en.Action, en.ModifiedBy ModifiedByUserName, ISNULL(up.FullName,u.UserName) ModifiedByUserName, en.ModifiedDate
	FROM	dbo.AuditEntityChange en
			JOIN dbo.aspnet_Users u ON u.UserId = en.ModifiedBy
			JOIN dbo.UserProfiles up ON up.Id = u.UserId
			JOIN dbo.LeadAssignment la ON la.Id = en.KeyValue
	WHERE	la.WaitingAppointmentId = @AppointmentId
			AND en.Action <> 'Insert'
			AND EXISTS (SELECT * FROM dbo.AuditFieldChange WHERE AuditId = en.Id)
	ORDER BY en.ModifiedDate

END
GO