﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace Webaby.Core.Role.Queries
{
    public class GetRoleByNameQuery : QueryBase<RoleData>
    {
        public string RoleName { get; set; }
    }

    internal class GetRoleByNameQueryHandler : QueryHandlerBase<GetRoleByNameQuery, RoleData>
    {
        public GetRoleByNameQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<RoleData>> ExecuteAsync(GetRoleByNameQuery query)
        {
            var entities = await EntitySet.Get<RoleEntity>().Where(x => x.Name == query.RoleName).ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<RoleData>(x));
            return QueryResult.Create(mapped, query.Pagination);
        }
    }
}