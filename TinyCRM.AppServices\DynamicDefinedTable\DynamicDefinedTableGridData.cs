﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Globalization;
using System.Reflection;
using TinyCRM.AppServices.RequestTicket;
using TinyCRM.AppServices.RequestTicket.Dto;
using TinyCRM.DynamicDefinedTable.Commands;
using TinyCRM.DynamicDefinedTable.Queries;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.ServiceType.Queries;
using Webaby;
using Webaby.Core.DynamicForm;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Data;
using Webaby.EntityData;
using Webaby.Localization;
using Webaby.Security;
using Webaby.Web;

namespace TinyCRM.AppServices.DynamicDefinedTable
{
    public class DynamicDefinedTableGridData : IEntityData, IGridData, IUserDefinedTableGridData
    {
        private List<string> _htmlTableHeaders;

        public Guid Id { get; set; }

        public int PageIndex { get; set; }
        public int PageSize { get; set; }

        public Guid ReferenceObjectId { get; set; }

        public Guid OnDynamicFieldId { get; set; }

        public Guid DynamicDefinedTableSchemaId { get; set; }

        public List<DynamicDefinedTableCellValueData> ListItems { get; set; }

        public List<RequestTicketData> LinkedRequestTicketList { get; set; }
        public Dictionary<Guid, NameValueCollection> LinkedRequestTicketFormValuesList { get; set; }

        public Dictionary<int, Guid> TableRowIds { get; set; }

        public string SystemTableStatus { get; set; }

        public Dictionary<Guid, List<DynamicFieldValueInfo>> LinkedRequestTicketDynamicFieldValueList { get; set; }

        private List<RequestTicketCreateEditArguments> RequestTicketCreateEditArgumentsList { get; set; }

        private List<DynamicDefinedTableColumnData> _dynamicDefinedTableColumns;

        private readonly IQueryExecutor _queryExecutor;
        private readonly ICommandExecutor _commandExecutor;
        private readonly Lazy<IRequestTicketAppService> _requestTicketAppService;
        private readonly IUserService _userService;
        private readonly IMapper _mapper;
        private readonly IText _text;
        public DynamicDefinedTableGridData (
            IQueryExecutor queryExecutor, 
            ICommandExecutor commandExecutor, 
            Lazy<IRequestTicketAppService> requestTicketAppService, 
            IUserService userService, 
            IMapper mapper, 
            IText text
        )
        {
            if (_htmlTableHeaders == null)
            {
                _htmlTableHeaders = new List<string>();
            }
            _queryExecutor = queryExecutor;
            _commandExecutor = commandExecutor;
            _requestTicketAppService = requestTicketAppService;
            _userService = userService;
            _mapper = mapper;
            _text = text;
        }

        public bool TryMapping(string objectName, NameValueCollection values, HttpPostedFileCollection files, out Dictionary<string, List<string>> validateErrors)
        {
            validateErrors = new Dictionary<string, List<string>>();

            var idValue = values[objectName + ".Id"];
            if (idValue.IsNotNullOrEmpty())
            {
                this.Id = new Guid(idValue);
            }
            else
            {
                this.Id = Guid.NewGuid();
            }

            var pageIndexValue = values[objectName + ".PageIndex"];
            if (pageIndexValue.IsNotNullOrEmpty())
            {
                this.PageIndex = Int32.Parse(pageIndexValue);
            }

            var pageSizeValue = values[objectName + ".PageSize"];
            if (pageSizeValue.IsNotNullOrEmpty())
            {
                this.PageSize = Int32.Parse(pageSizeValue);
            }

            string dynamicDefinedTableSchemaIdString = values[string.Format("{0}.DynamicDefinedTableSchemaId", objectName)];
            if (dynamicDefinedTableSchemaIdString.IsNotNullOrEmpty())
            {
                DynamicDefinedTableSchemaId = new Guid(dynamicDefinedTableSchemaIdString);

                var requestTicketDataType = typeof(RequestTicketData);
                var requestTicketCreateEditArgumentsType = typeof(RequestTicketCreateEditArguments);

                GetDynamicDefinedTableColumnsIfNull();

                List<DynamicDefinedTableLinkedTicketColumnData> linkedTicketColumnList = new List<DynamicDefinedTableLinkedTicketColumnData>();
                foreach (var column in _dynamicDefinedTableColumns)
                {
                    if (column.DataType.ToType() == requestTicketDataType)
                    {
                        linkedTicketColumnList = _queryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery { DynamicDefinedTableLinkedColumnId = column.Id, OnDynamicFieldId = OnDynamicFieldId }).GetAwaiter().GetResult().ToList();
                    }
                }

                var rowCount = values.AllKeys.Count(x => x.Contains(string.Format("{0}.Items[", objectName)) && x.Contains("].RowNumber"));
                var listItems = new List<DynamicDefinedTableCellValueData>();

                List<RequestTicketCreateEditArguments> requestTicketCreateEditArgumentsList = new List<RequestTicketCreateEditArguments>();
                Dictionary<Guid, NameValueCollection> linkedRequestTicketFormValuesList = new Dictionary<Guid, NameValueCollection>();
                TableRowIds = new Dictionary<int, Guid>();

                var systemTableStatusName = string.Format("{0}.systemdynamictable.status", objectName);
                var systemTableStatusValue = values[systemTableStatusName];
                if (systemTableStatusValue.IsNotNullOrEmpty())
                {
                    SystemTableStatus = systemTableStatusValue;
                }
                else
                {
                    SystemTableStatus = "";
                }

                for (int rowNumber = 0; rowNumber < rowCount; rowNumber++)
                {
                    var rowItems = new List<DynamicDefinedTableCellValueData>();
                    var ticketCreateEditArgumentsId = Guid.NewGuid();
                    var listTypeCOl = _dynamicDefinedTableColumns.Select(x => x.DataType.ToType()).ToList();
                    var isExitsLinkTicketCol = listTypeCOl.Any(x => x == requestTicketDataType);

                    var rowIdKey = string.Format("{0}.Items[{1}].TableRowId", objectName, rowNumber);
                    var rowIdKeyValue = values[rowIdKey];
                    if (rowIdKeyValue.IsNotNullOrEmpty()) TableRowIds.Add(rowNumber, new Guid(rowIdKeyValue));

                    foreach (var column in _dynamicDefinedTableColumns)
                    {
                        var dataType = column.DataType.ToType();

                        //TableRowIds;
                        var columnIdString = values[string.Format("{0}.Items[{1}].{2}.Id", objectName, rowNumber, column.Name)];
                        string columnValueName = string.Format("{0}.Items[{1}].{2}.Value", objectName, rowNumber, column.Name);
                        string columnStringValue = values[columnValueName];

                        try
                        {
                            var typeConverter = dataType.GetTypeConverter(string.IsNullOrEmpty(columnStringValue));
                            var convertValue = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, columnStringValue);
                            if (convertValue != null)
                            {
                                if (convertValue is DateTime)
                                {
                                    var dateTime = (DateTime)convertValue;
                                    columnStringValue = dateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                                }
                                else
                                {
                                    columnStringValue = convertValue.ToString();
                                }
                            }
                        }
                        catch { }

                        var dynamicDefinedTableCellValueData = new DynamicDefinedTableCellValueData
                        {
                            Id = columnIdString.IsNotNullOrEmpty() ? new Guid(columnIdString) : Guid.NewGuid(),
                            DynamicDefinedTableColumnId = column.Id,
                            Value = columnStringValue,
                            RowNumber = rowNumber,
                            DynamicFieldValueId = Id
                        };

                        if (isExitsLinkTicketCol && dataType != requestTicketDataType)
                        {
                            NameValueCollection ticketFormValues = new NameValueCollection();
                            ticketFormValues.Add(column.Name, columnStringValue);

                            if (linkedRequestTicketFormValuesList.ContainsKey(ticketCreateEditArgumentsId))
                            {
                                var oldTicketFormValues = linkedRequestTicketFormValuesList[ticketCreateEditArgumentsId];
                                ticketFormValues.Add(oldTicketFormValues);
                                linkedRequestTicketFormValuesList[ticketCreateEditArgumentsId] = ticketFormValues;
                            }
                        }

                        if (dataType == requestTicketDataType)
                        {
                            RequestTicketCreateEditArguments ticketCreateEditArguments = new RequestTicketCreateEditArguments();
                            ticketCreateEditArguments.Id = ticketCreateEditArgumentsId;
                            ticketCreateEditArguments.ServiceTypeId = Guid.Parse(column.DefaultValue);
                            ticketCreateEditArguments.IsNew = true;

                            NameValueCollection ticketFormValues = new NameValueCollection();

                            string requestTicketIdString = values[string.Format("{0}.Items[{1}].{2}.LinkedTicket.Id", objectName, rowNumber, column.Name)];
                            if (requestTicketIdString.IsNotNullOrEmpty())
                            {
                                ticketCreateEditArguments.Id = Guid.Parse(requestTicketIdString);
                                ticketCreateEditArguments.IsNew = false;
                            }
                            dynamicDefinedTableCellValueData.Value = ticketCreateEditArguments.Id.ToString();

                            foreach (var linkedTicketColumn in linkedTicketColumnList)
                            {
                                string propertyValueKey = string.Format("{0}.Items[{1}].{2}.LinkedTicket.{3}", objectName, rowNumber, column.Name, linkedTicketColumn.PropertyName);
                                string requestTicketPropertyValueString = values[propertyValueKey];
                                ticketFormValues.Add(linkedTicketColumn.PropertyName, requestTicketPropertyValueString);

                                if (linkedTicketColumn.DynamicFieldId.IsNullOrEmpty())
                                {
                                    PropertyInfo piInstance = requestTicketCreateEditArgumentsType.GetProperty(linkedTicketColumn.PropertyName);

                                    var typeConverter = piInstance.PropertyType.GetTypeConverter(string.IsNullOrEmpty(requestTicketPropertyValueString));
                                    var convertValue = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, requestTicketPropertyValueString);

                                    piInstance.SetValue(ticketCreateEditArguments, convertValue);
                                }
                            }

                            requestTicketCreateEditArgumentsList.Add(ticketCreateEditArguments);
                            linkedRequestTicketFormValuesList.Add(ticketCreateEditArguments.Id.Value, ticketFormValues);
                        }

                        if (files != null)
                        {
                            #region Single File Dynamic Column

                            if (column.DataType.ToType() == ("Webaby.Core.File.Queries.FileData").ToType())
                            {
                                string currentFileIdString = values[string.Format("{0}.Items[{1}].{2}.FileId", objectName, rowNumber, column.Name)];
                                if (currentFileIdString.IsNotNullOrEmpty())
                                {
                                    dynamicDefinedTableCellValueData.Value = currentFileIdString;
                                }

                                IFormFile columnFileValue = files[string.Format("{0}.Items[{1}].{2}.Value", objectName, rowNumber, column.Name)] as IFormFile;
                                if (columnFileValue != null && columnFileValue.Length > 0)
                                {
                                    DynamicDefinedTableFileCellValue dynamicDefinedTableFileCellValue = new DynamicDefinedTableFileCellValue();
                                    dynamicDefinedTableFileCellValue.Id = Guid.NewGuid();
                                    dynamicDefinedTableFileCellValue.FileData = columnFileValue;

                                    dynamicDefinedTableCellValueData.Value = dynamicDefinedTableFileCellValue.Id.ToString();
                                    dynamicDefinedTableCellValueData.SingleFileData = dynamicDefinedTableFileCellValue;
                                }
                            }

                            #endregion

                            #region Multi File Dynamic Column

                            if (column.DataType.ToType() == ("System.Collections.Generic.List`1[Webaby.Core.File.Queries.FileData]").ToType())
                            {
                                List<Guid> existedFileIdList = new List<Guid>();

                                var splitNameValueList = values.SplitByKey(string.Format("{0}.Items[{1}].{2}.FileId", objectName, rowNumber, column.Name)).ToList();
                                for (int i = 0; i < splitNameValueList.Count; i++)
                                {
                                    var vls = splitNameValueList[i];

                                    var id = vls[string.Format("{0}.Items[{1}].{2}.FileId", objectName, rowNumber, column.Name)];
                                    if (!string.IsNullOrEmpty(id))
                                    {
                                        existedFileIdList.Add(new Guid(id));
                                    }
                                }

                                List<DynamicDefinedTableFileCellValue> fileDataList = new List<DynamicDefinedTableFileCellValue>();

                                var splitHttpPostFileList = files.HttpFileCollectionBase.SplitByKey(string.Format("{0}.Items[{1}].{2}.Value", objectName, rowNumber, column.Name)).ToList();
                                for (int i = 0; i < splitHttpPostFileList.Count; i++)
                                {
                                    var fls = splitHttpPostFileList[i];

                                    var fileItem = (IFormFile)fls[string.Format("{0}.Items[{1}].{2}.Value", objectName, rowNumber, column.Name)];
                                    if (fileItem != null && fileItem.Length > 0)
                                    {
                                        DynamicDefinedTableFileCellValue dynamicDefinedTableFileCellValue = new DynamicDefinedTableFileCellValue();
                                        dynamicDefinedTableFileCellValue.Id = Guid.NewGuid();
                                        dynamicDefinedTableFileCellValue.FileData = fileItem;

                                        fileDataList.Add(dynamicDefinedTableFileCellValue);
                                    }
                                }

                                existedFileIdList.AddRange(fileDataList.Select(t => t.Id));

                                dynamicDefinedTableCellValueData.Value = JsonConvert.SerializeObject(existedFileIdList);
                                dynamicDefinedTableCellValueData.MultiFileDataList = fileDataList;
                            }

                            #endregion
                        }

                        listItems.Add(dynamicDefinedTableCellValueData);

                        List<DynamicFieldValueInfo> dynamicFieldValueInfoList = new List<DynamicFieldValueInfo>();

                        DynamicFieldValueInfo dynamicFieldValueInfo = column.ToDynamicFieldValueInfo(_mapper);
                        dynamicFieldValueInfo.Name = columnValueName;

                        dynamicFieldValueInfoList.Add(dynamicFieldValueInfo);

                        NameValueCollection tempValues = values;

                        var isCollection = dataType.IsCollection();
                        if (dataType.IsEntityData())
                        {
                            tempValues = new NameValueCollection();
                            tempValues.Add(columnValueName + ".Id", columnStringValue);

                            if (column.DataType.ToType() == ("Webaby.Core.File.Queries.FileData").ToType())
                            {
                                tempValues = new NameValueCollection();

                                string currentFileIdString = values[string.Format("{0}.Items[{1}].{2}.FileId", objectName, rowNumber, column.Name)];
                                if (currentFileIdString.IsNotNullOrEmpty())
                                {
                                    tempValues = new NameValueCollection();
                                    tempValues.Add(columnValueName + ".Id", currentFileIdString);
                                }
                            }
                        }
                        else if (column.DataType.ToType() == ("System.Collections.Generic.List`1[Webaby.Core.File.Queries.FileData]").ToType())
                        {
                            tempValues = new NameValueCollection();

                            List<KeyValueItem> items = new List<KeyValueItem>();
                            foreach (var keyItem in values.AllKeys)
                            {
                                var valueItems = values.GetValues(keyItem);
                                if (valueItems != null)
                                {
                                    foreach (var valueItem in valueItems)
                                    {
                                        items.Add(new KeyValueItem { Key = keyItem, Value = valueItem });
                                    }
                                }
                            }

                            foreach (var item in items.Where(x => x.Key.IsEqualIgnoreCase(string.Format("{0}.Items[{1}].{2}.FileId", objectName, rowNumber, column.Name))))
                            {
                                var strValues = item.Key.Split(',');
                                foreach (string value in strValues)
                                {
                                    tempValues.Add(columnValueName + ".Id", value);
                                }
                            }
                        }
                        else if (isCollection)
                        {
                            List<string> valueStrList = columnStringValue.Split(',').ToList();
                            dynamicDefinedTableCellValueData.Value = JsonConvert.SerializeObject(valueStrList);
                        }

                        Dictionary<string, List<string>> columnValidateErrors;
                        if (!dynamicFieldValueInfoList.TryMapping(tempValues, (files == null ? null : files.HttpFileCollectionBase), "", _queryExecutor, _text, out columnValidateErrors))
                        {
                            validateErrors.AddRange(columnValidateErrors);
                        }
                    }
                }

                this.ListItems = listItems;
                this.RequestTicketCreateEditArgumentsList = requestTicketCreateEditArgumentsList;
                this.LinkedRequestTicketFormValuesList = linkedRequestTicketFormValuesList;
            }

            return !validateErrors.Any();
        }

        public async Task SaveAsync()
        {
            if (DynamicDefinedTableSchemaId != Guid.Empty)
            {
                bool hasLinkedTicketColumn = CheckExistLinkedTicketColumn();
                List<RequestTicketData> oldLinkedRequestTicketList = new List<RequestTicketData>();
                if (hasLinkedTicketColumn)
                {
                    List<DynamicDefinedTableCellValueData> oldListItems = LoadListItems();
                    oldLinkedRequestTicketList = LoadLinkedTicketList(oldListItems);
                }

                if (ListItems != null)
                {
                    foreach (var listItem in this.ListItems)
                    {
                        listItem.DynamicFieldValueId = this.Id;
                    }
                    var createEditListCommand = new CreateEditDynamicDefinedTableCellValueListCommand()
                    {
                        Id = this.Id,
                        PageIndex = this.PageIndex,
                        PageSize = this.PageSize,
                        ListItems = this.ListItems,
                        TableRowIds = this.TableRowIds,
                        SystemTableStatus = this.SystemTableStatus
                    };
                    await _commandExecutor.ExecuteAsync(createEditListCommand);

                    if (hasLinkedTicketColumn)
                    {
                        ServiceTypeData serviceTypeData = null;
                        if (RequestTicketCreateEditArgumentsList != null && RequestTicketCreateEditArgumentsList.Count > 0)
                        {
                            foreach (var arguments in RequestTicketCreateEditArgumentsList)
                            {
                                NameValueCollection ticketFormValues = new NameValueCollection();
                                if (LinkedRequestTicketFormValuesList.ContainsKey(arguments.Id.Value))
                                {
                                    ticketFormValues = LinkedRequestTicketFormValuesList[arguments.Id.Value];
                                }

                                if (!arguments.IsNew)
                                {
                                    var requestTicketData = await _queryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(arguments.Id.Value));
                                    if (requestTicketData != null)
                                    {
                                        arguments.DynamicFormValueId = requestTicketData.DynamicFormValueId;
                                    }
                                }
                                else
                                {
                                    if (ticketFormValues != null && ticketFormValues.Count > 0)
                                    {
                                        if (serviceTypeData == null)
                                        {
                                            serviceTypeData = await _queryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(arguments.ServiceTypeId));
                                        }

                                        if (serviceTypeData.DynamicFormId.IsNotNullOrEmpty())
                                        {
                                            arguments.DynamicFormId = serviceTypeData.DynamicFormId.Value;
                                            arguments.DynamicFormValueId = Guid.NewGuid();
                                        }
                                    }
                                }

                                if (arguments.OwnerId.IsNullOrEmpty())
                                {
                                    var currentUser = await _userService.GetCurrentUserAsync();
                                    if (currentUser != null && !currentUser.IsAnonymous)
                                    {
                                        arguments.OwnerId = currentUser.Id;
                                    }
                                }
                                if (arguments.Customer == null)
                                {
                                    arguments.IsNoneCustomerTicket = true;
                                }

                                if (arguments.IsNew)
                                {
                                    var ticketResult = _requestTicketAppService.Value.CreateRequestTicket(arguments, new List<InsertProductRequestTicketItem>(), ticketFormValues, null, false, false, true);
                                    if (!ticketResult.IsSuccess)
                                    {
                                        throw new Exception("Tạo phiếu liên kết bị lỗi. " + ticketResult.ErrorMessage);
                                    }
                                    else
                                    {
                                        //Phiếu được tạo ra từ column linked ticket , table trên field sẽ lấy default data trên dynamicForm.
                                        var ticket = await _queryExecutor.ExecuteOneAsync(new GetRequestTicketByCodeQuery { Code = ticketResult.RequestTicketCode });
                                        var dynamicFormValueId = ticket.DynamicFormValueId.Value;
                                        var dynamicFieldValueList = await _queryExecutor.ExecuteManyAsync(new GetDynamicFieldValueInfoQuery
                                        {
                                            DynamicFormValueId = dynamicFormValueId
                                        }).ToList();
                                        foreach (var item in dynamicFieldValueList)
                                        {
                                            var dataType = item.DataType.ToType();
                                            if (dataType.IsUserDefinedTableGridData() || dataType.IsGridData())
                                            {
                                                Guid? guidTableOnTask = null;
                                                if (item.DefaultValue.IsNotNullOrEmpty()) guidTableOnTask = Guid.Parse(item.DefaultValue);
                                                if (guidTableOnTask != null && item.DynamicDefinedTableSchemaId.HasValue)
                                                {
                                                    var tableSchemeId = item.DynamicDefinedTableSchemaId;
                                                    var field_table_ListItems = await _queryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery
                                                    {
                                                        DynamicFieldValueId = guidTableOnTask.Value,
                                                        DynamicDefinedTableSchemaId = tableSchemeId.Value
                                                    }).ToList();

                                                    foreach (var cell in field_table_ListItems)
                                                    {
                                                        cell.DynamicFieldValueId = item.Id.Value;
                                                    }

                                                    var createCellValueCommand = new CreateEditDynamicDefinedTableCellValueListCommand()
                                                    {
                                                        Id = item.Id.Value,
                                                        PageIndex = 0,
                                                        PageSize = 0,
                                                        ListItems = field_table_ListItems
                                                    };
                                                    await _commandExecutor.ExecuteAsync(createCellValueCommand);
                                                }
                                            }
                                        }
                                        // Get Source Linked Dynamic Fields - nguồn đối tượng
                                        var currentUser = _userService.GetCurrentUser();
                                        List<Guid> keys = new List<Guid>();
                                        keys.Add(currentUser.Id);
                                        keys.Add(ticket.CustomerId);
                                        keys.Add(ticket.Id);

                                        if (currentUser.OrganizationId.HasValue) keys.Add(currentUser.OrganizationId.Value);
                                        var role = await _queryExecutor.ExecuteManyAsync(new GetRoleByUserIdQuery { UserId = currentUser.Id }).OrderByDescending(x => x.Permissions).FirstOrDefault();
                                        if (role != null) keys.Add(role.Id);

                                        dynamicFieldValueList = await _queryExecutor.ExecuteManyAsync(new GetSourceValueOfDynamicFieldQuery() { DynamicFieldValueInfo = dynamicFieldValueList, Keys = keys });
                                        dynamicFieldValueList.MapTypeFromExtended();
                                        dynamicFieldValueList.Save(ticket.Id, "dbo.RequestTicket", null, false);
                                    }
                                }
                                else
                                {
                                    var ticketResult = Container.One<IRequestTicketAppService>().EditRequestTicket(arguments, new List<InsertProductRequestTicketItem>(), ticketFormValues, null);
                                    if (!ticketResult.IsSuccess)
                                    {
                                        throw new Exception("Phiếu liên kết bị lỗi. " + ticketResult.ErrorMessage);
                                    }
                                }
                            }
                        }

                        // Xóa các Linked-Ticket bị xóa trên row
                        foreach (var oldLinkedRequestTicket in oldLinkedRequestTicketList)
                        {
                            if (!RequestTicketCreateEditArgumentsList.Exists(rt => rt.Id == oldLinkedRequestTicket.Id))
                            {
                                Container.One<IRequestTicketAppService>().DeleteRequestTicket(oldLinkedRequestTicket.Id, true, true);
                            }
                        }

                        // Đồng bộ EntityLink
                        Container.One<ICommandExecutor>().Execute(new UpdateEntityLinkByLinkedTicketColumnCommand
                        {
                            DynamicFieldValueId = this.Id,
                            ReferenceObjectId = this.ReferenceObjectId,
                            CurrentUserId = Container.One<IUserService>().GetCurrentUser().Id,
                            LinkedTicketBusinessSpecificId = EntityLinkBusinessSpecificConstants.LinkedTicketBusinessSpecific.Id,
                            LinkedTicketIdList = RequestTicketCreateEditArgumentsList.Select(rt => rt.Id.Value).ToList(),
                        });
                    }
                }
            }
        }

        public async Task LoadAsync()
        {
            this.ListItems = LoadListItems();

            bool hasLinkedTicketColumn = CheckExistLinkedTicketColumn();
            if (hasLinkedTicketColumn)
            {
                Guid linkedTicketColumnId = GetLinkedTicketColumnId();
                List<DynamicDefinedTableLinkedTicketColumnData> linkedTicketColumnList = new List<DynamicDefinedTableLinkedTicketColumnData>();
                linkedTicketColumnList = Webaby.Container.One<IQueryExecutor>().ExecuteMany(new GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery { DynamicDefinedTableLinkedColumnId = linkedTicketColumnId }).ToList();

                LinkedRequestTicketList = LoadLinkedTicketList(this.ListItems);
                LinkedRequestTicketDynamicFieldValueList = new Dictionary<Guid, List<DynamicFieldValueInfo>>();
                foreach (var requestTicketData in LinkedRequestTicketList)
                {
                    if (requestTicketData.DynamicFormValueId.IsNotNullOrEmpty())
                    {
                        var dynamicFieldValueList = Webaby.Container.One<IQueryExecutor>().ExecuteMany(new GetDynamicFieldValueInfoQuery { DynamicFormValueId = requestTicketData.DynamicFormValueId.Value }).ToList();
                        foreach (var dynamicFieldValue in dynamicFieldValueList)
                        {
                            if (dynamicFieldValue.Inject.IsNullOrEmpty())
                            {
                                DynamicDefinedTableLinkedTicketColumnData definedTableLinkedTicketColumnData = linkedTicketColumnList.Where(col => col.PropertyName == dynamicFieldValue.Name).SingleOrDefault();
                                if (definedTableLinkedTicketColumnData != null && definedTableLinkedTicketColumnData.Inject.IsNotNullOrEmpty())
                                {
                                    dynamicFieldValue.Inject = definedTableLinkedTicketColumnData.Inject;
                                }
                            }
                        }

                        LinkedRequestTicketDynamicFieldValueList.Add(requestTicketData.Id, dynamicFieldValueList);
                    }
                }
            }
        }

        private List<DynamicDefinedTableCellValueData> LoadListItems()
        {
            var recordDataList = Container.One<IQueryExecutor>().Execute(new GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery { DynamicFieldValueId = this.Id, DynamicDefinedTableSchemaId = this.DynamicDefinedTableSchemaId }).Many.ToList();
            return recordDataList.ToList();
        }

        private List<RequestTicketData> LoadLinkedTicketList(List<DynamicDefinedTableCellValueData> listItems)
        {
            Guid linkedTicketColumnId = GetLinkedTicketColumnId();

            var linkedRequestTicketList = new List<RequestTicketData>();
            if (linkedTicketColumnId != Guid.Empty)
            {
                foreach (var listItem in listItems)
                {
                    if (listItem.DynamicDefinedTableColumnId == linkedTicketColumnId)
                    {
                        RequestTicketData requestTicketData = Webaby.Container.One<IQueryExecutor>().ExecuteOne(new GetRequestTicketByIdQuery(Guid.Parse(listItem.Value)));
                        linkedRequestTicketList.Add(requestTicketData);
                    }
                }
            }

            return linkedRequestTicketList;
        }

        public bool HasValue(string objectName, NameValueCollection values, HttpPostedFileCollection files)
        {
            string dynamicDefinedTableSchemaIdString = values[string.Format("{0}.DynamicDefinedTableSchemaId", objectName)];
            DynamicDefinedTableSchemaId = new Guid(dynamicDefinedTableSchemaIdString);

            var columnList = Webaby.Container.One<IQueryExecutor>().ExecuteMany(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = DynamicDefinedTableSchemaId }).ToList();

            var rowCount = values.AllKeys.Count(x => x.Contains(string.Format("{0}.Items[", objectName)) && x.Contains("].RowNumber"));
            var listItems = new List<DynamicDefinedTableCellValueData>();

            for (int rowNumber = 0; rowNumber < rowCount; rowNumber++)
            {
                var rowItems = new List<DynamicDefinedTableCellValueData>();

                foreach (var column in columnList)
                {
                    var columnIdString = values[string.Format("{0}.Items[{1}].{2}.Id", objectName, rowNumber, column.Name)];
                    string columnValueString = values[string.Format("{0}.Items[{1}].{2}.Value", objectName, rowNumber, column.Name)];

                    if (columnValueString.IsNotNullOrEmpty())
                    {
                        var dynamicDefinedTableCellValueData = new DynamicDefinedTableCellValueData
                        {
                            Id = columnIdString.IsNotNullOrEmpty() ? new Guid(columnIdString) : Guid.NewGuid(),
                            DynamicDefinedTableColumnId = column.Id,
                            Value = columnValueString,
                            RowNumber = rowNumber,
                            DynamicFieldValueId = Id
                        };

                        listItems.Add(dynamicDefinedTableCellValueData);
                    }
                }
            }

            return listItems.Count > 0 ? true : false;
        }

        public Dictionary<string, string> GetAdditionalMetadata(Guid? dynamicFieldDefinitionId = null, Guid? dynamicFieldValueId = null)
        {
            return new Dictionary<string, string>();
        }

        public List<DynamicDefinedTableCellValueBase> GetBaseListItems()
        {
            return Mapper.Map<List<DynamicDefinedTableCellValueBase>>(ListItems);
        }

        public string DisplayContent(string templateHint, bool isHtml = false)
        {
            return string.Empty;
        }

        public IEnumerable<string> GetPropertyValues(string propertyName)
        {
            return null;
        }

        public Type GetPropertyType(string propertyName)
        {
            var columnList = Webaby.Container.One<IQueryExecutor>().ExecuteMany(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = DynamicDefinedTableSchemaId });
            DynamicDefinedTableColumnData column = columnList.Where(c => c.Name.IsEqualIgnoreCase(propertyName)).FirstOrDefault();

            if (column != null)
            {
                return Type.GetType(column.DataType);
            }
            return null;
        }

        public string GetValue()
        {
            return Id.ToString();
        }

        public bool ExecuteExpression(string expression)
        {
            return true;
        }

        public IEnumerable<IEntity> CloneReferenceEntities(Guid sourceValueGroupId, Guid destinationValueGroupId)
        {
            var entityQuery = Container.One<IEntitySet>().Get<DynamicDefinedTableCellValueEntity>();
            var dataEntities = (from dfv in entityQuery
                                where dfv.DynamicFieldValueId == sourceValueGroupId
                                select new DynamicDefinedTableCellValueData
                                {
                                    Id = dfv.Id,
                                    DynamicFieldValueId = destinationValueGroupId,

                                    DynamicDefinedTableColumnId = dfv.DynamicDefinedTableColumnId,
                                    RowNumber = dfv.RowNumber,
                                    Value = dfv.Value

                                }).ToList();

            List<DynamicDefinedTableCellValueEntity> entities = Mapper.Map<List<DynamicDefinedTableCellValueEntity>>(dataEntities);
            foreach (var entity in entities)
            {
                entity.Id = Guid.NewGuid();
                entity.IsNew = true;
            }

            return entities;
        }

        public IEntityData CopyDynamicFieldValue()
        {
            DynamicDefinedTableGridData newField = new DynamicDefinedTableGridData();
            newField.Id = Guid.NewGuid();
            newField.ListItems = ListItems;
            if (ListItems != null && ListItems.Count > 0)
            {
                foreach (var listItem in newField.ListItems)
                {
                    listItem.Id = Guid.NewGuid();
                }
            }
            return newField;
        }

        public IEntityData CopyDynamicFieldValueByName(Guid toDynamicDefinedTableSchemaId, Guid? toDynamicFieldValueId, string toDynamicFieldValue)
        {
            var fromDynamicDefinedTableColumnList = Webaby.Container.One<IQueryExecutor>().ExecuteMany(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = this.DynamicDefinedTableSchemaId }).ToList();
            var toDynamicDefinedTableColumnList = Webaby.Container.One<IQueryExecutor>().ExecuteMany(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = toDynamicDefinedTableSchemaId }).ToList();

            DynamicDefinedTableGridData newEntityInstance = new DynamicDefinedTableGridData();
            newEntityInstance.Id = Guid.NewGuid();
            newEntityInstance.ListItems = new List<DynamicDefinedTableCellValueData>();
            if (toDynamicFieldValue.IsNotNullOrEmpty())
            {
                newEntityInstance.Id = toDynamicFieldValueId.Value;
                newEntityInstance.DynamicDefinedTableSchemaId = toDynamicDefinedTableSchemaId;
                newEntityInstance.Load();
            }

            if (ListItems != null && ListItems.Count > 0)
            {
                var fromFullInfoListItems = (from li in ListItems
                                             join cl in fromDynamicDefinedTableColumnList on li.DynamicDefinedTableColumnId equals cl.Id
                                             select new
                                             {
                                                 DynamicDefinedTableCellValueData = li,
                                                 DynamicDefinedTableColumn = cl
                                             }).ToList();

                var toFullInfoListItems = (from li in newEntityInstance.ListItems
                                           join cl in toDynamicDefinedTableColumnList on li.DynamicDefinedTableColumnId equals cl.Id
                                           select new
                                           {
                                               DynamicDefinedTableCellValueData = li,
                                               DynamicDefinedTableColumn = cl
                                           }).ToList();

                foreach (var listItem in fromFullInfoListItems)
                {
                    var toDynamicDefinedTableColumn = toDynamicDefinedTableColumnList.Where(col => col.Name == listItem.DynamicDefinedTableColumn.Name).FirstOrDefault();
                    if (toDynamicDefinedTableColumn != null && toDynamicDefinedTableColumn.DataType.IsEqualIgnoreCase(listItem.DynamicDefinedTableColumn.DataType))
                    {
                        DynamicDefinedTableCellValueData dynamicDefinedTableCellValue = (from li in toFullInfoListItems
                                                                                         where li.DynamicDefinedTableColumn.Name == listItem.DynamicDefinedTableColumn.Name
                                                                                         && li.DynamicDefinedTableCellValueData.RowNumber == listItem.DynamicDefinedTableCellValueData.RowNumber
                                                                                         select li.DynamicDefinedTableCellValueData).FirstOrDefault();
                        if (dynamicDefinedTableCellValue != null)
                        {
                            dynamicDefinedTableCellValue.Value = listItem.DynamicDefinedTableCellValueData.Value;
                        }
                        else
                        {
                            dynamicDefinedTableCellValue = listItem.DynamicDefinedTableCellValueData;
                            dynamicDefinedTableCellValue.Id = Guid.NewGuid();
                            dynamicDefinedTableCellValue.DynamicFieldValueId = newEntityInstance.Id;
                            dynamicDefinedTableCellValue.DynamicDefinedTableColumnId = toDynamicDefinedTableColumn.Id;
                            dynamicDefinedTableCellValue.RowNumber = listItem.DynamicDefinedTableCellValueData.RowNumber;

                            newEntityInstance.ListItems.Add(dynamicDefinedTableCellValue);
                        }
                    }
                }
            }

            return newEntityInstance;
        }

        private Guid GetLinkedTicketColumnId()
        {
            GetDynamicDefinedTableColumnsIfNull();
            foreach (var column in _dynamicDefinedTableColumns)
            {
                if (column.DataType.ToType() == typeof(RequestTicketData))
                {
                    return column.Id;
                }
            }
            return Guid.Empty;
        }

        private bool CheckExistLinkedTicketColumn()
        {
            GetDynamicDefinedTableColumnsIfNull();
            foreach (var column in _dynamicDefinedTableColumns)
            {
                if (column.DataType.ToType() == typeof(RequestTicketData))
                {
                    return true;
                }
            }
            return false;
        }

        private void GetDynamicDefinedTableColumnsIfNull()
        {
            if (_dynamicDefinedTableColumns == null)
            {
                _dynamicDefinedTableColumns = Webaby.Container.One<IQueryExecutor>().ExecuteMany(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = DynamicDefinedTableSchemaId }).ToList();
            }
        }

        private class KeyValueItem
        {
            public string Key { get; set; }

            public string Value { get; set; }
        }
    }
}