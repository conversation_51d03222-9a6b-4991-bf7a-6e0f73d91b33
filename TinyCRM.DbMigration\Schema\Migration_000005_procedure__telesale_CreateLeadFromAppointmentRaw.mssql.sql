
/****** Object:  StoredProcedure [telesale].[Create<PERSON>ead<PERSON>romAppointmentRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[CreateLeadFromAppointmentRaw]
	@UserId UNIQUEIDENTIFIER,
	@SessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	INSERT INTO dbo.Lead
	        ( Id ,
	          CurrentLeadAssignmentId ,
	          CreatedDate ,
	          CreatedBy ,
	          ProductId ,
	          CreatedByProspectAssignmentId ,
	          [Status] ,
	          ModifiedBy ,
	          ModifiedDate ,
	          CreatedByContactCallId ,
	          ProductBudget ,
	          ExternalOldLeadCode ,
	          ContactId
	        )
	SELECT
			  ar.LeadId Id,
			  ar.LeadAssignmentId CurrentLeadAssignmentId,
			  GETDATE() CreatedDate,
			  tmr.Id CreatedBy,
			  NULL ProductId, --------------------------------------------NOTE
			  ar.ProspectAssignmentId CreatedByProspectAssignmentId,
			  1 [Status],
			  NULL ModifiedBy,
			  NULL ModifiedDate,
			  ar.ContactCallId CreatedByContactCallId,
			  ar.ProductValue ProductBudget,
			  ar.LeadCode ExternalOldLeadCode,
			  ar.ContactId
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles tmr ON ar.CodeTMR=tmr.AgentCode
	WHERE ar.IsInvalid <> 1 AND ar.ImportSessionId=@SessionId
	AND (NOT (ar.IsDupPa=1 AND ar.IsDupApp=1))

	UPDATE	l
	SET		l.UserDefinedFormatCode=FORMAT(l.CreatedDate,'yyMM') + RIGHT('00000' + CAST(l.LeadCode AS VARCHAR(MAX)), 5)
	FROM	dbo.Lead  l
			JOIN dbo.AppointmentRaw ar ON ar.LeadId=l.Id
END
GO