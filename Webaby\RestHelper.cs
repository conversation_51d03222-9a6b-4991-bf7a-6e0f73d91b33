﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using RestSharp.Deserializers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Dynamic;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;

namespace Webaby
{
    public class RestHelper<T> : object where T : new ()
    {
        private static IConfiguration _configuration;

        public RestHelper(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public static string ApiPostJsonCall(string url, string method, string input, Dictionary<string, string> headers = null)
        {
            var client = new RestClient(url);            
            if (_configuration.GetValue<string>("request.timeout") != null)
            {
                client.Timeout = int.Parse(_configuration.GetValue<string>("request.timeout"));
            }
            var request = new RestRequest(method, Method.POST);

            request = new RestRequest(method) { Method = Method.POST };
            client.ClearHandlers();
            client.AddHandler("application/json", new JsonDeserializer());
            request.Parameters.Clear();
            request.AddParameter("application/json; charset=utf-8", input, ParameterType.RequestBody);

            if (headers != null && headers.Count > 0)
            {
                foreach (KeyValuePair<string, string> keyValue in headers)
                {
                    request.AddHeader(keyValue.Key, keyValue.Value);
                }
            }

            var response = client.Execute(request);
            client.EnsureResponseWasSuccessful(request, response);
            return response.Content;
        }

        public static T ApiPostCall(string url, string method, object input, RestBodyOptions option, IEnumerable<String> urlSegment = null, Dictionary<string, string> headers = null)
        {
            var client = new RestClient(url);
            if(_configuration.GetValue<string>("request.timeout") != null)
            {
                client.Timeout = int.Parse(_configuration.GetValue<string>("request.timeout"));
            }
            var request = new RestRequest(method, Method.POST);
            Type inputType = input.GetType();
            var urlSegmentList = urlSegment == null ? new List<string>() : (urlSegment.ToList());
            if (option == RestBodyOptions.Params)
            {
                request = new RestRequest(method, Method.POST);
                if (input is ExpandoObject)
                {
                    IDictionary<string, object> propValues = (ExpandoObject)input;
                    foreach (var prop in propValues.Keys)
                    {
                        if (propValues[prop] is IFormFile)
                        {
                            var file = propValues[prop] as IFormFile;
                            var mem = new MemoryStream();                            
                            file.CopyToAsync(mem).GetAwaiter().GetResult();
                            request.AddFile(prop, mem.ToArray(), file.FileName);
                        }
                        else
                        {
                            if (urlSegment != null && urlSegmentList.Any() && urlSegmentList.Contains(prop))
                            {
                                request.AddUrlSegment(prop, propValues[prop].ToString());
                            }
                            else
                            {
                                request.AddParameter(prop, propValues[prop].ToString());
                            }
                        }
                    }
                }
                else
                {
                    foreach (var propInfo in inputType.GetProperties())
                    {
                        if (propInfo.CanRead)
                        {
                            object inputValue = propInfo.GetValue(input, null);
                            if (inputValue is IFormFile)
                            {
                                var file = inputValue as IFormFile;
                                var mem = new MemoryStream();
                                file.CopyToAsync(mem).GetAwaiter().GetResult();
                                request.AddFile(propInfo.Name, mem.ToArray(), file.FileName);
                            }
                            else
                            {
                                if (urlSegment != null && urlSegmentList.Any() && urlSegmentList.Contains(propInfo.Name))
                                {
                                    request.AddUrlSegment(propInfo.Name, inputValue.ToString());
                                }
                                else
                                {
                                    request.AddParameter(propInfo.Name, inputValue);
                                }
                            }
                        }
                    }
                }
                request.AlwaysMultipartFormData = true;
            }
            else if (option == RestBodyOptions.Json)
            {
                request = new RestRequest(method) { Method = Method.POST };
                client.ClearHandlers();
                client.AddHandler("application/json", new JsonDeserializer());
                request.Parameters.Clear();
                request.AddParameter("application/json; charset=utf-8", JsonConvert.SerializeObject(input),
                    ParameterType.RequestBody);
            }

            if (headers != null && headers.Count > 0)
            {
                foreach (KeyValuePair<string, string> keyValue in headers)
                {
                    request.AddHeader(keyValue.Key, keyValue.Value);
                }
            }

            var response = client.Execute(request);
            client.EnsureResponseWasSuccessful(request, response);
            return JsonConvert.DeserializeObject<T>(response.Content);
        }

        public static T ApiGetCall(string url, string method, object input)
        {
            var client = new RestClient(url);
            var request = new RestRequest(method, Method.GET);
            Type inputType = input.GetType();
            foreach (var propInfo in inputType.GetProperties())
            {
                if (propInfo.CanRead)
                {
                    object inputValue = propInfo.GetValue(input, null);
                    request.AddUrlSegment(propInfo.Name, inputValue.ToString());
                }
            }
            var response = client.Execute(request);
            client.EnsureResponseWasSuccessful(request, response);
            return JsonConvert.DeserializeObject<T>(response.Content);
        }
    }

    public enum RestBodyOptions
    {
        Params,
        Json
    }

    internal static class RestSharpExtensions
    {
        public static bool IsSuccessStatusCode(this HttpStatusCode responseCode)
        {
            var numericResponse = (int)responseCode;
            const int statusCodeOk = (int)HttpStatusCode.OK;
            const int statusCodeMultipleChoices = (int)HttpStatusCode.MultipleChoices;
            return numericResponse >= statusCodeOk && numericResponse < statusCodeMultipleChoices;
        }

        public static bool IsInternalErrorCode(this HttpStatusCode responseCode)
        {
            var numericResponse = (int)responseCode;
            const int internalError = (int)HttpStatusCode.InternalServerError;
            return numericResponse == internalError;
        }

        public static bool IsSuccessful(this IRestResponse response)
        {
            return response.StatusCode.IsSuccessStatusCode() &&
                response.ResponseStatus == ResponseStatus.Completed;
        }

        public static void EnsureResponseWasSuccessful(this IRestClient client, IRestRequest request, IRestResponse response)
        {
            if (response.IsSuccessful())
            {
                return;
            }
            var requestUri = client.BuildUri(request);
            throw RestException.CreateException(requestUri, response);
        }
    }

    public class RestException : Exception
    {
        public HttpStatusCode HttpStatusCode { get; set; }

        public Uri RequestUri { get; set; }

        public string Content { get; set; }

        public RestResultError Error { get; set; }

        public RestException(HttpStatusCode httpStatusCode, Uri requestUri, string content, string message, RestResultError error, Exception innerException)
            : base(message, innerException)
        {
            HttpStatusCode = httpStatusCode;
            RequestUri = requestUri;
            Content = content;
            Error = error;
        }

        public static RestException CreateException(Uri requestUri, IRestResponse response)
        {
            Exception innerException = null;
            RestResultError error = null;
            var messageBuilder = new StringBuilder();
            if (!response.StatusCode.IsSuccessStatusCode())
            {
                messageBuilder.AppendLine(response.StatusDescription);
                if (response.StatusCode.IsInternalErrorCode())
                {
                    error = JsonConvert.DeserializeObject<RestResultError>(response.Content);
                }
            }

            if (response.ErrorException != null)
            {
                messageBuilder.AppendLine(response.ErrorMessage);
                innerException = response.ErrorException;
            }
            return new RestException(response.StatusCode, requestUri, response.Content, messageBuilder.ToString(), error, innerException);
        }
    }

    public class RestResultError
    {
        public string Message { get; set; }

        public string ExceptionMessage { get; set; }

        public string ExceptionType { get; set; }

        public string StackTrace { get; set; }
    }
}
