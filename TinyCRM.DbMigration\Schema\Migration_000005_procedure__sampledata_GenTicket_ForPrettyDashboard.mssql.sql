
/****** Object:  StoredProcedure [sampledata].[GenTicket_ForPrettyDashboard]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [sampledata].[GenTicket_ForPrettyDashboard]
			@TotalNumOfTicket INT = 10000,
			@NumOfTicketPerday INT = 500

AS
BEGIN

	--SET @NumOfTicketPerday = 500
	--SET @TotalNumOfTicket = 50000


	DECLARE @numberOfDaysToCreateOn INT,
			@runningDate INT = 1

	SET @numberOfDaysToCreateOn = @TotalNumOfTicket / @NumOfTicketPerday + 1

	


	-- Create each day as a batch

	DECLARE @dateFrom DATETIME
			, @dateTo datetime
			,@replicaModel15 INT
			,@startBatchtime DATETIME
		
	SET @replicaModel15 = @NumOfTicketPerday / 15 + 1


	
	DECLARE @createFrom DATETIME,@createTo DATETIME
	-- 2.A0 A1
	SET @createFrom =  DATEADD(MINUTE,-18,GETDATE())
	SET @createTo =  DATEADD(MINUTE,-0,GETDATE())
	EXEC sampledata.GenTicket_SALData @ct_creatTimeFrom = @createFrom,@ct_createTimeTo = @createTo,  
																  @exactNumberOfTicketsToCreate=100,
																  @ct_rate2 = 100, @ct_rate3 = 0,
																  @AcceptDueMinute=10,
																  @cleanAllRelatedTable=1


	-- 2.P1
	SET @createFrom =  DATEADD(HOUR,-8,GETDATE())
	SET @createTo =  DATEADD(HOUR,-5,GETDATE())
	EXEC sampledata.GenTicket_SALData @ct_creatTimeFrom = @createFrom,@ct_createTimeTo = @createTo,  
																  @exactNumberOfTicketsToCreate=200,
																  @ct_rate2 = 100, @ct_rate3 = 0,
																  @ProcessDueMinute=240



	SET @dateFrom = DATETIMEFROMPARTS(DATEPART(YEAR, GETDATE()), DATEPART(MONTH, GETDATE()), DATEPART(DAY, GETDATE()), 8, 0, 0, 0)
	SET @dateTo = DATETIMEFROMPARTS(DATEPART(YEAR, GETDATE()), DATEPART(MONTH, GETDATE()), DATEPART(DAY, GETDATE()), 17, 0, 0, 0)
	WHILE @runningDate < @numberOfDaysToCreateOn
	BEGIN
		SET @startBatchtime = GETDATE()

		SET @dateFrom = DATEADD(DAY,-1,@dateFrom)
		SET @dateTo = DATEADD(DAY,-1,@dateTo)

		IF (@runningDate < 3)
				EXEC sampledata.GenTicket_SALData @ct_creatTimeFrom = @dateFrom,
									   @ct_createTimeTo = @dateTo,
									   @replicaNumberOwnershipModel14 = @replicaModel15,
									   @ct_rate2 = 10,
									   @ct_rate3 = 50
		ELSE IF (@runningDate < 10)
				EXEC sampledata.GenTicket_SALData @ct_creatTimeFrom = @dateFrom,
									   @ct_createTimeTo = @dateTo,
									   @replicaNumberOwnershipModel14 = @replicaModel15,
									   @ct_rate2 = 1,
									   @ct_rate3 = 30
		ELSE IF (@runningDate < 30)
				EXEC sampledata.GenTicket_SALData @ct_creatTimeFrom = @dateFrom,
									   @ct_createTimeTo = @dateTo,
									   @replicaNumberOwnershipModel14 = @replicaModel15,
									   @ct_rate2 = 0,
									   @ct_rate3 = 2
		ELSE 
				EXEC sampledata.GenTicket_SALData @ct_creatTimeFrom = @dateFrom,
									   @ct_createTimeTo = @dateTo,
									   @replicaNumberOwnershipModel14 = @replicaModel15,
									   @ct_rate2 = 0,
									   @ct_rate3 = 0
		
		DECLARE @message NVARCHAR(max)
		SET @message = CONCAT('*** Finish batch @runningDate:',@runningDate,'/',@numberOfDaysToCreateOn,'  - ms elapsed:', DATEDIFF(MILLISECOND,@startBatchtime,GETDATE()))
		RAISERROR(@message, 0, 1) WITH NOWAIT
		SET @runningDate = @runningDate+1
	END

END
GO