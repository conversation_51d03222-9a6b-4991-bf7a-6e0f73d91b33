
/****** Object:  StoredProcedure [telesale].[CreateAppointmentFromAppointmentRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[CreateAppointmentFromAppointmentRaw]
	@UserId UNIQUEIDENTIFIER,
	@SessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	INSERT INTO dbo.Appointment
	        ( Id ,
	          ContactId ,
	          LeadAssignmentId ,
	          MeetAddress ,
	          MeetDate ,
	          Latitude ,
	          Longitude ,
	          CreatedDate ,
	          CreatedBy ,
	          ProvinceId ,
	          DistrictId ,
	          WardId ,
	          AppointmentResultCodeId ,
	          RetryCount ,
	          Notes ,
	          UpdatedResultBy ,
	          UpdatedResultDate ,
	          [Status] ,
	          ProductId ,
	          ProductBudget
	        )
	SELECT
			  ar.AppointmentId Id,
			  ar.ContactId,
			  ar.LeadAssignmentId,
			  (N'' + IIF(ar.[Address] IS NOT NULL, ar.[Address] + N' - ', N'') + IIF(ar.Street IS NOT NULL, ar.Street + N' - ', N'') + IIF(ar.Ward IS NOT NULL, ar.Ward + N' - ', N'') + IIF(ar.District IS NOT NULL, ar.District + N' - ', N'') + IIF(ar.Province IS NOT NULL, ar.Province, N'')) MeetAddress,
			  ar.AppointmentTime MeetDate,
			  NULL Latitude,
			  NULL Longitude,
			  GETDATE() CreatedDate,
			  tmr.Id CreatedBy,
			  pf.ProvinceId,
			  df.DistrictId,
			  wf.WardId,
			  NULL AppointmentResultCodeId,
			  NULL RetryCount,
			  ar.Remark Notes,
			  NULL UpdatedResultBy,
			  NULL UpdatedResultDate,
			  1 [Status],
			  NULL ProductId,
			  ar.ProductValue ProductBudget
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles dmo ON ar.CodeDMO=dmo.AgentCode
	LEFT JOIN dbo.UserProfiles tmr ON ar.CodeTMR=tmr.AgentCode
	LEFT JOIN
		(
			SELECT p.ImportCodeName ImportCodeNameP, p.Id ProvinceId, p.ProvinceName FROM dbo.Province p
		) pf ON pf.ImportCodeNameP=ar.Province
		LEFT JOIN
		(
			SELECT p.ImportCodeName ImportCodeNameP, d.ImportCodeName ImportCodeNameD, p.ProvinceName, d.Id DistrictId, d.DistrictName FROM dbo.Province p
			INNER JOIN dbo.District d ON d.ProvinceId=p.Id
		) df ON df.ImportCodeNameD=ar.District AND df.ImportCodeNameP=ar.Province
		LEFT JOIN
		(
			SELECT p.ImportCodeName ImportCodeNameP, d.ImportCodeName ImportCodeNameD, p.ProvinceName, d.DistrictName, w.Id WardId, w.WardName FROM dbo.Province p
			INNER JOIN dbo.District d ON d.ProvinceId=p.Id
			INNER JOIN dbo.Ward w ON w.DistrictId=d.Id
		) wf ON wf.WardName=ar.Ward AND wf.ImportCodeNameD=ar.District AND wf.ImportCodeNameP=ar.Province
	WHERE ar.IsInvalid <> 1 AND ar.ImportSessionId=@SessionId
	AND (NOT (ar.IsDupPa=1 AND ar.IsDupApp=1))
END
GO