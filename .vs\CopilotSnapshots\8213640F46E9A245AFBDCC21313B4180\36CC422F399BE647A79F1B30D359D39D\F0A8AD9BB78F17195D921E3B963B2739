﻿using System;
using System.Linq;

namespace Webaby.Core.Access.Queries
{
    public class GetRoleBusinessPermissionByRoleIdQuery : QueryBase<RoleBusinessPermissionData>
    {
        public GetRoleBusinessPermissionByRoleIdQuery(Guid roleId)
        {
            RoleId = roleId;
        }

        public Guid RoleId { get; private set; }
    }

    internal class GetRoleBusinessPermissionByRoleIdQueryHandler :
        QueryHandlerBase<GetRoleBusinessPermissionByRoleIdQuery, RoleBusinessPermissionData>
    {
        public override QueryResult<RoleBusinessPermissionData> Execute(GetRoleBusinessPermissionByRoleIdQuery query)
        {
            var rolePermissionEntities =
                EntitySet.Get<RoleBusinessPermissionEntity>().Where(x => x.RoleId == query.RoleId);
            return QueryResult.Create(rolePermissionEntities, RoleBusinessPermissionData.FromEntity);
        }
    }
}
