﻿using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Caching;

namespace Webaby.Core.BusinessSettings.Queries
{
    public class GetBusinessSettingsByImportKeyQuery : QueryBase<BusinessSettingEntity>
    {
        public string ImportKey { get; set; }
    }

    internal class GetBusinessSettingsByImportKeyQueryHandler : QueryHandlerBase<GetBusinessSettingsByImportKeyQuery, BusinessSettingEntity>
    {
        public GetBusinessSettingsByImportKeyQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<BusinessSettingEntity>> ExecuteAsync(GetBusinessSettingsByImportKeyQuery query)
        {
            var businessSettingData = await EntitySet.Get<BusinessSettingEntity>().Where(x => x.ImportKey == query.ImportKey).ToListAsync();
            var mapped = businessSettingData.Select(x => Mapper.Map<BusinessSettingEntity>(x));
            return QueryResult.Create(mapped);
        }
    }
}
