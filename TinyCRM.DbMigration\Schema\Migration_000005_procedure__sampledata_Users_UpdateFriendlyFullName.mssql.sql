
/****** Object:  StoredProcedure [sampledata].[Users_UpdateFriendlyFullName]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROC [sampledata].[Users_UpdateFriendlyFullName]
	@withAccountName BIT =0
AS BEGIN
    

IF OBJECT_ID('sampledata.tempNames') IS NOT NULL DROP TABLE sampledata.tempNames

CREATE TABLE sampledata.tempNames 
(
	id INT IDENTITY(1, 1) PRIMARY KEY,
	nameValue NVARCHAR(max)
)



insert sampledata.tempNames(nameValue)
SELECT xx.Name 
FROM (
select N'Đặng Tuấn Anh' AS Name UNION
select N'Hoàng Đức Anh' Union
select N'Lưu Trang Anh' Union
select N'Phạm Hoàng Anh' Union
select N'Phạm Thị Hiền Anh' Union
select N'Phạm Khắc Việt Anh' Union
select N'Đỗ Hoàng Gia Bảo' Union
select N'Trần Thị Minh Ch<PERSON>u' Union
select N'Tăng Phương Chi' Union
select N'Gan Feng Du' Union
select N'Phạm Tiến Dũng' Union
select N'Nguyễn Thái Dương' Union
select N'Trần An Dương' Union
select N'Mạc Trung Đức' Union
select N'Vũ Hương Giang' Union
select N'Nguyễn Thị Ngân Hà' Union
select N'Nguyễn Lê Hiếu' Union
select N'Phạm Xuân Hòa' Union
select N'Khoa Minh Hoàng' Union
select N'Nguyễn Hữu Hiệp Hoàng' Union
select N'Nguyễn Mạnh Hùng' Union
select N'Nguyễn Vũ Gia Hưng' Union
select N'Trần Tuấn Hưng' Union
select N'Phạm Gia Minh' Union
select N'Đỗ Hoàng Mỹ' Union
select N'Đỗ Quang Ngọc' Union
select N'Đàm Yến Nhi' Union
select N'Đoàn Hoàng Sơn' Union
select N'Nguyễn Công Thành' Union
select N'Bùi Phương Thảo' Union
select N'Nguyễn Hương Thảo' Union
select N'Tô Diệu Thảo' Union
select N'Vũ Phương Thảo' Union
select N'Đặng Huyền Thi' Union
select N'Đặng Thành Trung' Union
select N'Trịnh Thiên Trường' Union
select N'Lê Khánh Vy' Union

select N'Đặng Quốc Việt' Union
select N'Hoàng Văn Bảo' Union
select N'Lưu Thanh Tuấn' Union
select N'Hoàng Thị Thanh Mai' Union
select N'Nguyễn Quỳnh Hoa' Union
select N'Cao Thị Xuân Dung' Union
select N'Đỗ Hồng Việt' Union
select N'Phạm Thị Thu Hương' Union
select N'Bùi Thị Vân Thiện' Union
select N'Nguyễn Thị Thu Hiền' Union
select N'Nguyễn Thị Trà My' Union
select N'Trần Thị Thúy' Union
select N'Trần Trọng Dũng' Union
select N'Mạc Văn Việt' Union
select N'Bùi Thị Thu Hương' Union
select N'Nguyễn Văn Đạm' Union
select N'Lê Thị Hợi' Union
select N'Phạm Văn Cường' Union
select N'Khoa Năng Tùng' Union
select N'Nguyễn Hữu Hòa' Union
select N'Nguyễn Vân Long' Union
select N'Nguyễn Thị Dương' Union
select N'Tô Thị Mai' Union
select N'Phạm Duy' Union
select N'Bùi Phạm Vân Anh' Union
select N'Đỗ Quang Minh' Union
select N'Nguyễn Thị Thu Hằng' Union
select N'Cao Thị Phương Thảo' Union
select N'Nguyễn Thị Việt Yên' Union
select N'Bùi Văn Quân' Union
select N'Nguyễn Thị Hương' Union
select N'Tô Sỹ Ngọc' Union
select N'Vũ Duy Phương' Union
select N'Phạm Thị Thanh Thùy' Union
select N'Nguyễn Thị Mai' Union
select N'Trịnh Đình Minh' Union
select N'Đinh Thúy Hằng' 
) xx 


UPDATE up SET up.FullName=n.nameValue + ' ' + IIF(@withAccountName=1, nup.AccountName ,CAST(nup.ranOrder/70+1 AS NVARCHAR(max)))
--SELECT *, n.nameValue + ' '  + IIF(@withAccountName=1, nup.AccountName ,CAST(nup.ranOrder/70+1 AS NVARCHAR(max)))
FROM (
SELECT Id,u.UserName AccountName, ROW_NUMBER() OVER (ORDER BY (SELECT 1)) ranOrder 
FROM dbo.UserProfiles up
JOIN dbo.aspnet_Users u ON u.UserId=up.Id
) nup
JOIN sampledata.tempNames n ON n.id=(nup.ranOrder%70)+1
JOIN dbo.UserProfiles up ON up.Id=nup.Id

END
GO