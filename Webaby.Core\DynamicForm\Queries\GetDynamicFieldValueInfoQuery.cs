﻿using System;
using System.Data;
using System.Data.SqlClient;
using Webaby.Data;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFieldValueInfoQuery : QueryBase<DynamicFieldValueInfo>
    {
        public Guid DynamicFormValueId { get; set; }

        public bool? Display { get; set; }

        public bool IncludeDeleted { get; set; }
    }

    internal class GetDynamicFieldValueInfoQueryHandler : QueryHandlerBase<GetDynamicFieldValueInfoQuery, DynamicFieldValueInfo>
    {
        public override QueryResult<DynamicFieldValueInfo> Execute(GetDynamicFieldValueInfoQuery query)
        {
            SqlCommand cmd = new SqlCommand();
            cmd.Parameters.AddRange(new[]
            {
                SqlParameterHelper.AddNullableGuid("@DynamicFormValueId", query.DynamicFormValueId),
                SqlParameterHelper.NewNullableBooleanParameter("@Display", query.Display),
                SqlParameterHelper.NewNullableBooleanParameter("@IncludeDeleted", query.IncludeDeleted),
            });
            cmd.CommandText = "GetDynamicFieldValueInfo";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = EntitySet.ExecuteReadCommand<DynamicFieldValueInfo>(cmd);
            return new QueryResult<DynamicFieldValueInfo>(mainQuery);
        }
    }
}
