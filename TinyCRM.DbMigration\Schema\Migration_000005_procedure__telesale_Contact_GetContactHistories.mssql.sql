
/****** Object:  StoredProcedure [telesale].[Contact_GetContactHistories]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[Contact_GetContactHistories]
	@ContactId		UNIQUEIDENTIFIER
AS
BEGIN
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	SELECT
	en.Id AuditId, en.KeyValue, en.[Action], en.ModifiedBy, ISNULL(up.FullName, u.UserName) ModifiedByUserName, en.ModifiedDate,
	ef.Id FieldId, ef.MemberName,
	CASE WHEN ef.MemberName = 'Notes' THEN 'Notes'
		 WHEN ef.MemberName = 'ProvinceId' THEN N'Tỉnh/thành'
		 ELSE ef.MemberName
	END MemberDescription,
	CASE WHEN ef.MemberName = 'ProvinceId' then pv1.ProvinceName
		 ELSE ISNULL(ef.OldValue, '')
	<PERSON><PERSON>,
	CASE WHEN ef.MemberName = 'ProvinceId' then pv2.ProvinceName
		 ELSE ISNULL(ef.NewValue, '')
	END NewValue
	FROM AuditEntityChange en
	INNER JOIN AuditFieldChange ef ON en.Id = ef.AuditId
	INNER JOIN aspnet_Users u ON u.UserId = en.ModifiedBy
	INNER JOIN UserProfiles up ON u.UserId = up.Id
	LEFT JOIN dbo.Province pv1 ON ef.MemberName = 'ProvinceId' AND pv1.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT)
	LEFT JOIN dbo.Province pv2 ON ef.MemberName = 'ProvinceId' AND pv2.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT)
	WHERE en.KeyValue=@ContactId
	ORDER BY en.ModifiedDate DESC
END
GO