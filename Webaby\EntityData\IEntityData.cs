﻿using System.Collections.Specialized;
using Webaby.Web;

namespace Webaby.EntityData
{
    public interface IEntityData
    {
        Guid Id { get; set; }

        bool TryMapping(string objectName,
            NameValueCollection values,
            HttpPostedFileCollection files,
            out Dictionary<string, List<string>> validateErrors);

        string GetValue();

        Task SaveAsync();

        Task LoadAsync();

        bool HasValue(string objectName,
            NameValueCollection values,
            HttpPostedFileCollection files);

        bool ExecuteExpression(string expression);

        Dictionary<string, string> GetAdditionalMetadata(Guid? dynamicFieldDefinitionId = null,
            Guid? dynamicFieldValueId = null);

        string DisplayContent(string templateHint, bool isHtml = false);
    }

    public class EntityDataEditorAttribute : Attribute
    {
        public string ImportKey { get; set; }
    }

    public class EntityDataEditorSetting
    {
        public string DisplayName { get; set; }

        public List<EntityDataEditorTemplate> EditorTemplates { get; set; }
    }

    public class EntityDataEditorTemplate
    {
        public string Name { get; set; }

        public string EditorTemplate { get; set; }
    }
}