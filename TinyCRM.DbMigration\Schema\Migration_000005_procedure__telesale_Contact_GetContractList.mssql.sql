﻿
/****** Object:  StoredProcedure [telesale].[Contact_GetContractList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[Contact_GetContractList]

	@ContactId	UNIQUEIDENTIFIER

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SELECT	dfv.Id DynamicFormValueId, dfv.Code DynamicFormCode, df.Name DynamicFormName, pr.ProductName, dfv.CreatedDate, ISNULL(up.FullName,u.UserName) CreatedByUser, dfv.Status
	FROM	dbo.DynamicFormValue dfv
			JOIN dbo.DynamicForm df ON df.Id = dfv.DynamicFormId
			JOIN dbo.Product pr ON pr.Id = dfv.ProductId
			JOIN dbo.ProspectAssignment pa ON pa.Id = dfv.ProspectAssignmentId
			JOIN dbo.Prospect p ON p.Id = pa.ProspectId
			JOIN dbo.Contact c ON c.Id = p.ContactId
			JOIN dbo.aspnet_Users u ON u.UserId = dfv.CreatedBy
			JOIN dbo.UserProfiles up ON up.Id = u.UserId
	WHERE	c.Id = @ContactId
			AND dfv.ReferencerObjectType = 1
	ORDER BY dfv.CreatedDate DESC
******/
END
GO