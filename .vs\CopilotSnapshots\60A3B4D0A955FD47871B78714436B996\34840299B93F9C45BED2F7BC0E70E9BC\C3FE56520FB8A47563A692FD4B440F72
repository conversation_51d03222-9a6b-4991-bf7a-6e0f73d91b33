﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFieldInFormByNameQuery : QueryBase<DynamicFieldDefinitionData>
    {
        public Guid? FieldId { get; set; }
        public string Name { get; set; }
        public Guid FormId { get; set; }
    }

    internal class GetDynamicFieldInFormByNameQueryHandler :
        QueryHandlerBase<GetDynamicFieldInFormByNameQuery, DynamicFieldDefinitionData>
    {
        public GetDynamicFieldInFormByNameQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        {
        }

        public override async Task<QueryResult<DynamicFieldDefinitionData>> ExecuteAsync(GetDynamicFieldInFormByNameQuery query)
        {
            // Lấy tất cả các DynamicFieldDefinitionEntity
            var fieldEntities = await EntitySet.GetAsync<DynamicFieldDefinitionEntity>();

            // Nếu có FieldId, loại bỏ field có Id đó
            if (query.FieldId.HasValue)
            {
                fieldEntities = fieldEntities.Where(x => x.Id != query.FieldId);
            }

            // Lọc theo FormId và Name
            var mainQuery = fieldEntities
                .Where(field => field.DynamicFormId == query.FormId && field.Name == query.Name);

            // Trả về kết quả đã map
            return QueryResult.Create(mainQuery, x => Mapper.Map<DynamicFieldDefinitionData>(x));
        }
    }
}

