
/****** Object:  StoredProcedure [telesale].[CallResultFunnelSumary]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[CallResultFunnelSumary]

	@CampaignId			UNIQUEIDENTIFIER,
	@FromDate			DATETIME,
	@ToDate				DATETIME,
	@OrganizationId		UNIQUEIDENTIFIER,
	@AssignedAgentId	UNIQUEIDENTIFIER

AS
BEGIN

	WITH cte(Id) AS
	(
		SELECT	Id
		FROM	dbo.Organization org
		WHERE	org.Id = @OrganizationId
		UNION ALL
		SELECT	org.Id
		FROM	dbo.Organization org
				JOIN cte temp ON temp.Id = org.ParentId
	)
	SELECT	cte.Id
	INTO	#TempOrgs
	FROM	cte

	DECLARE @SelectString NVARCHAR(MAX) = N'
	SELECT	org.Name OrganizationName, up.FullName TelesaleName,

			SUM(IIF(cr.IsConnected=1,1,0)) TotalConnected,
			SUM(IIF(cr.IsConnected=1,cc.Duration,0)) TotalConnectedDuration,

			SUM(IIF(cr.IsContacted=1,1,0)) TotalContacted,
			SUM(IIF(cr.IsContacted=1,cc.Duration,0)) TotalContactedDuration,

			SUM(IIF(cr.IsConsulted=1,1,0)) TotalConsulted,
			SUM(IIF(cr.IsConsulted=1,cc.Duration,0)) TotalConsultedDuration,

			SUM(IIF(cr.IsInterested=1,1,0)) TotalInterested,
			SUM(IIF(cr.IsInterested=1,cc.Duration,0)) TotalInterestedDuration,

			SUM(IIF(cr.IsEligible=1,1,0)) TotalEligible,
			SUM(IIF(cr.IsEligible=1,cc.Duration,0)) TotalEligibleDuration,

			SUM(IIF(cr.IsAppointmentMade=1,1,0)) TotalAppointmentMade,
			SUM(IIF(cr.IsAppointmentMade=1,cc.Duration,0)) TotalAppointmentMadeDuration,

			SUM(IIF(cr.IsWin=1,1,0)) TotalWin,
			SUM(IIF(cr.IsWin=1,cc.Duration,0)) TotalWinDuration,

			COUNT(*) TotalData,
			SUM(cc.Duration) TotalDuration
	'

	DECLARE @FromString NVARCHAR(MAX) = N'
	FROM	dbo.ContactCall cc
			JOIN dbo.CallResult cr ON cr.Id = cc.CallResultId
			LEFT JOIN dbo.Organization org ON org.Id = cc.CreatedByTeamId
			LEFT JOIN dbo.UserProfiles up ON up.Id = cc.CreatedBy
	'
	IF @OrganizationId IS NOT NULL
	BEGIN
		SET @FromString = @FromString + N'
			JOIN #TempOrgs tempOrg ON tempOrg.Id = org.Id
		'
	END

	DECLARE @WhereString NVARCHAR(MAX) = N''
	IF @CampaignId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' cc.CampaignId = @CampaignId '
	END
	IF @AssignedAgentId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' cc.CreatedBy = @AssignedAgentId '
	END

	IF @FromDate IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' cc.CreatedDate >= @FromDate '
	END
	IF @ToDate IS NOT NULL
	BEGIN
		SET @ToDate = DATEADD(DAY, 1, @ToDate)
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' cc.CreatedDate < @ToDate '
	END

	DECLARE @GroupBy NVARCHAR(MAX) = N'
	GROUP BY org.Name, up.FullName
	'

	DECLARE @ParamDefs NVARCHAR(MAX) = N'
		@CampaignId			UNIQUEIDENTIFIER,
		@FromDate			DATETIME,
		@ToDate				DATETIME,
		@OrganizationId		UNIQUEIDENTIFIER,
		@AssignedAgentId	UNIQUEIDENTIFIER
	'

	DECLARE @FullExecString NVARCHAR(MAX) = @SelectString + @FromString + @WhereString + @GroupBy

	EXECUTE sp_executesql @FullExecString, @ParamDefs,
											@CampaignId = @CampaignId,
											@FromDate = @FromDate,
											@ToDate = @ToDate,
											@OrganizationId = @OrganizationId,
											@AssignedAgentId = @AssignedAgentId
			

END
GO