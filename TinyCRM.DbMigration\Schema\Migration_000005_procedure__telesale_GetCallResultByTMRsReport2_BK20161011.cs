using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using System.Reflection;
using Webaby.Data;

namespace TinyCRM.DbMigration.Schema
{
    [DbContext(typeof(MigrationDbContext))]
    [Migration(nameof(Migration_000005_procedure__telesale_GetCallResultByTMRsReport2_BK20161011))]
    internal class Migration_000005_procedure__telesale_GetCallResultByTMRsReport2_BK20161011 : RawMigration
    {
        protected override Assembly ResourceAssembly => Assembly.GetExecutingAssembly();
    }
}