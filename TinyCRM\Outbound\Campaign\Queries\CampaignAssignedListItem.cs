﻿using System;
using TinyCRM.Enums;

namespace TinyCRM.Outbound.Campaign.Queries
{
    public class CampaignAssignedListItem
    {
        public Guid CampaignId { get; set; }

        public string Name { get; set; }

        public CampaignStatus Status { get; set; }

        public CampaignType CampaignType { get; set; }

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public int TotalWork { get; set; }

        public int NewWork { get; set; }

        public int AssignedTeamWork { get; set; }

        public int AssignedAgentWork { get; set; }

        public int ProgressWork { get; set; }

        public int ClosedWork { get; set; }

        public int TotalCount { get; set; }

        public string Description { get; set; }
    }
}
