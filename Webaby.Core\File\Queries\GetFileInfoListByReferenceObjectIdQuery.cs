﻿using System;
using System.Linq;

namespace Webaby.Core.File.Queries
{
    public class GetFileInfoListByReferenceObjectIdQuery : QueryBase<FileData>
    {
        public Guid ReferenceObjectId { get; set; }
    }

    internal class GetFileInfoListByReferenceObjectIdQueryHandler : QueryHandlerBase<GetFileInfoListByReferenceObjectIdQuery, FileData>
    {
        public override QueryResult<FileData> Execute(GetFileInfoListByReferenceObjectIdQuery query)
        {
            var fileData = from file in EntitySet.Get<FileEntity>()
                           where file.ReferenceObjectId == query.ReferenceObjectId
                           select new FileData
                           {
                               Id = file.Id,
                               Descriptions = file.Descriptions,
                               Extensions = file.Extension,
                               FileName = file.FileName,
                               ReferenceFileId = file.ReferenceFileId,
                               ReferenceObjectId = file.ReferenceObjectId,
                               ReferenceObjectType = file.ReferenceObjectType,
                           };
            return new QueryResult<FileData>(fileData);
        }
    }
}