﻿using System.Collections.Specialized;

namespace Webaby.Core.DynamicForm.Queries
{
    public class CalculateDynamicFieldQuery : QueryBase<DynamicFieldValueInfo>
    {
        public Guid DynamicFormId { get; set; }

        public IEnumerable<DynamicFieldValueInfo> DynamicFieldValueInfo { get; set; }

        public NameValueCollection FormValues { get; set; }

        public string SuggestValueCalculatedFieldName { get; set; }
    }
}