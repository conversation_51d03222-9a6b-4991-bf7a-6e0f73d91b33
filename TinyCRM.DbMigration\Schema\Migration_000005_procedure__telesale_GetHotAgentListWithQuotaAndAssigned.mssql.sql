
/****** Object:  StoredProcedure [telesale].[GetHotAgentListWithQuotaAndAssigned]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- ALTER PROCEDURE [telesale].[GetHotAgentListWithQuotaAndAssigned] '5BBD14DB-DC69-4155-AEC7-C1183E3693D9', '2016-06-28'
CREATE PROCEDURE [telesale].[GetHotAgentListWithQuotaAndAssigned]

	@HotListGroupId		UNIQUEIDENTIFIER,
	@AssignedDate		DATETIME

AS
BEGIN

	SELECT	u.UserId, u.UserName, up.FullName, hotGroup.GroupName HotGroupName, orgTeam.Name TeamName, ISNULL(competence.TargetContact,0) TargetNumber, 0 AssignedNumber
	FROM	dbo.UserProfiles up
			JOIN telesale.HotListGroupUser hotU ON hotU.UserId = up.Id
			JOIN telesale.HotListGroup hotGroup ON hotGroup.Id = hotU.HotListGroupId
			JOIN dbo.aspnet_Users u ON u.UserId = up.Id
			JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
			JOIN dbo.Organization orgTeam ON orgTeam.Id = up.OrganizationId
			JOIN dbo.aspnet_Membership mem ON mem.UserId = u.UserId
			LEFT JOIN
			(
				SELECT	CompetenceLevel, SUM(TargetContact) TargetContact
				FROM	dbo.AgentTargetByCompetenceModel
				GROUP BY CompetenceLevel
			) competence ON competence.CompetenceLevel = up.CompetenceLevel
	WHERE	uir.RoleId = 'EC6E1D89-6251-4812-9D17-4CBBECEF44ED'
			AND hotGroup.Id = @HotListGroupId
			AND mem.IsApproved=1
	ORDER BY hotGroup.GroupName, ISNULL(up.FullName, u.UserName)

END
GO