
/****** Object:  StoredProcedure [sampledata].[Init_Permission_CleanAll_ExceptApiRelated]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [sampledata].[Init_Permission_CleanAll_ExceptApiRelated]
as
BEGIN
		---Clean Biz and BusinessPermission Exclude anything related to CoreAPI
		WITH cteBiz (BizId, ParentId)
		as
		(
			SELECT b.Id,
				   b.ParentId
			FROM dbo.Accesses a
				JOIN dbo.AccessBusinessPermission ab ON ab.AccessId = a.Id
				JOIN dbo.BusinessPermission b ON b.Id = ab.BusinessPermissionId
			WHERE IsApi=1
			UNION ALL
			SELECT b.Id,b.ParentId FROM cteBiz JOIN dbo.BusinessPermission b ON b.Id=cteBiz.ParentId
		) 
		SELECT DISTINCT cteBiz.BizId
		INTO #temp
		FROM cteBiz


		DELETE dbo.BusinessPermission 
		FROM dbo.BusinessPermission b
		LEFT JOIN #temp t ON t.BizId=b.Id
		WHERE t.BizId IS NULL

		DELETE ab
		FROM dbo.AccessBusinessPermission ab
		LEFT JOIN #temp t ON t.BizId=ab.BusinessPermissionId
		WHERE t.BizId IS NULL
END

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
GO