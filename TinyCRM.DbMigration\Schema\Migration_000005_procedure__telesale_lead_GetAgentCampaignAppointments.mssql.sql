
/****** Object:  StoredProcedure [telesale].[lead_GetAgentCampaignAppointments]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE   PROCEDURE [telesale].[lead_GetAgentCampaignAppointments]

	@CampaignId				UNIQUEIDENTIFIER,
	@AgentId				UNIQUEIDENTIFIER,
	@TeamId					UNIQUEIDENTIFIER,
	@Status					INT,
	@MeetTimeFrom			DATETIME,
	@MeetTimeTo				DATETIME,
	@AppointmentResultId	UNIQUEIDENTIFIER,
	@FullName				NVARCHAR(500),
	@StartRow				INT,
	@EndRow					INT

AS
BEGIN
	IF @MeetTimeTo IS NOT NULL
	BEGIN
		SET @MeetTimeTo = DATEADD(DAY, 1, @MeetTimeTo)
	END

	DECLARE @ExtWhereString NVARCHAR(MAX) = N''
	SET @ExtWhereString = @ExtWhereString + IIF(@Status IS NOT NULL, N' AND app.Status = @Status ', '')
	SET @ExtWhereString = @ExtWhereString + IIF(@MeetTimeFrom IS NOT NULL, N' AND app.MeetDate >= @MeetTimeFrom ', '')
	SET @ExtWhereString = @ExtWhereString + IIF(@MeetTimeTo IS NOT NULL, N' AND app.MeetDate < @MeetTimeTo ', '')
	SET @ExtWhereString = @ExtWhereString + IIF(@AppointmentResultId IS NOT NULL, N' AND app.AppointmentResultCodeId = @AppointmentResultId ', '')
	SET @ExtWhereString = @ExtWhereString + IIF(ISNULL(@FullName,'') <> '', N' AND CONTAINS(c.Name, @FullName) ', '')

	DECLARE @ExecutedString NVARCHAR(MAX) = N'
	WITH cte AS
	(
		SELECT	app.Id, ROW_NUMBER() OVER (ORDER BY app.MeetDate DESC) RowNumber, up.FullName CreatedName
		FROM	dbo.Appointment app '
				+ IIF(ISNULL(@FullName,'')='', '', ' JOIN dbo.Customer c ON c.Id = app.CustomerId ') + '
				JOIN dbo.ProspectAssignment pa ON pa.Id = app.ProspectAssignmentId
				JOIN dbo.UserProfiles up on up.Id = pa.AssignedAgentId
		WHERE	pa.CampaignId = @CampaignId
				'+IIF(@TeamId IS NOT NULL, ' AND up.OrganizationId='''+CAST(@TeamId AS NVARCHAR(MAX))+''' ', '')+'
				'+IIF(@AgentId IS NOT NULL, ' AND up.Id = @AgentId ', '')+'
				' + @ExtWhereString + '
	)
	SELECT	app.Id AppointmentId, l.LeadCode, app.ContactId, app.LeadAssignmentId, app.ProspectAssignmentId, app.MeetDate, app.MeetAddress, app.Notes, app.FeedbackNotes, app.Status AppointmentStatus,
			c.Name CustomerName, c.DataSource CustomerDataSource, pr.Name,
			cw.Name WardName, cd.Name DistrictName, pd.Name ProvinceName , appResult.ResultCode AppointmentResultCode,
			assignedOrg.Name AssignedFieldSaleTeamName, assignedFieldSale.FullName AssignedFieldSaleName, cte.CreatedName,
			(SELECT COUNT(*) FROM cte) TotalCount
	FROM	cte
			JOIN dbo.Appointment app ON app.Id = cte.Id
			JOIN dbo.Customer c ON c.Id = app.ContactId
			LEFT JOIN dbo.Product pr ON pr.Id = app.ProductId
			LEFT JOIN dbo.LeadAssignment la ON la.Id = app.LeadAssignmentId
			LEFT JOIN dbo.Lead l ON l.Id = la.LeadId
			LEFT JOIN dbo.Organization assignedOrg ON assignedOrg.Id = la.AssignedFieldSaleTeamId
			LEFT JOIN dbo.UserProfiles assignedFieldSale ON assignedFieldSale.Id = la.AssignedFieldSaleId
			LEFT JOIN dbo.Geolocation cw ON cw.Id = app.WardId
			LEFT JOIN dbo.Geolocation cd ON cd.Id = app.DistrictId
			LEFT JOIN dbo.Geolocation pd ON pd.Id = app.ProvinceId
			LEFT JOIN dbo.AppointmentResultCode appResult ON appResult.Id = app.AppointmentResultCodeId
	WHERE	cte.RowNumber BETWEEN @StartRow AND @EndRow '

	DECLARE @ParamDefs NVARCHAR(MAX) = N'
	@CampaignId				UNIQUEIDENTIFIER,
	@AgentId				UNIQUEIDENTIFIER,
	@Status					INT,
	@MeetTimeFrom			DATETIME,
	@MeetTimeTo				DATETIME,
	@AppointmentResultId	UNIQUEIDENTIFIER,
	@FullName				NVARCHAR(500),
	@StartRow				INT,
	@EndRow					INT
	'
	--SELECT @ExecutedString
	EXEC sp_executesql @ExecutedString, @ParamDefs,
										@CampaignId				= @CampaignId,
										@AgentId				= @AgentId,
										@Status					= @Status,
										@MeetTimeFrom			= @MeetTimeFrom,
										@MeetTimeTo				= @MeetTimeTo,
										@AppointmentResultId	= @AppointmentResultId,
										@FullName				= @FullName,
										@StartRow				= @StartRow,
										@EndRow					= @EndRow

END
GO