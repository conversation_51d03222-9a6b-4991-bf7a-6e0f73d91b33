
/****** Object:  StoredProcedure [telesale].[SalesSupportDistributeAppointments]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[SalesSupportDistributeAppointments]
	@FromDate DATETIME,
	@ToDate  DATETIME,
	@UserId UNIQUEIDENTIFIER 
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
			DECLARE @Old AppointmentAudit;
			DECLARE @New AppointmentAudit;

			INSERT INTO @Old( LeadAssignmentId ,AssignedFieldSaleId ,AssignedFieldSaleTeamId ,LeadAssignmentStatus ) 
			SELECT LA.Id,AssignedFieldSaleId, AssignedFieldSaleTeamId, la.Status
			FROM	dbo.Appointment ap
			JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId = ap.Id
			WHERE ap.MeetDate BETWEEN @FromDate AND @ToDate

	-- <PERSON><PERSON> bổ cho cái đám DMO đã được suggest trước cái đã, còn lại tính sau
	UPDATE	la
	SET		AssignedFieldSaleTeamId = up.OrganizationId,
			AssignedFieldSaleTeamDate = GETDATE(),
			AssignedFieldSaleId = up.Id,
			AssignedFieldSaleDate = GETDATE(),
			Status=2   
	FROM	dbo.Appointment ap
			JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId = ap.Id
			JOIN dbo.Lead l ON l.CurrentLeadAssignmentId = la.Id
			JOIN dbo.UserProfiles up ON up.Id = la.SuggestedFieldSaleId
	WHERE	ap.Status = 1
			AND ap.Status <> 4 AND ap.Status <> 5 -- Hẹn bị hủy, bị trả
			AND la.SuggestedFieldSaleId IS NOT NULL
			AND la.AssignedFieldSaleId IS NULL
			AND ap.MeetDate BETWEEN @FromDate AND @ToDate

	-- Phân bổ cho cái đám nhóm DMO đã được suggest trước cái đã, còn lại tính sau
	UPDATE	la
	SET		AssignedFieldSaleTeamId = la.SuggestedFieldSaleTeamId,
			AssignedFieldSaleTeamDate = GETDATE(),
			Status=2  -- modified to fix bug 
	FROM	dbo.Appointment ap
			JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId = ap.Id
			JOIN dbo.Lead l ON l.CurrentLeadAssignmentId = la.Id
	WHERE	ap.Status = 1
			AND ap.Status <> 4 AND ap.Status <> 5 -- Hẹn bị hủy, bị trả
			AND la.SuggestedFieldSaleTeamId IS NOT NULL AND la.SuggestedFieldSaleId IS NULL
			AND la.AssignedFieldSaleTeamId IS NULL
			AND ap.MeetDate BETWEEN @FromDate AND @ToDate

	--==================================================
	-- Phân bổ hẹn HOT
	--==================================================

	DECLARE @HotAppointmentId UNIQUEIDENTIFIER
    DECLARE @HotAppointmentProvinceId UNIQUEIDENTIFIER
    DECLARE @HotLeadAssignmentId UNIQUEIDENTIFIER
    DECLARE @HotListGroupId UNIQUEIDENTIFIER

    DECLARE @AssignedHotFieldSaleTeamId UNIQUEIDENTIFIER
    DECLARE @AssignedTeam UNIQUEIDENTIFIER

    SELECT  ap.Id AppointmentId ,
            la.Id LeadAssignmentId ,
            ap.HotListGroupId ,
            ap.ProvinceId ,
            ROW_NUMBER() OVER ( ORDER BY la.Id ) RowIndex
    INTO    #TempHotTable
    FROM    dbo.Appointment ap
            JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId = ap.Id
    WHERE   ap.Status = 1
            AND ap.Status <> 4
            AND ap.Status <> 5 -- Hẹn bị hủy, bị trả
            AND ap.MeetDate BETWEEN @FromDate AND @ToDate
            AND ap.HotListGroupId IS NOT NULL
            AND la.AssignedFieldSaleTeamId IS NULL
	 
    SELECT  fieldSaleCount.FieldSaleTeamId ,
            HotFsCount.ProvinceId ,
            fieldSaleCount.FieldSaleCount ,
            HotFsCount.HotFieldSaleCount ,
            HotFsCount.OrganizationName ,
            HotFsCount.HotListGroupId ,
            HotFsCount.ProvinceName ,
            HotFsCount.GroupName
    INTO    #HotDmo
    FROM    ( SELECT    org.Id FieldSaleTeamId ,
                        COUNT(*) FieldSaleCount
              FROM      dbo.Organization org
                        JOIN dbo.UserProfiles up ON up.OrganizationId = org.Id
                        JOIN dbo.aspnet_Membership m ON m.UserId = up.Id
                        JOIN dbo.UserInRoles uir ON uir.UserId = m.UserId
              WHERE     m.IsApproved = 1
                        AND uir.RoleId = 'A4095631-C225-4644-B39A-F5152F4A525C'
              GROUP BY  org.Id
            ) fieldSaleCount
            LEFT JOIN ( SELECT  org.Id FieldSaleTeamId ,
                                SUM(IIF(hg.Id IS NOT NULL, 1, 0)) HotFieldSaleCount ,
                                hotUp.HotListGroupId ,
                                wo.ProvinceId ,
                                org.OrganizationName ,
                                pv.ProvinceName ,
                                hg.GroupName
                        FROM    dbo.Organization org
                                JOIN dbo.UserProfiles up ON up.OrganizationId = org.Id
                                JOIN dbo.aspnet_Membership m ON m.UserId = up.Id
                                JOIN dbo.UserInRoles uir ON uir.UserId = m.UserId
                                LEFT JOIN telesale.HotListGroupUser hotUp ON hotUp.UserId = up.Id
                                JOIN dbo.OrganizationWorkingArea wo ON wo.OrganizationId = org.Id
                                JOIN dbo.Province pv ON pv.Id = wo.ProvinceId
                                LEFT JOIN telesale.HotListGroup hg ON hg.Id = hotUp.HotListGroupId
                        WHERE   m.IsApproved = 1
                                AND uir.RoleId = 'A4095631-C225-4644-B39A-F5152F4A525C'
                        GROUP BY org.Id ,
                                hotUp.HotListGroupId ,
                                wo.ProvinceId ,
                                org.OrganizationName ,
                                pv.ProvinceName ,
                                hg.GroupName
                      ) HotFsCount ON HotFsCount.FieldSaleTeamId = fieldSaleCount.FieldSaleTeamId
    WHERE   HotFsCount.ProvinceId IS NOT NULL

    SELECT  team.Id, SUM(IIF(ap.Id IS NOT NULL, 1, 0)) HenDaChia
    INTO    #HotAppointment
    FROM    dbo.Organization team
            LEFT JOIN dbo.LeadAssignment la ON la.AssignedFieldSaleTeamId = team.Id
            LEFT JOIN dbo.Appointment ap ON la.WaitingAppointmentId = ap.Id
                                            AND ap.Status = 1
                                            AND ap.Status <> 4
                                            AND ap.Status <> 5 -- Hẹn bị hủy, bị trả
                                            AND ap.MeetDate BETWEEN @FromDate AND @ToDate
                                            AND ap.HotListGroupId IS NOT NULL
                                            AND la.AssignedFieldSaleTeamId IS NOT NULL
    --WHERE   team.OrganizationType = 'DMO Team'
    GROUP BY team.Id	
	
    DECLARE @SumHotDmoWorkingOnTheAppointment INT
    
	DECLARE @HotIndex INT = 1
    DECLARE @TotalHot INT = (SELECT  COUNT(*) FROM #TempHotTable)

    WHILE @HotIndex <= @TotalHot
    BEGIN
	
        SELECT  @HotAppointmentId = AppointmentId ,
                @HotLeadAssignmentId = LeadAssignmentId ,
                @HotListGroupId = HotListGroupId ,
                @HotAppointmentProvinceId = ProvinceId
        FROM    #TempHotTable
        WHERE   RowIndex = @HotIndex

        SELECT  @SumHotDmoWorkingOnTheAppointment = ISNULL(SUM(HotFieldSaleCount), 0)
        FROM    #HotDmo
        WHERE   ProvinceId = @HotAppointmentProvinceId
                AND HotListGroupId = @HotListGroupId

        SET @AssignedTeam = NULL

        SELECT	TOP 1
                @AssignedTeam = ha.Id
        FROM    #HotDmo dmo
                JOIN #HotAppointment ha ON dmo.FieldSaleTeamId = ha.Id
        WHERE   dmo.ProvinceId = @HotAppointmentProvinceId
                AND dmo.HotListGroupId = @HotListGroupId
        ORDER BY CAST(ha.HenDaChia AS FLOAT)/IIF(@SumHotDmoWorkingOnTheAppointment = 0, dmo.FieldSaleCount, dmo.HotFieldSaleCount)

        IF @AssignedTeam IS NOT NULL
        BEGIN
            UPDATE  dbo.LeadAssignment
            SET     AssignedFieldSaleTeamId = @AssignedTeam ,
                    AssignedFieldSaleTeamDate = GETDATE(),
					Status=2
            WHERE   Id = @HotLeadAssignmentId
            UPDATE  #HotAppointment
            SET     HenDaChia = HenDaChia + 1
            WHERE   Id = @AssignedTeam
        END

        SET @HotIndex = @HotIndex + 1

    END

	--==================================================
	-- Hết phần phân bổ hẹn HOT
	--==================================================

	SELECT	org.Name OrganizationName,org.Id TeamId,ISNULL(assignedApp.AssignedCount,0) AppointmentAssignedCount,fieldSaleCount.FieldSaleCount
	INTO	#CurrentAppointmentDistributionStatus
	FROM	dbo.Organization org
			LEFT JOIN 
			(	
				SELECT	la.AssignedFieldSaleTeamId TeamId,COUNT(*) AssignedCount
				FROM	dbo.LeadAssignment la 
						JOIN dbo.Appointment ap ON ap.LeadAssignmentId = la.Id 
				WHERE	DATEDIFF(DAY,ap.MeetDate,@FromDate)=0 
						AND la.AssignedFieldSaleTeamId IS NOT NULL
						AND ap.Status <> 4 AND ap.Status <> 5
				GROUP BY la.AssignedFieldSaleTeamId
			) assignedApp ON assignedApp.TeamId = org.Id
			JOIN
			(
				SELECT	org.Id OrganizationId, COUNT(*) FieldSaleCount
				FROM	dbo.Organization org
						JOIN dbo.UserProfiles up ON up.OrganizationId = org.Id
						JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
						JOIN dbo.aspnet_Membership m ON m.UserId=up.Id
				WHERE	--org.OrganizationType = 'DMO Team' AND
						uir.RoleId = 'A4095631-C225-4644-B39A-F5152F4A525C' AND
						m.IsApproved=1
				GROUP BY org.Id
			) fieldSaleCount ON fieldSaleCount.OrganizationId = org.Id
	--WHERE org.OrganizationType='DMO Team'

	SELECT	ap.Id AppointmentId, la.Id LeadAssignmentId, ROW_NUMBER() OVER (ORDER BY ap.MeetDate) RowIndex
	INTO	#TempTable
	FROM	dbo.Appointment ap
			JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId = ap.Id
	WHERE	ap.Status = 1
			AND ap.Status <> 4 AND ap.Status <> 5 -- Hẹn bị hủy, bị trả
			AND ap.MeetDate BETWEEN @FromDate AND @ToDate
			AND la.AssignedFieldSaleTeamId IS NULL

	DECLARE @TotalUnassignedLead INT = (SELECT COUNT(*) FROM #TempTable)
	DECLARE @AssignedOrganizationId UNIQUEIDENTIFIER

	DECLARE @iIndex INT = 1
	DECLARE @AppointmentId UNIQUEIDENTIFIER
	DECLARE @LeadAssignmentId UNIQUEIDENTIFIER

	WHILE @iIndex <= @TotalUnassignedLead
	BEGIN

		SELECT @AppointmentId = AppointmentId , @LeadAssignmentId=LeadAssignmentId FROM #TempTable WHERE RowIndex = @iIndex
		SET @AssignedOrganizationId = NULL

		-- Lấy các nhóm thuộc khu vực "hẹn hò", có tỷ lệ được chia đang nhỏ nhất.
		SELECT	TOP 1 @AssignedOrganizationId = TeamId
		FROM	#CurrentAppointmentDistributionStatus temp
				JOIN dbo.OrganizationWorkingArea orgWork ON orgWork.OrganizationId = temp.TeamId
				JOIN dbo.Appointment ap ON ap.ProvinceId = orgWork.ProvinceId
		WHERE	ap.Id = @AppointmentId
		ORDER BY CAST(AppointmentAssignedCount AS FLOAT)/FieldSaleCount, FieldSaleCount DESC

		-- Lấy được nhóm rồi, phân bổ cho nhóm đó đi
		UPDATE dbo.LeadAssignment SET AssignedFieldSaleTeamId = @AssignedOrganizationId, AssignedFieldSaleTeamDate = GETDATE(), Status = 2 WHERE Id = @LeadAssignmentId
  
		-- Ghi sổ cho team em nó thêm 1 hẹn 
		UPDATE #CurrentAppointmentDistributionStatus SET AppointmentAssignedCount=AppointmentAssignedCount+1 WHERE TeamId=@AssignedOrganizationId
		-- Lưu ý: Có trường hợp sai khi một team phục vụ 2 tỉnh --> nên có constraint một team chỉ phục vụ trong phạm vi một tỉnh

		SET @iIndex = @iIndex + 1

	END

		INSERT INTO @New( LeadAssignmentId ,AssignedFieldSaleId ,AssignedFieldSaleTeamId ,LeadAssignmentStatus ) 
			SELECT LA.Id,AssignedFieldSaleId, AssignedFieldSaleTeamId, la.Status
			FROM	dbo.Appointment ap
			JOIN dbo.LeadAssignment la ON la.WaitingAppointmentId = ap.Id
			WHERE ap.MeetDate BETWEEN @FromDate AND @ToDate


	--SELECT * FROM @Old
	--SELECT * FROM @New
	EXEC dbo.AppointmentAudit @_Old = @Old, @_New = @New, @UserId=@UserId
******/
END
GO