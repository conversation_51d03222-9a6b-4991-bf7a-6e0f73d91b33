using AutoMapper;
using System;

namespace TinyCRM.Outbound.Prospect.Queries
{
    public class ProspectData
    {
        public Guid Id
        {
            get;
            set;
        }

        public Guid CustomerId
        {
            get;
            set;
        }

        public Guid CampaignId
        {
            get;
            set;
        }

        public Guid? ReferenceObjectId
        {
            get;
            set;
        }

        public string ReferenceObjectType
        {
            get; set;
        }

        public Guid? ReferenceResultId
        {
            get; set;
        }

        public string ReferenceResultType
        {
            get; set;
        }

        public ProspectStatus Status
        {
            get;
            set;
        }

        public Guid? CurrentAssignmentId
        {
            get;
            set;
        }

        public DateTime? NextCallbackDate
        {
            get;
            set;
        }

        public DateTime? BackToCommonBasketDate
        {
            get;
            set;
        }

        public DateTime? ReprospectDate
        {
            get;
            set;
        }

        public string Notes
        {
            get;
            set;
        }

        public DateTime? CreatedDate
        {
            get;
            set;
        }

        public Guid? CreatedBy
        {
            get;
            set;
        }

        public bool IsHot
        {
            get;
            set;
        }

        public Guid? HotListGroupId
        {
            get;
            set;
        }

        public Guid? CallResultId
        {
            get;
            set;
        }

        public Guid? PreviousPaCallResultId
        {
            get;
            set;
        }

        public bool? AlertCallbackDate { get; set; }

        public string AdditionalJsonData { get; set; }

        public Guid? DigitalContactId { get; set; }

        
    }
}
