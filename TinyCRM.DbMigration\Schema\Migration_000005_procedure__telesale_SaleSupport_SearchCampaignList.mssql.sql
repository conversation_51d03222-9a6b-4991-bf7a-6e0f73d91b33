
/****** Object:  StoredProcedure [telesale].[SaleSupport_SearchCampaignList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[SaleSupport_SearchCampaignList]

	@CampaignName		NVARCHAR(500),
	@CampaignStatus		INT,
	@StartRow			INT,
	@EndRow				INT

AS
BEGIN

	WITH cte AS
	(
		SELECT	c.*,
				ROW_NUMBER() OVER (ORDER BY c.CreatedDate DESC) RowNumber
		FROM	dbo.Campaign c
		WHERE	c.Deleted = 0
				AND (ISNULL(@CampaignName,'')='' OR c.CampaignName LIKE N'%' + @CampaignName + '%')
				AND (@CampaignStatus IS NULL OR c.Status = @CampaignStatus)
	)
	SELECT	c.*, ISNULL(up.FullName,u.UserName) CreatedByUser,
			(SELECT COUNT(*) FROM cte) TotalCount
	FROM	cte c
			LEFT JOIN dbo.aspnet_Users u ON u.UserId = c.CreatedBy
			LEFT JOIN dbo.UserProfiles up ON up.Id = u.UserId
	WHERE	c.RowNumber BETWEEN @StartRow AND @EndRow
	
END
GO