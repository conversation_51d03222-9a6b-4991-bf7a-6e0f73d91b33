
/****** Object:  StoredProcedure [telesale].[ImportAppointmentFromExcel]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[ImportAppointmentFromExcel] 
	@UserId UNIQUEIDENTIFIER,
	@File VARBINARY(MAX),
	@RawTable AppointmentRawType READONLY
AS
BEGIN
	SET NOCOUNT ON;
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @ErrorSeverity INT;
    DECLARE @ErrorState INT;

	DECLARE @new_iis UNIQUEIDENTIFIER = NEWID(); 
	EXEC telesale.NewAppointmentRaw @UserId = @UserId,
	    @ImportSessionId = @new_iis,
	    @File = @File,
	    @RawTable = @RawTable;

	--validate
	UPDATE ar SET
	ar.IsInvalid=1,
	ar.InvalidNote=(
		IIF(tmr.Id IS NULL, N'Không nhận diện được TMR - Sai TMR Code',
		IIF(p.Id IS NULL, N'Không nhận diện được tỉnh/thành của khách hàng',
		IIF(ar.AppointmentTime IS NULL, N'Không xác định được thời gian hẹn',
		IIF((ar.CellPhone IS NULL OR ar.CellPhone=N'') AND (ar.HomePhone IS NULL OR ar.HomePhone=N''), N'Không xác định được số điện thoại của khách hàng', N''))))
	)
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles tmr ON ar.CodeTMR=tmr.AgentCode
	LEFT JOIN dbo.Province p ON ar.Province=p.ImportCodeName
	WHERE (tmr.Id IS NULL OR p.Id IS NULL OR ar.AppointmentTime IS NULL
	OR (ar.CellPhone IS NULL OR ar.CellPhone=N'') AND (ar.HomePhone IS NULL OR ar.HomePhone=N''))
	AND ar.ImportSessionId=@new_iis

	--check contact is exist
	UPDATE ar SET
	ar.IsDupContact=1,
	ar.ContactDupId=(SELECT TOP 1 c.Id),
	ar.ContactId=(SELECT TOP 1 c.Id),
	ar.ProspectId=IIF((SELECT TOP 1 p.Id) IS NULL, NEWID(), (SELECT TOP 1 p.Id))
	FROM dbo.Contact c
	JOIN dbo.AppointmentRaw ar ON ar.CellPhone=c.Phone -- OR ar.HomePhone IN (c.Phone, c.Phone2, c.Phone3)
	LEFT JOIN dbo.Prospect p ON p.ContactId=c.Id
	WHERE c.Id IS NOT NULL
	AND ar.ImportSessionId=@new_iis AND ar.IsInvalid=0

	--check prospect assignment is active
	UPDATE ar SET
	ar.IsDupPa=1,
	ar.PaDupId=(SELECT TOP 1 pa.Id)
	FROM dbo.Prospect p
	JOIN dbo.ProspectAssignment pa ON p.CurrentAssignmentId=pa.Id
	JOIN dbo.Contact c ON p.ContactId=c.Id
	JOIN dbo.AppointmentRaw ar ON ar.CellPhone=c.Phone -- OR ar.HomePhone IN (c.Phone, c.Phone2, c.Phone3)
	WHERE pa.Id IS NOT NULL
	AND ar.ImportSessionId=@new_iis AND ar.IsInvalid=0

	--check appointment is exist
	UPDATE ar SET
	ar.IsDupApp=1,
	ar.AppDupId=(SELECT TOP 1 a.Id)
	FROM dbo.Appointment a
	JOIN dbo.Contact c ON a.ContactId=c.Id
	JOIN dbo.AppointmentRaw ar ON ar.CellPhone=c.Phone --OR ar.HomePhone IN (c.Phone, c.Phone2, c.Phone3)
	WHERE a.Id IS NOT NULL
	AND ar.ImportSessionId=@new_iis AND ar.IsInvalid=0

	--check prospect assignment is active & appointment is not exist
	UPDATE pa SET
	pa.Status=3, -- close
	pa.UnassignedBy=@UserId,
	pa.UnassignedDate=GETDATE(),
	pa.ClosedReason=5
	FROM dbo.Prospect p
	JOIN dbo.ProspectAssignment pa ON p.CurrentAssignmentId=pa.Id
	JOIN dbo.AppointmentRaw ar ON ar.PaDupId=pa.Id
	WHERE ar.IsDupPa=1 AND ar.IsDupApp=0
	AND ar.ImportSessionId=@new_iis
	UPDATE p SET
	p.CurrentAssignmentId=ar.ProspectAssignmentId
	FROM dbo.Prospect p
	JOIN dbo.ProspectAssignment pa ON p.CurrentAssignmentId=pa.Id
	JOIN dbo.AppointmentRaw ar ON ar.PaDupId=pa.Id
	WHERE ar.IsDupPa=1 AND ar.IsDupApp=0
	AND ar.ImportSessionId=@new_iis AND ar.IsInvalid=0

	--un-blacklist
	UPDATE c SET c.Inactive=0
	FROM dbo.Contact c
	JOIN dbo.AppointmentRaw ar ON c.Id=ar.ContactId
	WHERE ar.IsDupApp=0 AND ar.IsInvalid=0 AND ar.ImportSessionId=@new_iis

	BEGIN TRANSACTION
		BEGIN TRY
			EXEC telesale.CreateContactFromAppointmentRaw @UserId = @UserId, @SessionId=@new_iis;
			EXEC telesale.CreateContactRelationshipFromAppointmentRaw @UserId = @UserId, @SessionId = @new_iis
			EXEC telesale.CreateProspectFromAppointmentRaw @UserId = @UserId, @SessionId=@new_iis;
			EXEC telesale.CreateProspectAssignmentFromAppointmentRaw @UserId = @UserId, @SessionId=@new_iis;
			EXEC telesale.CreateContactCallFromAppointmentRaw @UserId = @UserId, @SessionId=@new_iis;
			EXEC telesale.CreateLeadFromAppointmentRaw @UserId = @UserId, @SessionId=@new_iis;
			EXEC telesale.CreateLeadAssignmentFromAppointmentRaw @UserId = @UserId, @SessionId=@new_iis;
			EXEC telesale.CreateAppointmentFromAppointmentRaw @UserId = @UserId, @SessionId=@new_iis;
		END TRY
		BEGIN CATCH
			SET @ErrorMessage = ERROR_MESSAGE();
			SET @ErrorSeverity = ERROR_SEVERITY();
			SET @ErrorState = ERROR_STATE();
			RAISERROR (@ErrorMessage, @ErrorSeverity,@ErrorState);
	ROLLBACK TRANSACTION
		END CATCH
	COMMIT TRANSACTION
	SELECT ar.*, la.Id LeadDupId FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.Appointment a ON ar.AppDupId=a.Id
	LEFT JOIN dbo.LeadAssignment la ON la.Id=a.LeadAssignmentId
	WHERE ImportSessionId=@new_iis
	ORDER BY ar.IsInvalid DESC, ar.InvalidNote
******/
END
GO