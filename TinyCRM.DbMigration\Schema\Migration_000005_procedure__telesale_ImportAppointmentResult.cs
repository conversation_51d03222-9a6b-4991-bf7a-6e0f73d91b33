using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using System.Reflection;
using Webaby.Data;

namespace TinyCRM.DbMigration.Schema
{
    [DbContext(typeof(MigrationDbContext))]
    [Migration(nameof(Migration_000005_procedure__telesale_ImportAppointmentResult))]
    internal class Migration_000005_procedure__telesale_ImportAppointmentResult : RawMigration
    {
        protected override Assembly ResourceAssembly => Assembly.GetExecutingAssembly();
    }
}