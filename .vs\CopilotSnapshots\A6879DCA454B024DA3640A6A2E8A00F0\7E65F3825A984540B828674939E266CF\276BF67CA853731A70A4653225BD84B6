﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFieldInFormByOrderQuery : QueryBase<DynamicFieldDefinitionData>
    {
        public Guid? FieldId { get; set; }
        public int Order { get; set; }
        public Guid FormId { get; set; }
    }

    internal class GetDynamicFieldInFormByOrderQueryHandler : QueryHandlerBase<GetDynamicFieldInFormByOrderQuery, DynamicFieldDefinitionData>
    {
        public GetDynamicFieldInFormByOrderQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        {
        }

        public override async Task<QueryResult<DynamicFieldDefinitionData>> ExecuteAsync(GetDynamicFieldInFormByOrderQuery query)
        {
            // L<PERSON>y tất cả các trường động của form
            var allFields = EntitySet.Get<DynamicFieldDefinitionEntity>();

            // Loại bỏ trường có FieldId nếu được chỉ định
            if (query.FieldId.HasValue)
            {
                allFields = allFields.Where(field => field.Id != query.FieldId.Value);
            }

            // Lọc theo FormId và Order
            var filteredFields = allFields
                .Where(field => field.DynamicFormId == query.FormId && field.Order == query.Order);

            // Trả về kết quả đã ánh xạ sang DTO
            return QueryResult.Create(filteredFields, field => Mapper.Map<DynamicFieldDefinitionData>(field));
        }
    }
}

