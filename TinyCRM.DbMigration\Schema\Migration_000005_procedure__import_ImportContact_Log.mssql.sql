
/****** Object:  StoredProcedure [import].[ImportContact_Log]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[ImportContact_Log]
	@ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
	INSERT INTO [import].[ContactRaw_Log]
           ([Id]
           ,[ImportSessionId]
           ,[Source]
           ,[FullName]
           ,[PhoneNo]
           ,[Areas]
           ,[Address]
           ,[DOB]
           ,[Gender]
           ,[Job]
           ,[Income]
           ,[MaritalStatus]
           ,[Note]
           ,[PhoneValid]
           ,[DOBValid]
           ,[ProvinceValid]
           ,[IsDupInternal]
           ,[DupWithMaster]
           ,[ErrorCode]
           ,[WarningCode]
           ,[ContactId]
           ,[BackSystemContactId]
           ,[QualifiedProgram]
           ,[CompanyName]
           ,[CompanyAddress]
           ,[CompanyType]
           ,[CompanyPhone]
           ,[IncomeSource]
           ,[Nationality]
           ,[DistrictId]
           ,[WardId]
           ,[AddressMailing]
           ,[ProvinceMailingId]
           ,[DistrictMailingId]
           ,[WardMailingId]
           ,[AddressPermanent]
           ,[ProvincePermanentId]
           ,[DistrictPermanentId]
           ,[WardPermanentId]
           ,[ProvincePermanent]
           ,[DistrictPermanent]
           ,[WardPermanent]
           ,[ProvinceMailing]
           ,[DistrictMailing]
           ,[WardMailing]
           ,[District]
           ,[Ward]
           ,[GenderValid]
           ,[MaritalStatusValid]
           ,[DataQuality]
           ,[IncomeValid]
           ,[DataQualityValid]
		   ,[CustomerId])
	SELECT [Id]
           ,[ImportSessionId]
           ,[Source]
           ,[FullName]
           ,[PhoneNo]
           ,[Areas]
           ,[Address]
           ,[DOB]
           ,[Gender]
           ,[Job]
           ,[Income]
           ,[MaritalStatus]
           ,[Note]
           ,[PhoneValid]
           ,[DOBValid]
           ,[ProvinceValid]
           ,[IsDupInternal]
           ,[DupWithMaster]
           ,[ErrorCode]
           ,[WarningCode]
           ,[ContactId]
           ,[BackSystemContactId]
           ,[QualifiedProgram]
           ,[CompanyName]
           ,[CompanyAddress]
           ,[CompanyType]
           ,[CompanyPhone]
           ,[IncomeSource]
           ,[Nationality]
           ,[DistrictId]
           ,[WardId]
           ,[AddressMailing]
           ,[ProvinceMailingId]
           ,[DistrictMailingId]
           ,[WardMailingId]
           ,[AddressPermanent]
           ,[ProvincePermanentId]
           ,[DistrictPermanentId]
           ,[WardPermanentId]
           ,[ProvincePermanent]
           ,[DistrictPermanent]
           ,[WardPermanent]
           ,[ProvinceMailing]
           ,[DistrictMailing]
           ,[WardMailing]
           ,[District]
           ,[Ward]
           ,[GenderValid]
           ,[MaritalStatusValid]
           ,[DataQuality]
           ,[IncomeValid]
           ,[DataQualityValid]
		   ,[CustomerId]
	FROM import.ContactRaw
END
GO