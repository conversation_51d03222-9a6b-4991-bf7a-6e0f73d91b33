﻿using System;
using System.Linq;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFormByCodeQuery : QueryBase<DynamicFormData>
    {
        public string Code { get; set; }
    }

    internal class GetDynamicFormByCodeQueryHandler : QueryHandlerBase<GetDynamicFormByCodeQuery, DynamicFormData>
    {
        public override QueryResult<DynamicFormData> Execute(GetDynamicFormByCodeQuery query)
        {
            var mainQuery = EntitySet.Get<DynamicFormEntity>().Where(df => df.Code == query.Code);
            return QueryResult.Create(mainQuery, DynamicFormData.FromEntity);
        }
    }
}