﻿using AutoMapper;
using Webaby.Core.Organization;
using Webaby.Core.Organization.Queries;
using Webaby.Core.Role;
using Webaby.Core.Role.Queries;
using Webaby.Core.Access.Queries;
using Webaby.Security;

namespace Webaby.Core
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        { 
            // Organization
            CreateMap<OrganizationEntity, OrganizationData>();
            // Role
            CreateMap<AspNetRoleEntity, RoleData>();
            // RoleBusinessPermission
            CreateMap<RoleBusinessPermissionEntity, RoleBusinessPermissionData>();
        }
    }
}