
/****** Object:  StoredProcedure [telesale].[Contact_GetStatisticsImportHotList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[Contact_GetStatisticsImportHotList]

	@ImportSessionId		UNIQUEIDENTIFIER,
	@CampaignId				UNIQUEIDENTIFIER

AS
BEGIN

	SELECT	COUNT(*) ItemCount, N'Tổng số dữ liệu' StatisticName, 1 DisplayOrder, 'Sum' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
	UNION
	SELECT	COUNT(*) ItemCount, 'Trùng trong file' StatisticName, 3 DisplayOrder, 'NotDistributed' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND DuplicatedCase = 1
	UNION
	SELECT	COUNT(*) ItemCount, 'Trùng trong file' StatisticName, 3 DisplayOrder, 'NotDistributed' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND DuplicatedCase = 1
	UNION
	SELECT	COUNT(*) ItemCount, DataErrorMessage StatisticName, 4 DisplayOrder, 'NotDistributed' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND ISNULL(DataErrorMessage,'') <> ''
			AND isnull(DuplicatedCase,0) = -1
	GROUP BY DataErrorMessage
	UNION
	SELECT	COUNT(*) ItemCount, N'Trong rổ TMR, Ưu tiên' StatisticName, 5 DisplayOrder, 'NotDistributed' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND DuplicatedCase = 5
	UNION
	SELECT	COUNT(*) ItemCount, N'Trong rổ TMR, Thường-đang theo' StatisticName, 6 DisplayOrder, 'NotDistributed' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND DuplicatedCase = 7
	UNION
	SELECT	COUNT(*) ItemCount, N'Tổng số được phân bổ' StatisticName, 7 DisplayOrder, 'Sum' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND ToBeHotDistributePaId IS NOT NULL
	UNION
	SELECT	COUNT(*) ItemCount, N'Phân bổ lại (Chờ 90 ngày)' StatisticName, 8 DisplayOrder, 'Distributed' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND DuplicatedCase = 2
	UNION
	SELECT	COUNT(*) ItemCount, N'Không tiềm năng' StatisticName, 9 DisplayOrder, 'Distributed' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND DuplicatedCase = 3
	UNION
	SELECT	COUNT(*) ItemCount, N'Trong rổ sup' StatisticName, 10 DisplayOrder, 'Distributed' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND DuplicatedCase = 4	
	UNION
	SELECT	COUNT(*) ItemCount, N'Trong rổ TMR, Thường-chưa gọi' StatisticName, 11 DisplayOrder, 'Distributed' ResultType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND DuplicatedCase = 6
	

END
GO