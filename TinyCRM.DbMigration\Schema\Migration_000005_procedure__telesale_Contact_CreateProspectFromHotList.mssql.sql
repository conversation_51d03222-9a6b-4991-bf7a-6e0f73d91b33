﻿
/****** Object:  StoredProcedure [telesale].[Contact_CreateProspectFromHotList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[Contact_CreateProspectFromHotList]
	@ImportSessionId		UNIQUEIDENTIFIER,
	@CampaignId				UNIQUEIDENTIFIER,
	@ImportedUser			UNIQUEIDENTIFIER
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	-- Create contacts from hot list, without duplicated contacts in master data
	INSERT INTO dbo.Contact
	        ( 
				Id,
				FullName,
				Address,
				ProvinceId,
				Phone,
				DOB,
				DataSource,
				CreatedDate,
				Status,
				Job,
				Inactive,
				Notes,
				CreatedBy,
				Income,
				MaritalStatus,
				Gender
	        )
	SELECT	sc.NewContactId,
			sc.FullName,
            sc.Address,
			(SELECT TOP 1 Id FROM dbo.Province WHERE ProvinceName LIKE N'%' + sc.Province + '%' OR sc.Province LIKE N'%' + ProvinceName + '%'),
			sc.Phone,
			sc.DOB,
			sc.Source,
			GETDATE(),
			1,
			sc.Job,
			0,
			sc.Notes,
			@ImportedUser,
			sc.Income,
			CASE sc.Gender WHEN N'Unknown' THEN 0 WHEN N'Male' THEN 1 WHEN N'Female' THEN 2 ELSE 0 END,
			CASE sc.MaritalStatus WHEN N'Unknown' THEN 0 WHEN N'Single' THEN 1 WHEN N'Married' THEN 2 ELSE 0 END
	FROM	dbo.StagingContact sc
			LEFT JOIN dbo.Contact c ON c.Phone = sc.Phone
	WHERE	sc.ImportSessionId = @ImportSessionId AND sc.NewContactId IS NOT NULL
			AND c.Id IS NULL

	-- Create prospect from host list, without duplicated prospect in campaign
	INSERT	dbo.Prospect (Id, ContactId, CampaignId, Status, CreatedBy, CreatedDate, IsHot)
	SELECT	sc.NewProspectId, ISNULL(sc.NewContactId,sc.CurrentContactId), @CampaignId, 1, @ImportSessionId, GETDATE(), 1
	FROM	dbo.StagingContact sc
	WHERE	sc.ImportSessionId = @ImportSessionId AND sc.NewProspectId IS NOT NULL
			AND ISNULL(DuplicatedCase,0)=0
			--AND p.Id IS NULL -- Contacts have not been added to campaign
******/
END
GO