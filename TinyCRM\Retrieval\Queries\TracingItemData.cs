using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;

namespace TinyCRM.Retrieval.Queries
{
    public class TracingItemData
    {
        public Guid Id { get; set; }

        public Guid CustomerId { get; set; }

        public string CustomerName { get; set; }

        public string CustomerCode { get; set; }

        public Guid RetrievalId { get; set; }

        public Guid ProductId { get; set; }

        public string ProductName { get; set; }

        public string ProductCode { get; set; }

        public Guid FactoryId { get; set; }

        public string FactoryName { get; set; }

        public string FactoryCode { get; set; }

        public DateTime ExpiredDate { get; set; }

        public int Quantity { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; }

        public Guid? ModifiedBy { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public Boolean Deleted { get; set; }

        public Guid? DeletedBy { get; set; }

        public DateTime? DeletedDate { get; set; }

        
    }
}

