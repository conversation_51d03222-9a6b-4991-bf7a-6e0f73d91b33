﻿using System;
using System.Collections.Generic;
using TinyCRM.Enums;
using TinyCRM.Outbound.Contact;

namespace TinyCRM.AppServices.RequestTicket.Dto
{
    public class RequestTicketCustomer
    {
        public Guid? Id { get; set; }

        public string Name { get; set; }

        public string Code { get; set; }

        public string CompanyType { get; set; }

        public string B2BCode { get; set; }

        public CustomerType Type { get; set; }

        public Guid SourceClassificationId { get; set; }

        public string CMND { get; set; }

        public string SourceClassification { get; set; }

        public string SubName { get; set; }

        public DateTime? Dob { get; set; }

        public bool CustomerClass { get; set; }

        public long? CreditLimit { get; set; }

        public Enums.Gender Sex { get; set; }

        public Guid? ProvinceId { get; set; }
        public Guid? DistrictId { get; set; }
        public Guid? WardId { get; set; }

        public string Address { get; set; }

        public string AddressStreet { get; set; }

        public string AddressNumber { get; set; }

        public string Phone1 { get; set; }

        public string Phone2 { get; set; }

        public string Phone3 { get; set; }

        public string Email { get; set; }

        public string FacebookId { get; set; }

        public string WorkAddress { get; set; }

        public string Job { get; set; }

        public string ContactPhone { get; set; }

        public string TaxNumber { get; set; }

        public string LicenseType { get; set; }

        public string License { get; set; }

        public DateTime? LicenseDate { get; set; }

        public string Avatar { get; set; }

        public string Background { get; set; }

        public DateTime? LicenseExpired { get; set; }

        public string LicensePlace { get; set; }

        public string OriginNation { get; set; }

        public string Nation { get; set; }

        public string BankID { get; set; }

        public string LocationID { get; set; }

        public string Residence { get; set; }

        public string Status { get; set; }

        public string Notes { get; set; }

        public Guid? CustomerAlternativeAddressId { get; set; }

        public Guid? AppartmentId { get; set; }

        public bool IsBackendCustomer { get; set; }

        public MaritalStatus? MaritalStatus { get; set; }

        public bool AllowEdit { get; set; }

        public string BackendErrorMessage { get; set; }

        public bool IsPrimary { get; set; }

        public Guid? AdditionalTemplateId { get; set; }

        public string AdditionalData { get; set; }
        public Dictionary<string, Dictionary<string, string>> AdditionalDataDic { get; set; }

        public Guid? HotListGroupId { get; set; }
    }
}