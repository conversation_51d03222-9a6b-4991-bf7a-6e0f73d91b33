﻿using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Microsoft.EntityFrameworkCore; // Thêm nếu dùng EF Core

namespace Webaby.Core.Access.Queries
{
    public class GetApiAccessQuery : QueryBase<AccessData>
    {
        public string UrlPart { get; set; }
        public HttpMethods Method { get; set; }
    }

    internal class GetApiAccessQueryHandler : QueryHandlerBase<GetApiAccessQuery, AccessData>
    {
        public GetApiAccessQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override Task<QueryResult<AccessData>> ExecuteAsync(GetApiAccessQuery query)
        {
            var queryable = EntitySet.Get<AccessEntity>()
                .Where(access => access.ActionName == query.UrlPart && access.Method == query.Method);

            // Nếu dùng EF Core, tối ưu hóa chỉ đọc
            // queryable = queryable.AsNoTracking();

            if (query.Pagination != null)
            {
                return Task.FromResult(QueryResult.Create(queryable, query.Pagination, x => Mapper.Map<AccessData>(x)));
            }
            return Task.FromResult(QueryResult.Create(queryable, x => Mapper.Map<AccessData>(x)));
        }
    }
}

