
/****** Object:  StoredProcedure [telesale].[Prospect_AssignedHotProspects]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

--ALTER PROCEDURE [telesale].[Prospect_AssignedHotProspects]
CREATE PROCEDURE [telesale].[Prospect_AssignedHotProspects]

	@ImportSessionId			UNIQUEIDENTIFIER,
	@HotListGroupId				UNIQUEIDENTIFIER,
	@CampaignId					UNIQUEIDENTIFIER,
	@HotAgentAssignedNumbers	dbo.ObjectNumberList READONLY,
	@DistributedDate			DATETIME,
	@DistributedUserId			UNIQUEIDENTIFIER

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	BEGIN TRANSACTION DistributeImportedData
	
	BEGIN TRY

		DECLARE @DataSource NVARCHAR(MAX)
		SELECT	@DataSource = GroupName
		FROM	telesale.HotListGroup
		WHERE	Id = @HotListGroupId
	
		--=================================================================
		-- Tạo Contact mới
		--=================================================================
		INSERT INTO dbo.Contact
				( 
					Id,
					FullName,
					Address,
					ProvinceId,
					Phone,
					DOB,
					DataSource,
					CreatedDate,
					Status,
					Job,
					Inactive,
					Notes,
					CreatedBy,
					Income,
					MaritalStatus,
					Gender
				)
		SELECT	sc.NewContactId,
				sc.FullName,
				sc.Address,
				(SELECT TOP 1 Id FROM dbo.Province WHERE ImportCodeName LIKE N'%' + sc.Province + '%' OR sc.Province LIKE N'%' + ProvinceName + '%'),
				sc.Phone,
				sc.DOB,
				@DataSource,
				GETDATE(),
				1,
				sc.Job,
				0,
				sc.Notes,
				@DistributedUserId,
				sc.Income,
				CASE sc.Gender WHEN N'Unknown' THEN 0 WHEN N'Male' THEN 1 WHEN N'Female' THEN 2 ELSE 0 END,
				CASE sc.MaritalStatus WHEN N'Unknown' THEN 0 WHEN N'Single' THEN 1 WHEN N'Married' THEN 2 ELSE 0 END
		FROM	dbo.StagingContact sc
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.NewContactId IS NOT NULL
				AND sc.DuplicatedCase IS NULL

		--=================================================================
		-- Tạo Prospect mới
		--=================================================================
		INSERT	dbo.Prospect (Id, ContactId, CampaignId, Status, CreatedBy, CreatedDate, IsHot, CurrentAssignmentId)
		SELECT	sc.NewProspectId, ISNULL(sc.NewContactId,sc.CurrentContactId), @CampaignId, 1, @DistributedUserId, GETDATE(), 1, sc.NewProspectAssignmentId
		FROM	dbo.StagingContact sc
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.NewProspectId IS NOT NULL
				AND sc.DuplicatedCase IS NULL

		--=================================================================
		-- Tạo ProspectAssignment mới
		--=================================================================
		INSERT	dbo.ProspectAssignment (Id, ProspectId, Status, CreatedDate, CreatedBy, CreatedReason, EnterCampaignBasketReason, PreviousProspectAssingmentId)
		SELECT	sc.NewProspectAssignmentId, ISNULL(sc.NewProspectId,sc.CurrentProspectId), 1, GETDATE(), @DistributedUserId, 12, 5, sc.CurrentProspectAssignmentId
		FROM	dbo.StagingContact sc
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1, 5, 7)
				AND sc.NewProspectAssignmentId IS NOT NULL

		--=================================================================
		-- Update CurrentAssignmentId cho các Prospect đã có (Các trường hợp lấy về)
		--=================================================================
		UPDATE	p
		SET		CurrentAssignmentId = sc.NewProspectAssignmentId
		FROM	dbo.Prospect p
				JOIN dbo.StagingContact sc ON sc.CurrentProspectId = p.Id
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.CurrentProspectId IS NOT NULL
				AND sc.NewProspectAssignmentId IS NOT NULL
				AND DuplicatedCase IN (3, 4, 6) -- Các trường hợp lấy về

		--=================================================================
		-- Update Status, ClosedReason, Unassigned cho các PA closed
		--=================================================================
		UPDATE	pa
		SET		Status = 3, ClosedReason = 12, UnassignedBy = @DistributedUserId, UnassignedDate = GETDATE()
		FROM	dbo.StagingContact sc
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.CurrentProspectAssignmentId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND DuplicatedCase IN (3, 4, 6) -- Các trường hợp lấy về

		--=================================================================
		-- Update CreatedReason cho tất cả các PA được phân bổ
		--=================================================================
		UPDATE	pa
		SET		CreatedReason = 12
		FROM	dbo.StagingContact sc
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.ToBeHotDistributePaId IS NOT NULL
				AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1, 5, 7)

		--=================================================================
		-- Update Ignore15DaysRule, Status cho PA
		--=================================================================
		UPDATE	pa
		SET		Ignore15DaysRule = 1, pa.Status = 1
		FROM	dbo.StagingContact sc 
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1, 5, 7)

		--=================================================================
		-- Update IsHot cho tất cả các Case được phân bổ
		-- 90 ngày, set null ReprospectDate
		--=================================================================
		UPDATE	p
		SET		IsHot=1, HotListGroupId=@HotListGroupId, p.ReprospectDate=NULL, p.Status=2
		FROM	dbo.StagingContact sc 
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
				JOIN dbo.Prospect p ON p.Id=pa.ProspectId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1, 5, 7)

		--=================================================================
		-- Update lại Status, DataSource cho Contact
		--=================================================================
		UPDATE	c
		SET		Status=1, DataSource = @DataSource
		FROM	dbo.StagingContact sc 
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
				JOIN dbo.Prospect p ON p.Id=pa.ProspectId
				JOIN dbo.Contact c ON c.Id=p.ContactId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1, 5, 7)

		
		--=================================================================
		-- Bắt đầu việc phân bổ xuống HOT TMRs
		--=================================================================

		DECLARE @HotAgentAssignNumbersTable TABLE (
			iIndex		INT PRIMARY KEY IDENTITY(1,1),
			Id			UNIQUEIDENTIFIER,
			Number		INT,
			StartRow	INT,
			EndRow		INT
		)

		INSERT	@HotAgentAssignNumbersTable(Id, Number)
		SELECT	Id, Number
		FROM	@HotAgentAssignedNumbers
		ORDER BY SortOrder

		UPDATE	hot
		SET		StartRow = temp.StartRow,
				EndRow = temp.EndRow
		FROM	@HotAgentAssignNumbersTable hot
				JOIN
				(
					SELECT	Id, ((SUM(Number) OVER (ORDER BY iIndex)) - Number + 1) StartRow, (SUM(Number) OVER (ORDER BY iIndex)) EndRow
					FROM	@HotAgentAssignNumbersTable
				) temp ON temp.Id = hot.Id

		UPDATE	pa
		SET		AssignedTeamId = temp.TeamId, AssignedAgentId = temp.AgentId, AssignedTeamDate = @DistributedDate, AssignedAgentDate = @DistributedDate, Ignore15DaysRule = 1
		FROM	dbo.ProspectAssignment pa
				JOIN
				(
					SELECT	hotAgent.Id AgentId, up.OrganizationId TeamId, temp.Id ProspectAssignmentId
					FROM	@HotAgentAssignNumbersTable hotAgent
							JOIN dbo.UserProfiles up ON up.Id = hotAgent.Id
							JOIN 
							(
								SELECT	sc.ToBeHotDistributePaId Id, ROW_NUMBER() OVER (ORDER BY ToBeHotDistributePaId) RowNumber
								FROM	dbo.StagingContact sc
								WHERE	sc.ImportSessionId = @ImportSessionId
										AND sc.ToBeHotDistributePaId IS NOT NULL
										AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1, 5, 7)
							) temp ON temp.RowNumber BETWEEN hotAgent.StartRow AND hotAgent.EndRow
				) temp ON temp.ProspectAssignmentId = pa.Id

		UPDATE	sc
		SET		AssignedTMRId = pa.AssignedAgentId
		FROM	dbo.StagingContact sc
				JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND sc.ToBeHotDistributePaId IS NOT NULL
				AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1, 5, 7)

		--=================================================================
		-- Cập nhật các thông tin liên hệ nếu đang blank
		--=================================================================
		UPDATE	c
		SET		FullName = CASE WHEN ISNULL(c.FullName,'') = '' THEN sc.FullName ELSE c.FullName END,
				ProvinceId = CASE WHEN c.ProvinceId IS NULL THEN pv.Id ELSE c.ProvinceId END,
				DOB = CASE WHEN c.DOB IS NULL THEN sc.DOB ELSE c.DOB END,
				Job = CASE WHEN ISNULL(c.Job,'') = '' THEN sc.Job ELSE c.Job END,
				Income = CASE WHEN c.Income IS NULL THEN sc.Income ELSE c.Income END,
				[Address] = CASE WHEN ISNULL(c.[Address],'') = '' THEN sc.[Address] ELSE c.[Address] END,
				Gender = CASE 
							WHEN c.Gender = 0 THEN 
								CASE WHEN sc.Gender='Unknown' THEN 0 WHEN sc.Gender='Male' THEN 1 WHEN sc.Gender='Female' THEN 2 ELSE 0 END
							ELSE
								c.Gender
                         END,
				MaritalStatus =	CASE 
									WHEN c.MaritalStatus = 0 THEN 
										CASE WHEN sc.MaritalStatus='Unknown' THEN 0 WHEN sc.MaritalStatus='Single' THEN 1 WHEN sc.MaritalStatus='Married' THEN 2 ELSE 0 END
									ELSE
										c.MaritalStatus
								END,
				DataSource = CASE WHEN ISNULL(DuplicatedCase,0) NOT IN (5, 7) THEN sc.Source ELSE c.DataSource END
		FROM	dbo.Contact c
				JOIN dbo.StagingContact sc ON sc.CurrentContactId = c.Id
				JOIN dbo.Province pv ON pv.ImportCodeName = sc.Province
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1)

		--=================================================================
		-- Insert Notes history
		--=================================================================
		SELECT	NEWID() AuditId, sc.CurrentContactId, sc.NewContactId, sc.Notes, ISNULL(sc.CurrentContactId, sc.NewContactId) ContactId, sc.DuplicatedCase
		INTO	#TempNotesHistory
		FROM	dbo.StagingContact sc
				LEFT JOIN dbo.Contact c ON c.Id = ISNULL(sc.CurrentContactId, sc.NewContactId)
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND ISNULL(DuplicatedCase,0) NOT IN (-1, 1)
				AND
				(
					ISNULL(sc.Notes,'') <> ''
					OR (ISNULL(c.DataSource,'') <> @DataSource AND ISNULL(DuplicatedCase,0) NOT IN (5, 7))
				)


		INSERT INTO dbo.AuditEntityChange
		        ( 
					Id,
					Action,
					TableName,
					KeyValue,
					ModifiedBy,
					ModifiedDate
		        )
		SELECT	AuditId,
				CASE WHEN CurrentContactId IS NULL THEN 'Insert' ELSE 'Update' END,
				'dbo.Contact',
				ContactId,
				@DistributedUserId,
				GETDATE()
		FROM	#TempNotesHistory

		-- Log for Notes
		INSERT INTO dbo.AuditFieldChange
		        ( 
					Id,
					AuditId,
					MemberName,
					OldValue,
					NewValue
		        )
		SELECT	NEWID(),
				his.AuditId,
				'Notes',
				CASE WHEN his.CurrentContactId IS NOT NULL THEN c.Notes ELSE NULL END,
				his.Notes
		FROM	#TempNotesHistory his
				JOIN dbo.Contact c ON c.Id = his.ContactId
		WHERE	ISNULL(his.Notes,'') <> ''

		UPDATE	c
		SET		Notes = sc.Notes
		FROM	dbo.Contact c
				JOIN #TempNotesHistory sc ON sc.CurrentContactId = c.Id
		WHERE	ISNULL(sc.Notes,'') <> ''

		-- Log for DataSource
		INSERT INTO dbo.AuditFieldChange
		        ( 
					Id,
					AuditId,
					MemberName,
					OldValue,
					NewValue
		        )
		SELECT	NEWID(),
				his.AuditId,
				'DataSource',
				CASE WHEN his.CurrentContactId IS NOT NULL THEN c.DataSource ELSE NULL END,
				@DataSource
		FROM	#TempNotesHistory his
				JOIN dbo.Contact c ON c.Id = his.ContactId
		WHERE	ISNULL(his.DuplicatedCase,0) NOT IN (5, 7)

		UPDATE	c
		SET		DataSource = @DataSource
		FROM	dbo.Contact c
				JOIN #TempNotesHistory sc ON sc.CurrentContactId = c.Id
		WHERE	ISNULL(sc.DuplicatedCase,0) NOT IN (5, 7)

		-- Insert StagingContact vào Log
		INSERT INTO [dbo].[StagingContact_Log]
			(
				[Id]
				,[ImportSessionId]
				,[Source]
				,[Phone]
				,[FullName]
				,[Address]
				,[Province]
				,[DOB]
				,[Gender]
				,[Job]
				,[MaritalStatus]
				,[Income]
				,[Notes]
				,[DedupSolution]
				,[DupInSessionGroupId]
				,[RowOrder]
				,[DataErrorMessage]
				,[InternalDupGroupId]
				,[InternalDupGroupOrder]
				,[CurrentProspectAssignmentId]
				,[NewContactId]
				,[NewProspectId]
				,[NewProspectAssignmentId]
				,[ToBeHotDistributePaId]
				,[NeedClose]
				,[DuplicatedTMRTeamId]
				,[DuplicatedTMRId]
				,[AssignedTMRId]
				,[DuplicatedCase]
				,[CurrentProspectId]
				,[CurrentContactId]
			)
		SELECT	NEWID() [Id]
			 ,[ImportSessionId]
			 ,[Source]
			 ,[Phone]
			 ,[FullName]
			 ,[Address]
			 ,[Province]
			 ,[DOB]
			 ,[Gender]
			 ,[Job]
			 ,[MaritalStatus]
			 ,[Income]
			 ,[Notes]
			 ,[DedupSolution]
			 ,[DupInSessionGroupId]
			 ,[RowOrder]
			 ,[DataErrorMessage]
			 ,[InternalDupGroupId]
			 ,[InternalDupGroupOrder]
			 ,[CurrentProspectAssignmentId]
			 ,[NewContactId]
			 ,[NewProspectId]
			 ,[NewProspectAssignmentId]
			 ,[ToBeHotDistributePaId]
			 ,[NeedClose]
			 ,[DuplicatedTMRTeamId]
			 ,[DuplicatedTMRId]
			 ,[AssignedTMRId]
			 ,[DuplicatedCase]
			 ,[CurrentProspectId]
			 ,[CurrentContactId]
		FROM	dbo.StagingContact
		WHERE	ImportSessionId = @ImportSessionId

		COMMIT TRANSACTION DistributeImportedData;

	END TRY
    BEGIN CATCH
		ROLLBACK TRANSACTION DistributeImportedData;
		THROW
    END CATCH
******/
END
GO