
/****** Object:  StoredProcedure [telesale].[GetDistributedProspectSummary]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

--ALTER PROCEDURE [telesale].[GetDistributedProspectSummary] '0E197B45-2D50-90CA-56FB-0AE8AA9CF9EF', NULL, NULL
CREATE PROCEDURE [telesale].[GetDistributedProspectSummary]

	@AgentId		UNIQUEIDENTIFIER,
	@FromDate		DATETIME,
	@ToDate			DATETIME

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SELECT	CAST(pa.AssignedAgentDate AS DATE) AssignedDate, ISNULL(c.ProvinceId, '00000000-0000-0000-0000-000000000000') ProvinceId, pa.Status, pa.ClosedReason
	INTO	#TempTable
	FROM	dbo.ProspectAssignment pa
			JOIN dbo.Prospect p ON p.CurrentAssignmentId = pa.Id
			JOIN dbo.Contact c ON c.Id = p.ContactId
	WHERE	pa.AssignedAgentId = @AgentId
			AND (@FromDate IS NULL OR pa.AssignedAgentDate >= @FromDate)
			AND (@ToDate IS NULL OR pa.AssignedAgentDate < DATEADD(DAY, 1, @ToDate))

	SELECT	AssignedDate, ProvinceId, COUNT(*) TotalAssinged
	INTO	#TempAssinged
	FROM	#TempTable
	GROUP BY AssignedDate, ProvinceId

	SELECT	AssignedDate, ProvinceId, SUM(CASE WHEN Status <> 2 AND ISNULL(ClosedReason,0) <> 1 THEN 1 ELSE 0 END) TotalAssinged
	INTO	#TempNotCalled
	FROM	#TempTable
	GROUP BY AssignedDate, ProvinceId

	DECLARE @PivotColumns NVARCHAR(MAX)
	SELECT	@PivotColumns = COALESCE(@PivotColumns + ', ', '') + '[' + CAST(ProvinceId AS NVARCHAR(50)) + ']'
	FROM	(
				SELECT	DISTINCT ProvinceId
				FROM	#TempTable
			) temp

	SELECT	DISTINCT ISNULL(ProvinceId, '00000000-0000-0000-0000-000000000000') ProvinceId, ISNULL(ProvinceName, N'Không xác định') ProvinceName
	FROM	#TempTable temp
			LEFT JOIN dbo.Province p ON p.Id = temp.ProvinceId
	ORDER BY ISNULL(ProvinceName, N'Không xác định')

	DECLARE @ExecuteString NVARCHAR(MAX) = 
	'
		SELECT	*
		FROM	#TempAssinged temp
				PIVOT
				(
					SUM(TotalAssinged) FOR temp.ProvinceId IN (' + @PivotColumns + ')
				) tempPivot
		ORDER BY tempPivot.AssignedDate

		SELECT	*
		FROM	#TempNotCalled temp
				PIVOT
				(
					SUM(TotalAssinged) FOR temp.ProvinceId IN (' + @PivotColumns + ')
				) tempPivot
		ORDER BY tempPivot.AssignedDate
	'

	EXEC sp_executesql	@ExecuteString
******/
END
GO