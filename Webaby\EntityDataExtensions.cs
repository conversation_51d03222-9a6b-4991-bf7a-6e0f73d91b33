﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Collections.Specialized;
using Webaby.EntityData;
using Webaby.Web;

namespace Webaby
{
    public static class EntityDataExtensions
    {
        public static bool TryMapping(this ICollection<IEntityData> entities,
            string objectName,
            Type baseType,
            NameValueCollection values,
            IFormFileCollection files,
            out Dictionary<string, List<string>> validateErrors)
        {
            var splitNameValueList = values.SplitByKey(objectName + ".Id").ToList();
            List<HttpPostedFileCollection> splitHttpPostFileList = new List<HttpPostedFileCollection>();
            if (files != null)
            {
                splitHttpPostFileList = files.SplitByKey(objectName).ToList();
            }
            validateErrors = new Dictionary<string, List<string>>();
            var max = Math.Max(splitNameValueList.Count, splitHttpPostFileList.Count);
            if (max > 0)
            {
                for (int i = 0; i < splitNameValueList.Count; i++)
                {
                    var vls = splitNameValueList[i];
                    var fls = new HttpPostedFileCollection();
                    Dictionary<string, List<string>> validate;
                    var entity = (IEntityData)Activator.CreateInstance(baseType);
                    if (entity.TryMapping(objectName, vls, fls, out validate))
                    {
                        entities.Add(entity);
                    }
                    else
                    {
                        validateErrors.AddRange(validate);
                    }
                }
                for (int i = 0; i < splitHttpPostFileList.Count; i++)
                {
                    var vls = new NameValueCollection();
                    var fls = splitHttpPostFileList[i];
                    Dictionary<string, List<string>> validate;
                    var entity = (IEntityData)Activator.CreateInstance(baseType);
                    if (entity.TryMapping(objectName, vls, fls, out validate))
                    {
                        entities.Add(entity);
                    }
                    else
                    {
                        validateErrors.AddRange(validate);
                    }
                }
            }

            string base64DataKey = string.Format("{0}.{1}", objectName, "Base64Data");
            string base64FileNameKey = string.Format("{0}.{1}", objectName, "Base64FileName");
            foreach (var item in values.AllKeys)
            {
                if (item.ToLower() == base64DataKey.ToLower())
                {
                    base64DataKey = item;
                }
                if (item.ToLower() == base64FileNameKey.ToLower())
                {
                    base64FileNameKey = item;
                }
            }

            var fileDataList = values.GetValues(base64DataKey);
            var fileNameList = values.GetValues(base64FileNameKey);

            if (fileDataList != null && fileNameList != null)
            {
                for (int i = 0; i < fileDataList.Length; i++)
                {
                    NameValueCollection nv = new NameValueCollection();
                    nv.Add(base64DataKey, fileDataList[i]);
                    if (fileNameList != null && fileNameList.Length > i)
                    {
                        nv.Add(base64FileNameKey, fileNameList[i]);
                    }

                    var fls = new HttpPostedFileCollection();
                    Dictionary<string, List<string>> validate;
                    var entity = (IEntityData)Activator.CreateInstance(baseType);
                    if (entity.TryMapping(objectName, nv, fls, out validate))
                    {
                        entities.Add(entity);
                    }
                    else
                    {
                        validateErrors.AddRange(validate);
                    }
                }
            }

            return !validateErrors.Any();
        }

        public static async Task SaveAsync(this ICollection<IEntityData> entities)
        {
            foreach (var entity in entities)
            {
                await entity.SaveAsync();
            }
        }

        public static string GetValue(this ICollection<IEntityData> entities)
        {
            var valueList = new List<string>();
            foreach (var entity in entities)
            {
                valueList.Add(entity.GetValue());
            }
            return JsonConvert.SerializeObject(valueList);
        }

        public static bool ExecuteExpression(this ICollection<IEntityData> entities, string expression)
        {
            foreach (var entity in entities)
            {
                if (!entity.ExecuteExpression(expression))
                {
                    return false;
                }
            }
            return true;
        }
    }
}