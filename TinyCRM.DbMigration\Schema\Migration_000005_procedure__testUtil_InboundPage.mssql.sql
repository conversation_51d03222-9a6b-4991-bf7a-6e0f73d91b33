
/****** Object:  StoredProcedure [testUtil].[InboundPage]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROC [testUtil].[InboundPage] as
begin

-- 11. Most Complicated cases  
-- Giai thuat sinh 432 cases trong file import excel, xac dinh truong hop thu 19 co Phone1 se trung VL  --> co 1 cap (Disabled VL, Linked core)
-- fixed co sdt bi trung la 0909100019


DECLARE @case11 VARCHAR(max)
SET @case11='0909100019'

DECLARE @markCustomer VARCHAR(max)
SET @markCustomer='For TestInboundPage'
UPDATE dbo.Customer SET Notes=@markCustomer WHERE Phone1='0909100019' OR Phone2='0909100019'

-- Core without ticket, Phone1 = inbound
UPDATE TOP (1) c 
SET			 c.Phone1=@case11
			,c.Notes=@markCustomer
FROM dbo.Customer c
LEFT JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE rt.Id IS NULL AND c.IsBackendCustomer=1 AND c.Notes IS NULL

-- Core without ticket, Phone2 = inbound
UPDATE TOP (1) c 
SET	 		 c.Phone2=@case11
			,c.Notes=@markCustomer
FROM dbo.Customer c
LEFT JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE rt.Id IS NULL AND c.IsBackendCustomer=1 AND c.Notes IS NULL

-- Core without ticket, Phone3 = inbound
UPDATE TOP (1) c SET c.Phone3=@case11
FROM dbo.Customer c
LEFT JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE rt.Id IS NULL AND c.IsBackendCustomer=1 AND c.Notes IS NULL

-- core with 2 rp phieu  --> update rp
UPDATE TOP(1) rt SET rt.RpPhone=@case11,rt.Notes=@markCustomer
FROM dbo.Customer c
JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE c.Notes IS NULL AND c.IsBackendCustomer=1

-- VL with 2 Rp ticket
UPDATE TOP(1) rt SET rt.RpPhone=@case11,rt.Notes=@markCustomer
FROM dbo.Customer c
JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE c.IsDisabled=0 AND c.IsBackendCustomer=0 AND rt.Notes IS NULL




-- CASE 3: phonenumber existed in Disabled Customer only
DECLARE @case3 NVARCHAR(max)

SELECT TOP 1 @case3=d.phone2
FROM dbo.Customer d
JOIN dbo.Customer a ON a.Id=d.LinkedBackendSystemCustomerId
WHERE d.IsDisabled=1 
AND d.Phone1=a.Phone1


DECLARE @case4 NVARCHAR(max)

SELECT TOP 2 @case4=d.phone2
FROM dbo.Customer d
JOIN dbo.Customer a ON a.Id=d.LinkedBackendSystemCustomerId
WHERE d.IsDisabled=1 
AND d.Phone1=a.Phone1

UPDATE TOP (2) rt SET rt.RpPhone=@case4
FROM dbo.RequestTicket rt
JOIN dbo.Customer c ON c.Id=rt.CustomerId
WHERE c.IsBackendCustomer=1

-- case 7: Temp customer with tickets
DECLARE @case7 VARCHAR(max)

SELECT TOP 1  @case7=c.Phone2
FROM dbo.Customer c
JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE rt.RpPhone IS NULL AND c.IsBackendCustomer=0 AND c.IsDisabled=0


-- case 5: Temp Customer, no ticket
DECLARE @case5 VARCHAR(max)

SELECT TOP 1  @case5=c.Phone2
FROM dbo.Customer c
LEFT JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE rt.Id IS NULL AND c.Phone2 IS NOT NULL AND c.IsBackendCustomer=0 AND c.IsDisabled=0

-- case 9: Core, rp ticiet
UPDATE TOP (2) rt SET rt.RpPhone='0909990021'
FROM dbo.Customer c
JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE rt.RpPhone IS NULL AND c.IsBackendCustomer=1

-- case 6: Temp, rp ticket
UPDATE TOP (2) rt SET rt.RpPhone='0909990022'
FROM dbo.Customer c
JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE rt.RpPhone IS NULL AND c.IsBackendCustomer=0 AND c.IsDisabled=0

-- case 6+9: rp ticket on VL, Core
UPDATE TOP (2) rt SET rt.RpPhone='0909990023'
FROM dbo.Customer c
JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE rt.RpPhone IS NULL AND c.IsBackendCustomer=1

UPDATE TOP (2) rt SET rt.RpPhone='0909990023'
FROM dbo.Customer c
JOIN dbo.RequestTicket rt ON rt.CustomerId=c.Id
WHERE rt.RpPhone IS NULL AND c.IsBackendCustomer=0 AND c.IsDisabled=0

-- case 2: 
UPDATE TOP (1) dbo.RequestTicket 
SET RpPhone='0909990020'
WHERE RpPhone IS NULL

-- case 10: core, with tickets
DECLARE @case10 VARCHAR(max)

SELECT TOP (1)  @case10=c.Phone2
FROM dbo.Customer c
JOIN dbo.RequestTicket rt ON rt.CustomerId = c.Id
WHERE rt.RpPhone IS NULL AND c.IsBackendCustomer=1 AND c.Phone2 IS NOT NULL


-- case 8: core, no tickets
DECLARE @case8 VARCHAR(max)

SELECT TOP (1)  @case8=c.Phone2
FROM dbo.Customer c
LEFT JOIN dbo.RequestTicket rt ON rt.CustomerId = c.Id
WHERE rt.id IS NULL AND c.IsBackendCustomer=1


-------------------

	  SELECT '1.	  Not		'	AS Customer	, 'no'		   AS Ticket,'0909990019' AS InboundPhone 
UNION SELECT '3.	  Disable	'				, 'no'					,@case3
UNION SELECT '4.	  Disable	'				, 'rp'					,@case4
UNION SELECT '5.	  Temp		'				, 'no'					,@case5
UNION SELECT '6.	  Temp		'				, 'rp'					,'0909990022' 
UNION SELECT '7.	  Temp		'				, 'WITH '				,@case7
UNION SELECT '8.	  Core		'				, 'no'					,@case8 
UNION SELECT '9.	  Core		'				, 'rp'					,'0909990021' 
UNION SELECT '10.	  Core		'				, 'with'				,@case10 
UNION SELECT '6+9.  Core+Temp	'				, 'rp'					,'0909990023'
UNION SELECT '11. Most complicated  '			, '11. Most complicated',@case11

END
GO