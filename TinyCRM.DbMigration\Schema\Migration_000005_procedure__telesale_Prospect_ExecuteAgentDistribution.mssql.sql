
/****** Object:  StoredProcedure [telesale].[Prospect_ExecuteAgentDistribution]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[Prospect_ExecuteAgentDistribution]

	@CampaignId UNIQUEIDENTIFIER,

	@Status INT,
	@DataSource NVARCHAR(50)=NULL,
	@ProvinceId IdList READONLY,
	@PhoneNumber VARCHAR(100)=NULL,
	@CreatedReason INT,
	@AssignedTeamDate DATETIME,
	@CreatedDate DATETIME,
	@IncomeFrom INT=0,
	@IncomeTo INT=0,
	@MaritalStatus INT=0,
	@PreviousAssignedAgentId UNIQUEIDENTIFIER,
	@PreviousPaCallResultId UNIQUEIDENTIFIER,

	@DistributedDate DATETIME =NULL,
	@SelectedAgentIds ObjectNumberList READONLY,
	@SelectedProspectAssignmentIds IdList READONLY,
	@DataQualityBy NVARCHAR(MAX)

AS
BEGIN
	declare @find_result TABLE (
		ProspectAssignmentId UNIQUEIDENTIFIER
	);

	DECLARE @TeamId UNIQUEIDENTIFIER
	SET @TeamId = (SELECT TOP 1 up.OrganizationId FROM @SelectedAgentIds a JOIN dbo.UserProfiles up ON up.Id=a.Id)

	IF (SELECT COUNT(*) FROM @SelectedProspectAssignmentIds)=0
	BEGIN
		INSERT INTO @find_result 
		EXEC dbo.SearchToDistributeByTeamLead 
				@CampaignId = @CampaignId,
				@TeamId = @TeamId,
				@Status = @Status,
				@DataSource = @DataSource,
				@ProvinceId = @ProvinceId,
				@PhoneNumber = @PhoneNumber,
				@CreatedReason = @CreatedReason,
				@AssignedTeamDate = @AssignedTeamDate,
				@CreatedDate = @CreatedDate,
				@IncomeFrom = @IncomeFrom,
				@IncomeTo = @IncomeTo,
				@MaritalStatus = @MaritalStatus,
				@PreviousAssignedAgentId = @PreviousAssignedAgentId,
				@PreviousPaCallResultId = @PreviousPaCallResultId,
				@StartRow = NULL,
				@EndRow = NULL,
				@TobeDistribute = 1;
	END
	IF @DataQualityBy='Income' OR @DataQualityBy='DataQuality'
	BEGIN
	   DECLARE @Dresult DistributeData;
	   DECLARE @Ddata DistributeData;
	   DECLARE @Dplan DistributePlan;
	   INSERT INTO @Dplan
			( 
				AgentId ,
				ExpectedNumber
			)
	   SELECT Id AgentId, ISNULL(Number, 0) ExpectedNumber FROM @SelectedAgentIds
	   WHERE Number>0;
	   INSERT INTO @Ddata
			( 
				ID ,
				Quality
			)
		SELECT ProspectAssignmentId, IIF(@DataQualityBy='Income', ISNULL(c.Income, 0), ISNULL(c.DataQuality, 0)) Quality FROM @find_result r
		JOIN dbo.ProspectAssignment pa ON r.ProspectAssignmentId=pa.Id
		JOIN dbo.Prospect p ON pa.ProspectId=p.Id
		JOIN dbo.Customer c ON c.Id=p.CustomerId
		UNION ALL
		SELECT pa.Id ProspectAssignmentId, ISNULL(c.Income, 0) Quality  FROM @SelectedProspectAssignmentIds r
		JOIN dbo.ProspectAssignment pa ON r.Id=pa.Id
		JOIN dbo.Prospect p ON pa.ProspectId=p.Id
		JOIN dbo.Customer c ON c.Id=p.CustomerId

		INSERT INTO @Dresult
		EXEC dbo.DistributeDataToAgent @Ddata=@Ddata, @Dplan=@Dplan;
		UPDATE pa SET pa.AssignedAgentId=d.AssignedTo, pa.AssignedAgentDate=@DistributedDate FROM @Dresult d
		JOIN dbo.ProspectAssignment pa ON d.ID=pa.Id
		WHERE d.AssignedTo IS NOT NULL

		UPDATE p SET p.BackToCommonBasketDate=@DistributedDate FROM @Dresult d
		JOIN dbo.ProspectAssignment pa ON d.ID=pa.Id
		JOIN dbo.Prospect p ON pa.ProspectId=p.Id
		WHERE d.AssignedTo IS NOT NULL 
	END
	ELSE
	BEGIN
	   -- Find TeamId by an agent
		SELECT	Id, x.AgentId
		INTO	#temptt
		FROM
				(
					SELECT	Id AgentId, Number ToBeAssigned , SUM(Number) OVER (ORDER BY Id DESC) RowOrderUpper
					FROM	@SelectedAgentIds
		
				) x 
				JOIN 
				(
					--- All prospects which satisfy search criteria
					SELECT	[@find_result].ProspectAssignmentId Id, ROW_NUMBER() OVER (ORDER BY ProspectAssignmentId) RowOrder
					FROM	@find_result
					UNION all
					SELECT	Id, ROW_NUMBER() OVER (ORDER BY Id) RowOrder 
					FROM	@SelectedProspectAssignmentIds
				) pp ON pp.RowOrder>x.RowOrderUpper-x.ToBeAssigned AND pp.RowOrder<=x.RowOrderUpper

		UPDATE	pa 
		SET		AssignedAgentId=yy.AgentId, AssignedAgentDate=@DistributedDate
		FROM	#temptt yy 
				JOIN dbo.ProspectAssignment pa ON pa.Id=yy.Id

		-- Implement Rule: Trong ngày không gọi thì lấy về rổ sup
		UPDATE	p
		SET		BackToCommonBasketDate=@DistributedDate
		FROM	#temptt tt
				JOIN dbo.ProspectAssignment pa ON pa.Id = tt.Id
				JOIN dbo.Prospect p ON p.CurrentAssignmentId = pa.Id
	END
END
GO