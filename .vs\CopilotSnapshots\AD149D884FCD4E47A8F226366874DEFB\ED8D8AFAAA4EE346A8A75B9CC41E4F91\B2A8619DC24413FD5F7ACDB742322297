﻿using System;
using System.Linq;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFormValueByFormIdQuery : QueryBase<DynamicFormValueData>
    {
        public Guid FormId { get; set; }
    }

    internal class GetDynamicFormValueByFormIdQueryHandler : QueryHandlerBase<GetDynamicFormValueByFormIdQuery, DynamicFormValueData>
    {
        public override QueryResult<DynamicFormValueData> Execute(GetDynamicFormValueByFormIdQuery query)
        {
            var entites = EntitySet.Get<DynamicFormValueEntity>().Where(dfv => dfv.DynamicFormId == query.FormId);
            return QueryResult.Create(entites, DynamicFormValueData.FromEntity);
        }
    }
}
