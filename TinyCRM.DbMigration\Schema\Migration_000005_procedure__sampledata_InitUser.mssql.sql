
/****** Object:  StoredProcedure [sampledata].[InitUser]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROC [sampledata].[InitUser]
AS
BEGIN

DECLARE @CountOfOrgs INT
SET @CountOfOrgs = (SELECT COUNT(*) FROM dbo.Organization)

PRINT CONCAT('INIT USERS - START    - creating some users for every existing organizations ..................... ', @CountOfOrgs, ' organizaitons')
-- Validation



/*
Xác định bao nhiêu user cho mỗi org và với roles gì?với subfix tượng trưng là gì?
Vd: trường hợp này có mỗi org có 5 user: 1 trưởng ban, 1 phó trưởng ban, 3 chuyên viên
*/
--========================================================================================
IF OBJECT_ID('sampledata.UserPerOrg') IS NOT NULL DROP TABLE sampledata.UserPerOrg

CREATE TABLE sampledata.UserPerOrg
    (
        roleId         UNIQUEIDENTIFIER,
        subfixUserName [NVARCHAR](255) NULL
    ) ON [PRIMARY]

INSERT sampledata.UserPerOrg VALUES('00c270a5-3762-47df-8ff8-68987ee880eb', 'ptb');
INSERT sampledata.UserPerOrg VALUES('1361B686-D841-4DF6-B906-D242F715C5DB', 'tb');
INSERT sampledata.UserPerOrg VALUES('34C91EA3-07E9-4643-990E-F05CE4C0CBF4', 'cv01');
INSERT sampledata.UserPerOrg VALUES('34C91EA3-07E9-4643-990E-F05CE4C0CBF4', 'cv02');
INSERT sampledata.UserPerOrg VALUES('34C91EA3-07E9-4643-990E-F05CE4C0CBF4', 'cv03');
INSERT sampledata.UserPerOrg VALUES('1D57D017-F087-4213-A63A-910ED80241F1', 'cvdd');


--=================================================--
--		create user per org
--=================================================--



SELECT o.Code+'_'+u.subfixUserName username, (o.Code+'_'+u.subfixUserName+'@test20180208.com') email, o.Id orgID, u.roleId roleID
INTO #newUserTable
FROM Organization o
     CROSS JOIN UserPerOrg u
WHERE (o.OrganizationType='DVQuanLy' OR o.OrganizationType='PhongBan');


INSERT #newUserTable
SELECT o.Code+'_'+u.subfixUserName username, (o.Code+'_'+u.subfixUserName+'@test20180208.com') email, o.Id orgID, u.roleId roleID
FROM Organization o
     CROSS JOIN UserPerOrg u
WHERE (o.OrganizationType!='DVQuanLy' AND o.OrganizationType!='PhongBan' )AND (u.roleId='34C91EA3-07E9-4643-990E-F05CE4C0CBF4' OR u.roleId='1D57D017-F087-4213-A63A-910ED80241F1')
AND LTRIM(RTRIM(o.Code)) IS NOT NULL


--=================================================--
--		Insert user data
--=================================================--
DECLARE @sql NVARCHAR(MAX), @uName NVARCHAR(MAX), @mail NVARCHAR(MAX);
DECLARE @orgId UNIQUEIDENTIFIER, @rId UNIQUEIDENTIFIER;
SET @sql='EXEC sampledata.CreateOneUser @UserName = @u,  @Email = @e,  @OrganizationId = @o,   @roleID = @r';
--SELECT * FROM #newUserTable;
WHILE(SELECT COUNT(*)FROM #newUserTable)>0 BEGIN
    SELECT TOP 1 @uName=username, @mail=email, @orgId=orgID, @rId=roleID
    FROM #newUserTable;
    --PRINT @sql
    EXEC sp_executesql @sql, N'@u NVARCHAR(MAX),  @e NVARCHAR(MAX),  @o UNIQUEIDENTIFIER,  @r UNIQUEIDENTIFIER', @uName, @mail, @orgId, @rId;
    DELETE #newUserTable
    WHERE orgID=@orgId AND username=@uName AND email=@mail;


EXEC sampledata.CreateOneUser @UserName = N'admin-1',        -- nvarchar(256)
                              @Email = N'<EMAIL>',           -- nvarchar(256)
                              @OrganizationId = NULL, -- uniqueidentifier
                                 @roleID = '706127A8-B106-479D-A5B2-5A089F5A906C',         -- uniqueidentifier
							  @fixedUserId='00000000-1111-2222-3333-444444444444'

				
EXEC sampledata.CreateOneUser @UserName = N'admin-2',        -- nvarchar(256)
                              @Email = N'<EMAIL>',           -- nvarchar(256)
                              @OrganizationId = NULL, -- uniqueidentifier
                                 @roleID = '706127A8-B106-479D-A5B2-5A089F5A906C' ,        -- uniqueidentifier
							  @fixedUserId='AAAAAAAA-BBBB-CCCC-DDDD-EEEEEEEEEEEE'

END;

DROP TABLE #newUserTable
DROP TABLE sampledata.UserPerOrg

SET @CountOfOrgs = ( SELECT COUNT(*) FROM dbo.aspnet_Users )

PRINT CONCAT('INIT USERS - FINISH   - Creating users                                       ..................... ',@CountOfOrgs,' users created')

END
GO