
/****** Object:  StoredProcedure [telesale].[SearchDynamicFormValue]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[SearchDynamicFormValue]

	@CampaignId				UNIQUEIDENTIFIER,
	@TeamId					UNIQUEIDENTIFIER,
	@AgentId				UNIQUEIDENTIFIER,
	@Status					INT,
	@PaymentDayLefts		INT,
	@StartRow				INT,
	@EndRow					INT

AS
BEGIN

	DECLARE @TempLockDays INT = 21;

	DECLARE @SelectString NVARCHAR(MAX) = N'
		SELECT	pa.Id ProspectAssignmentId, dfv.Id DynamicFormValueId, c.DataSource, c.FullName CustomerName, pv.ProvinceName, pr.ProductName, dfv.Code, dfv.Status, dfv.CreatedDate, dfv.TempLockDate, dfv.LockDate, up.FullName CreatedUser,
				ROW_NUMBER() OVER (ORDER BY dfv.CreatedDate DESC) RowNumber
	'

	DECLARE @FromString NVARCHAR(MAX) = N'
		FROM	dbo.Prospect p
				JOIN dbo.ProspectAssignment pa ON pa.Id = p.CurrentAssignmentId
				JOIN dbo.DynamicFormValue dfv ON dfv.ProspectAssignmentId = pa.Id
				JOIN dbo.Contact c ON c.Id = p.ContactId
				JOIN dbo.Product pr ON pr.Id = dfv.ProductId
				JOIN dbo.UserProfiles up ON up.Id = dfv.CreatedBy
				LEFT JOIN dbo.Province pv ON pv.Id = c.ProvinceId
	'

	DECLARE @WhereString NVARCHAR(MAX) = N'WHERE dfv.ReferencerObjectType = 1 '

	IF @CampaignId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', ' WHERE ', ' AND ') + N' p.CampaignId = @CampaignId '
	END

	IF @TeamId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', ' WHERE ', ' AND ') + N' up.OrganizationId = @TeamId '
	END

	IF @AgentId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', ' WHERE ', ' AND ') + N' up.Id = @AgentId '
	END

	IF @Status IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', ' WHERE ', ' AND ') + N' dfv.Status = @Status '
	END

	IF @PaymentDayLefts IS NOT NULL
	BEGIN
		IF @PaymentDayLefts < 0
		BEGIN
			SET @WhereString = @WhereString + IIF(@WhereString='', ' WHERE ', ' AND ') + N' @TempLockDays - DATEDIFF(DAY, dfv.TempLockDate, GETDATE()) < 0 '
		END
		ELSE
		BEGIN
			SET @WhereString = @WhereString + IIF(@WhereString='', ' WHERE ', ' AND ') + N' @TempLockDays - DATEDIFF(DAY, dfv.TempLockDate, GETDATE()) = @PaymentDayLefts '
		END
	END

	DECLARE @ExecuteString NVARCHAR(MAX) = N'
	WITH cte AS
	(
		' + @SelectString + N'
		' + @FromString + N'
		' + @WhereString + N'
	)
	SELECT	*,
			(SELECT COUNT(*) FROM cte) TotalCount
	FROM	cte
	WHERE	cte.RowNumber BETWEEN @StartRow AND @EndRow
	'

	DECLARE @ParamDefs NVARCHAR(MAX) = N'
	@CampaignId				UNIQUEIDENTIFIER,
	@TeamId					UNIQUEIDENTIFIER,
	@AgentId				UNIQUEIDENTIFIER,
	@Status					INT,
	@PaymentDayLefts		INT,
	@StartRow				INT,
	@EndRow					INT,

	@TempLockDays			INT
	'

	EXECUTE sp_executesql @ExecuteString, @ParamDefs,
											@CampaignId = @CampaignId,
											@TeamId = @TeamId,
											@AgentId = @AgentId,
											@Status = @Status,
											@PaymentDayLefts = @PaymentDayLefts,
											@StartRow = @StartRow,
											@EndRow = @EndRow,
											@TempLockDays = @TempLockDays

END
GO