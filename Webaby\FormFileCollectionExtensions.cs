﻿using Microsoft.AspNetCore.Http;
using Webaby.Web;

namespace Webaby
{
    public static class FormFileCollectionExtensions
    {
        public static IEnumerable<HttpPostedFileCollection> SplitByKey(this IFormFileCollection files, string startKey)
        {
            var result = new List<HttpPostedFileCollection>();
            var dict = new Dictionary<string, List<IFormFile>>();
            for (int i = 0; i < files.Count; i++)
            {
                string key = files[i].Name;
                if (key.StartsWith(startKey))
                {
                    if (files[i] != null && files[i].Length > 0)
                    {
                        if (dict.ContainsKey(key))
                        {
                            dict[key].Add(files[i]);
                        }
                        else
                        {
                            dict.Add(key, new List<IFormFile>() { files[i] });
                        }
                    }
                }
            }

            if (dict.Count > 0)
            {
                var count = dict.First().Value.Count;
                for (var i = 0; i < count; i++)
                {
                    HttpPostedFileCollection pfc = new HttpPostedFileCollection();
                    foreach (KeyValuePair<string, List<IFormFile>> kv in dict)
                    {
                        if (kv.Value.Count > i)
                        {
                            pfc.Add(kv.Key, kv.Value[i]);
                        }
                    }
                    result.Add(pfc);
                }
            }
            return result;
        }
    }
}
