
/****** Object:  StoredProcedure [telesale].[lead_GetLeadList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



--ALTER PROCEDURE [telesale].[lead_GetLeadList]
CREATE PROCEDURE [telesale].[lead_GetLeadList]
    @AgentId UNIQUEIDENTIFIER ,
    @TeamId UNIQUEIDENTIFIER = NULL , --thay the khi AgentId=NULL
    @LeadStatus INT ,
    @AppStatus INT ,
    @LeadAssignmentStatus INT ,
    @RedirectValue INT = 0 ,
    @AppCreateFrom DATETIME = NULL ,
    @AppCreateTo DATETIME = NULL ,
    @ModifiedDate DATETIME ,
    @ProvinceList IdList READONLY ,
    @LeadCode NVARCHAR(MAX) = NULL ,
    @StartRow INT ,
    @EndRow INT ,
    @CampaignId UNIQUEIDENTIFIER
AS
    BEGIN
        IF @App<PERSON>reateFrom IS NULL
            SET @AppCreateFrom = DATEADD(YEAR, -100, GETDATE())

        IF @AppCreateTo IS NULL
            SET @AppCreateTo = DATEADD(YEAR, 100, GETDATE())

        DECLARE @SearchByProvince BIT
        SET @SearchByProvince = CASE WHEN ( SELECT  COUNT(*)
                                            FROM    @ProvinceList
                                          ) > 0 THEN 1
                                     ELSE 0
                                END;
		IF @LeadStatus IS NULL SET @LeadStatus=0;

		DECLARE @queryString NVARCHAR(max)
		SET @queryString ='
        WITH    cte
        AS 
			( SELECT   * , ROW_NUMBER() OVER ( ORDER BY CreatedDate DESC ) RowNumber
				FROM     
				( SELECT   ap.Id AppointmentId ,
								ap.MeetDate AppointmentTime ,
								ap.UpdatedResultDate UpdatedAppointmentResultDate ,
								ap.Status AppointmentStatus ,
								ap.FeedbackNotes ,
								ap.DistrictId ,
								ap.WardId ,
								ap.ProvinceId ap_ProvinceId ,
								ap.CreatedBy ApCreatedBy ,
								ap.AppointmentResultCodeId ,
								pa.ProspectId ,
								pa.Status ProspectAssignmenStatus ,
								pa.Id ProspectAssignmentId ,
								la.SuggestedFieldSaleReason SuggestedFieldSaleReason ,
								la.Id LeadAssignmentId ,
								la.AssignedFieldSaleId ,
								la.SuggestedFieldSaleId la_SuggestedFieldSaleId ,
								la.AssignedFieldSaleTeamId la_AssignedFieldSaleTeamId ,
								l.Id ,
								l.LeadCode ,
								l.CreatedDate CreatedDate ,
								l.ModifiedDate ,
								l.Status
						FROM    dbo.Lead l
								JOIN dbo.ProspectAssignment pa ON pa.Id = l.CreatedByProspectAssignmentId
								JOIN dbo.LeadAssignment la ON la.Id = l.CurrentLeadAssignmentId
								JOIN dbo.Appointment ap ON ap.Id = la.WaitingAppointmentId
								'
						+ IIF(@SearchByProvince>0,'JOIN @ProvinceList spv ON ap.ProvinceId = spv.Id' + CHAR(13),'')
						+'WHERE   ( '+ IIF(@AgentId IS NOT NULL,'pa.AssignedAgentId = @AgentId','pa.AssignedTeamId = @TeamId')  + 
								')
								 '
									+ CASE @LeadStatus 
										WHEN 6 THEN 'AND ( l.Status <> 4 AND l.Status <> 5)' 
										WHEN 7 THEN 'AND ( l.Status = 5 AND DATEDIFF(DAY,ap.UpdatedResultDate,GETDATE()) <= 7)'
                                        WHEN 8 THEN 'AND ( l.Status = 4 AND DATEDIFF(DAY,ap.UpdatedResultDate,GETDATE()) <= 7)'
                                        WHEN 0 THEN ''
                                        ELSE 'AND ( @LeadStatus = l.Status)'
									end
                                + IIF(@ModifiedDate IS NOT NULL,' and DATEDIFF(DAY,l.ModifiedDate ,@ModifiedDate)=0 ','')
					 + CHAR(13) + IIF(@AppStatus=6,' AND (ap.Status=2 OR ap.Status=5)',
										IIF(@AppStatus != 0 AND @AppStatus IS NOT NULL,'AND ap.Status=@AppStatus ',''))
								+ iif (@LeadAssignmentStatus != 0 and @LeadAssignmentStatus IS NOT NULL, CHAR(13)+' and @LeadAssignmentStatus=la.Status ','')
								+ IIF(@AppCreateFrom IS NOT NULL,CHAR(13) +' AND ap.CreatedDate >= @AppCreateFrom ','')
								+ IIF(@AppCreateTo IS NOT NULL,CHAR(13) +' AND ap.CreatedDate <= @AppCreateTo ','')
								+ IIF(@LeadCode IS NOT NULL,CHAR(13) +' and @LeadCode=l.LeadCode ','')
								+ IIF(@LeadStatus!=7 OR @LeadStatus IS NULL,CHAR(13) +' and pa.Status<3','') -- vẫn cho hiển thị những lead mà pa đã đóng (để nhìn thấy những lead bị lost)
					 + CHAR(13) + CASE @RedirectValue
									WHEN 5 THEN  ' AND (la.Status=1 AND ap.Status=1)'
									WHEN 6 THEN  ' AND (la.AssignedFieldSaleTeamId IS NOT NULL AND ap.Status=1) '
									WHEN 7 THEN  ' AND (l.Status <> 4 AND l.Status <> 5 AND ap.Status=3) '
									WHEN 8 THEN  ' AND l.Status <> 4 AND l.Status <> 5 AND (ap.Status=2 OR ap.Status=5) '
									WHEN 9 THEN  ' AND (l.Status=5 AND DATEDIFF(DAY, ap.UpdatedResultDate, GETDATE()) <=7) '
									WHEN 10 THEN ' AND ((l.Status=4) AND DATEDIFF(DAY, ap.UpdatedResultDate, GETDATE()) <=7))'
									ELSE ''
									END                                    
								+'
					) filteredLead
				)
            SELECT  cte.* ,
                    p.HotListGroupId ,
                    crby.FullName AppointmentCreateBy ,
                    fs.FullName FieldSaleName ,
                    suggest_fs.FullName SuggestedFieldSaleName ,
                    ( IIF(ward.WardName IS NOT NULL
                      AND district.DistrictName IS NOT NULL, concat(ward.WardName,'' - ''), '''')
                      + IIF(district.DistrictName IS NOT NULL, concat(district.DistrictName,'' - ''), '''') + pv.ProvinceName ) AppointmentAddress ,
                    c.FullName ,
                    c.DataSource ,
                    c.Address ,
                    pv.ProvinceName ,
                    c.Phone ,
                    apr.ResultCode AppointmentResultCode ,
                    apr.DescriptionVn AppointmentResultDescription ,
                    temp.FieldSaleLeaderName ,
                    temp.FieldSaleLeaderPhone ,
                    ( SELECT    COUNT(*)
                      FROM      cte
                    ) TotalCount
            FROM    cte
                    JOIN dbo.Prospect p ON p.Id = cte.ProspectId
                    JOIN dbo.Contact c ON c.Id = p.ContactId
                    JOIN dbo.UserProfiles crby ON crby.Id = ApCreatedBy
                    LEFT JOIN dbo.Province pv ON pv.Id = cte.ap_ProvinceId
                    LEFT JOIN dbo.AppointmentResultCode apr ON apr.Id = cte.AppointmentResultCodeId
                    LEFT JOIN dbo.UserProfiles fs ON fs.Id = cte.AssignedFieldSaleId
                    LEFT JOIN dbo.District district ON district.Id = cte.DistrictId
                    LEFT JOIN dbo.Ward ward ON ward.Id = cte.WardId
                    LEFT JOIN dbo.UserProfiles suggest_fs ON suggest_fs.Id = la_SuggestedFieldSaleId
                    LEFT JOIN ( SELECT  org.Id OrganizationId ,
                                        ISNULL(up.FullName, u.UserName) FieldSaleLeaderName ,
                                        up.Phone FieldSaleLeaderPhone
                                FROM    dbo.Organization org
                                        JOIN dbo.UserProfiles up ON up.OrganizationId = org.Id
                                        JOIN dbo.aspnet_Users u ON u.UserId = up.Id
                                        JOIN dbo.UserInRoles uir ON uir.UserId = u.UserId
                                WHERE   org.OrganizationType = ''DMO Team''
                                        AND uir.RoleId = ''94E5A159-A404-4D5C-9D6D-BE0C49ADC34C''
                              ) temp ON temp.OrganizationId = la_AssignedFieldSaleTeamId
            WHERE   cte.RowNumber BETWEEN @StartRow AND @EndRow
		'

	DECLARE @paramsQueryPart NVARCHAR(max) = ' @AgentId UNIQUEIDENTIFIER ,
											@TeamId UNIQUEIDENTIFIER = NULL , 
											@LeadStatus INT ,
											@AppStatus INT ,
											@LeadAssignmentStatus INT ,
											@RedirectValue INT = 0 ,
											@ProvinceList IdList READONLY ,
											@AppCreateFrom DATETIME = NULL ,
											@AppCreateTo DATETIME = NULL ,
											@ModifiedDate DATETIME ,
											@LeadCode NVARCHAR(MAX) = NULL ,
											@StartRow INT ,
											@EndRow INT '
	--PRINT CAST(@queryString AS NTEXT)

	EXEC  sp_executesql @queryString, 
						@paramsQueryPart,
						@AgentId,
						@TeamId , 
						@LeadStatus ,
						@AppStatus	,
						@LeadAssignmentStatus ,
						@RedirectValue ,
						@ProvinceList,
						@AppCreateFrom,
						@AppCreateTo	,
						@ModifiedDate ,
						@LeadCode,
						@StartRow ,
						@EndRow ;
    END
GO