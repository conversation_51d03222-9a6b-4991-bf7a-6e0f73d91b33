﻿using Microsoft.EntityFrameworkCore.Migrations.Operations.Builders;
using Microsoft.EntityFrameworkCore.Migrations.Operations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore;

namespace Webaby.Data
{
    public static class EFCoreExternsion
    {
            public static DatabaseType GetDatabaseType(this MigrationBuilder migrationBuilder)
            {
                if (migrationBuilder.IsSqlServer())
                {
                    return DatabaseType.SqlServer;
                }
                if (migrationBuilder.IsNpgsql())
                {
                    return DatabaseType.PostgreSql;
                }
                throw new NotImplementedException($"Database type is not supported.");
            }

        public static string SafeName(this string str, DatabaseType databaseType)
        {
            if (databaseType == DatabaseType.SqlServer)
            {
                return str.StartsWith('[') && str.EndsWith(']') ? str : $"[{str}]";
            }
            if (databaseType == DatabaseType.PostgreSql)
            {
                return str.StartsWith('\"') && str.EndsWith('\"') ? str : $"\"{str}\"";
            }
            if (databaseType == DatabaseType.SQLite)
            {
                return str.StartsWith('[') && str.EndsWith(']') ? str : $"[{str}]";
            }
            throw new NotImplementedException($"Database type {databaseType} is not supported.");
        }

        public static string SafeBolean(this bool val, DatabaseType databaseType)
        {
            if (databaseType == DatabaseType.SqlServer)
            {
                return val ? "1" : "0";
            }
            if (databaseType == DatabaseType.PostgreSql)
            {
                return val ? "True" : "False";
            }
            if (databaseType == DatabaseType.SQLite)
            {
                return val ? "1" : "0";
            }
            throw new NotImplementedException($"Database type {databaseType} is not supported.");
        }

        public static string SafeUUID(DatabaseType databaseType)
        {
            if (databaseType == DatabaseType.SqlServer)
            {
                return "newid()";
            }
            if (databaseType == DatabaseType.PostgreSql)
            {
                return "md5(random()::text || clock_timestamp()::text)::uuid"; 
            }
            if (databaseType == DatabaseType.SQLite)
            {
                return "lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))";
            }
            throw new NotImplementedException($"Database type {databaseType} is not supported.");
        }

        public static string SafeUUIDSequential(DatabaseType databaseType)
        {
            if (databaseType == DatabaseType.SqlServer)
            {
                return "newsequentialid()";
            }
            if (databaseType == DatabaseType.PostgreSql)
            {
                return "md5(clock_timestamp()::text || random()::text)::uuid";
            }
            if (databaseType == DatabaseType.SQLite)
            {
                return "lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || substr(lower(hex(cast(strftime('%s','now') as blob))),1,8) || substr(lower(hex(randomblob(2))),1,4)";
            }
            throw new NotImplementedException($"Database type {databaseType} is not supported.");
        }

        public static string SafeCurrentDate(DatabaseType databaseType)
        {
            if (databaseType == DatabaseType.SqlServer)
            {
                return "getdate()";
            }
            if (databaseType == DatabaseType.PostgreSql)
            {
                return "CURRENT_TIMESTAMP";
            }
            if (databaseType == DatabaseType.SQLite)
            {
                return "datetime('now')";
            }
            throw new NotImplementedException($"Database type {databaseType} is not supported.");
        }

        public static OperationBuilder<AddColumnOperation> AnnotationBasicIdentity(this OperationBuilder<AddColumnOperation> b, DatabaseType databaseType)
        {
            if (databaseType == DatabaseType.SqlServer)
            {
                return b.Annotation("SqlServer:Identity", "1, 1");
            }
            if (databaseType == DatabaseType.PostgreSql)
            {
                return b.Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);
            }
            if (databaseType == DatabaseType.SQLite)
            {
                return b.Annotation("Sqlite:Autoincrement", true);
            }
            throw new NotImplementedException($"Database type {databaseType} is not supported.");
        }
    }
}
