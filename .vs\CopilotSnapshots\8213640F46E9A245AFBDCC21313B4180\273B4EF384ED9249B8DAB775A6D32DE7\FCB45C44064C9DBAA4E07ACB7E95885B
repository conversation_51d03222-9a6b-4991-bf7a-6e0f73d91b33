﻿using System;
using System.Linq;
using Webaby;

namespace Webaby.Core.Role.Queries
{
    public class GetRoleByNameQuery : QueryBase<RoleData>
    {
        public string RoleName { get; set; }
    }

    internal class GetRoleByNameQueryHandler : QueryHandlerBase<GetRoleByNameQuery, RoleData>
    {
        public override QueryResult<RoleData> Execute(GetRoleByNameQuery query)
        {
            return QueryResult.Create(EntitySet.Get<RoleEntity>().Where(x => x.Name == query.RoleName), query.Pagination, RoleData.FromEntity);
        }
    }
}