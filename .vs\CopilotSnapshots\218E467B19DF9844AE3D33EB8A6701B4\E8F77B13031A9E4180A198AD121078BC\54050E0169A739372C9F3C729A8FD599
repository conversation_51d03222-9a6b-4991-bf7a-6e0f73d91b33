﻿using AutoMapper;
using Webaby.Data;
using Webaby.Localization;

namespace Webaby.Core.Access.Queries
{
    public class GetApiAccessQuery : QueryBase<AccessData>
    {
        public string UrlPart { get; set; }
        public HttpMethods Method { get; set; }
    }

    internal sealed class GetApiAccessQueryHandler : QueryHandlerBase<GetApiAccessQuery, AccessData>
    {
        public GetApiAccessQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<AccessData>> ExecuteAsync(GetApiAccessQuery query)
        {
            // L<PERSON>y danh sách các AccessEntity phù hợp với UrlPart và Method
            var accessEntities = EntitySet.Get<AccessEntity>()
                .Where(access => access.ActionName == query.UrlPart && access.Method == query.Method);

            // Map sang AccessData
            Func<AccessEntity, AccessData> selector = x => Mapper.Map<AccessData>(x);

            // Tạo QueryResult
            var result = QueryResult.Create(accessEntities, selector);

            return result;
        }
    }
}

