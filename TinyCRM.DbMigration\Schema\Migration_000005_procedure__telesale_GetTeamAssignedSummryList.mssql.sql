
/****** Object:  StoredProcedure [telesale].[GetTeamAssignedSummryList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[GetTeamAssignedSummryList]

	@ProvinceId		UNIQUEIDENTIFIER,
	@MeetDate		DATETIME

AS
BEGIN
	SELECT	x.*, y.DmoCount 
	FROM	(
				SELECT  org.Id, org.Name OrganizationName,
						COUNT(la.Id) AssignedCount,
						SUM(IIF(DATEDIFF(MINUTE, la.AssignedFieldSaleTeamDate, GETDATE())<=120, 1, 0)) RecentAssignCount
				FROM	dbo.Organization org
						LEFT JOIN dbo.LeadAssignment la ON la.AssignedFieldSaleTeamId = org.Id 
						LEFT JOIN dbo.Appointment a ON a.Id=la.WaitingAppointmentId AND DATEDIFF(DAY, a.MeetDate,@MeetDate)=0 AND a.Status <> 4 AND a.Status <> 5
				--WHERE	org.OrganizationType = 'DMO Team'
				GROUP BY org.Id, org.Name
			) x
			JOIN
			(
				SELECT	up.OrganizationId, COUNT(*) DmoCount
				FROM	dbo.UserProfiles up
						JOIN dbo.aspnet_Membership m ON m.UserID=up.Id
						JOIN dbo.UserInRoles ur ON ur.UserId=up.Id
						JOIN dbo.aspnet_Users u ON u.UserId=up.Id
						JOIN dbo.Roles r ON r.Id=ur.RoleId
				WHERE	r.Name = 'Field Sale'
						AND m.IsApproved=1
				GROUP BY up.OrganizationId
			) y ON y.OrganizationId=x.Id
	ORDER BY x.OrganizationName
END
GO