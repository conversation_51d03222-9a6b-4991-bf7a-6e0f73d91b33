﻿
/****** Object:  StoredProcedure [import].[ImportContactAfterInsert]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[ImportContactAfterInsert]
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SET NOCOUNT ON;

	--validate user_name
	UPDATE ic SET ic.UserIdValid=au.UserId 
	FROM import.TMRContact ic
	LEFT JOIN dbo.aspnet_Users au ON LOWER(RTRIM(ic.UserName))=LOWER(RTRIM(au.UserName))

	
	-- UNION with existing Contact in system before dedup
	INSERT INTO import.TMRContact
            ( Id ,
              UserName ,
              PhoneNumberValid,
			  UserIdValid,
			  IsInDbContact
            )
	SELECT NEWID(), u.UserName,c.Phone,u.UserId ,1
	FROM dbo.ProspectAssignment pa
	JOIN dbo.aspnet_Users u ON u.UserId= pa.AssignedAgentId
	JOIN dbo.Prospect p ON p.CurrentAssignmentId=pa.Id
	JOIN dbo.Contact c ON c.Id=p.ContactId

	
	-- Check internal USER DUP
	UPDATE c 
	SET Dup_InternalUserOrder =xx.dup,
		Dup_InternalUserCount = xx.groupCount,
		Dup_SelfInDb=xx.countInDb
	FROM import.TMRContact c
	join (
			SELECT ROW_NUMBER() OVER (PARTITION BY c.UserName, c.PhoneNumberValid ORDER BY c.IsInDbContact desc) dup,
				   COUNT(*) OVER (PARTITION BY c.UserName, c.PhoneNumberValid) groupCount,
				   sum(iif(c.IsInDbContact=1,1,0)) OVER (PARTITION BY c.UserName, c.PhoneNumberValid) countInDb,
			 c.* 
			FROM import.TMRContact c
			WHERE c.PhoneNumberValid IS NOT NULL
	)xx ON xx.id=c.id
	

	
	-- Check CROSS USER DUP

	UPDATE c SET Dup_CrossUserGroupCount=yy.countUsers
	FROM (
		SELECT Id, PhoneNumberValid, ContactName, UserIdValid,IsInDbContact , COUNT(UserName) OVER (PARTITION BY PhoneNumberValid) countUsers
		FROM import.TMRContact
		WHERE PhoneNumberValid IS NOT NULL 
		AND Dup_InternalUserOrder=1
		) yy 
	JOIN import.TMRContact c ON c.Id=yy.Id
	WHERE yy.countUsers>1
        
	
	DELETE import.TMRContact_Valid
	-- Update contact ID
	INSERT INTO import.TMRContact_Valid
	        ( Id ,
	          DataSource ,
	          PhoneNumber ,
	          ContactName ,
	          Address ,
	          ProvinceId ,
	          DOB ,
	          Gender ,
	          Career ,
	          Marital ,
	          Income ,
	          Note ,
	          ContactId ,
			  IsNewContact,
	          ProspectId ,
			  IsNewProspect,
	          ProspectAssignmentId,
			  AssignedUserId
	        )
	SELECT tc.Id, tc.DataSource,tc.PhoneNumberValid,tc.ContactName,tc.Address,pv.Id,tc.DOBValid,0,tc.Career,0,tc.Income,tc.Note,ISNULL(c.Id,NEWID()),IIF(c.Id IS NULL,1,0),ISNULL(p.Id, NEWID()),
			IIF(p.Id IS NULL,1,0),NEWID()
	,tc.UserIdValid
	FROM import.TMRContact tc
	LEFT JOIN dbo.Contact c ON c.Phone=tc.PhoneNumberValid
	LEFT JOIN dbo.Prospect p ON p.ContactId=c.Id
	LEFT JOIN dbo.Province pv ON pv.ImportCodeName=tc.ProvinceName
	WHERE Dup_CrossUserGroupCount IS NULL 
		AND IsInDbContact=0 
		AND Dup_InternalUserOrder=1 AND tc.Dup_InternalUserOrder IS NOT NULL
******/
END
GO