﻿
/****** Object:  StoredProcedure [telesale].[CreateSurveyFeedback]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[CreateSurveyFeedback]
	@ProspectAssignmentId UNIQUEIDENTIFIER,
	@UserId UNIQUEIDENTIFIER
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
    SET NOCOUNT ON;
	IF EXISTS (SELECT * FROM dbo.ProspectAssignment WHERE ReferenceResultId IS NOT NULL AND Id=@ProspectAssignmentId)
	BEGIN
	    SELECT sf.* FROM dbo.SurveyFeedback sf
		JOIN dbo.ProspectAssignment pa ON sf.Id = pa.ReferenceResultId
		WHERE pa.Id=@ProspectAssignmentId
	END
	ELSE
	BEGIN
	   ---- Generate SurveyFeedbackId & Code
		DECLARE	@SurveyFeedbackId UNIQUEIDENTIFIER = NEWID()
		DECLARE	@SurveyFeedbackCodeBinary VARBINARY(MAX) = CAST(@SurveyFeedbackId AS VARBINARY(MAX))
		DECLARE @SurveyFeedbackCode VARCHAR(50) = (SELECT @SurveyFeedbackCodeBinary FOR XML PATH(''), BINARY BASE64)
		SET @SurveyFeedbackCode = REPLACE(REPLACE(@SurveyFeedbackCode, '=', ''), '+', '')

		DECLARE @CampaignId UNIQUEIDENTIFIER;
		DECLARE @ContactId UNIQUEIDENTIFIER;
		DECLARE @SurveyId UNIQUEIDENTIFIER;
		DECLARE @OrgId UNIQUEIDENTIFIER;
		SELECT @OrgId=OrganizationId FROM dbo.UserProfiles WHERE id=@UserId;
		SELECT @ContactId=ContactId, @CampaignId=CampaignId FROM dbo.ProspectAssignment WHERE id=@ProspectAssignmentId
		SELECT	@SurveyId = sc.SurveyId 
		FROM dbo.Campaign c
		JOIN dbo.SurveyCampaign sc ON sc.CampaignId = c.Id
		WHERE	c.Id = @CampaignId

		BEGIN TRANSACTION
		BEGIN TRY
			---- Create SurveyFeedback
			INSERT INTO dbo.SurveyFeedback (Id, SurveyId, RelatedObjectId, SurveyeeId, SurveyeeOrganizationId, CreatedDate, Code)
			SELECT	@SurveyFeedbackId, @SurveyId, @ContactId, NULL, NULL, GETDATE(), @SurveyFeedbackCode

			---- Distribute here
			UPDATE  pa
			SET     pa.AssignedTeamId = @OrgId,
					pa.AssignedTeamDate = GETDATE(),
					pa.AssignedAgentId = @UserId,
					pa.AssignedAgentDate = GETDATE(),
					pa.Status = 1,
					pa.CreatedReason = 1,
					pa.Ignore15DaysRule = 0 ,

					pa.OwnerId = pa.AssignedAgentId,
					pa.OwnerType = 1, -- Contact

					pa.ReferenceResultId = @SurveyFeedbackId,
					pa.ReferenceResultType = 'SurveyFeedback'

			FROM    dbo.ProspectAssignment pa WHERE pa.Id=@ProspectAssignmentId
		
			UPDATE  p
			SET     p.Status = 2,
					p.BackToCommonBasketDate = NULL,
					p.ReprospectDate = NULL,

					p.ReferenceObjectId = @ContactId,
					p.ReferenceObjectType = 'Contact',

					p.ReferenceResultId = @SurveyFeedbackId,
					p.ReferenceResultType = 'SurveyFeedback'

			FROM    dbo.ProspectAssignment pa
					JOIN dbo.Prospect p ON p.Id = pa.ProspectId
					WHERE pa.Id=@ProspectAssignmentId

			COMMIT TRANSACTION

		END TRY
		BEGIN CATCH
			ROLLBACK TRANSACTION
			THROW;
		END CATCH

		SELECT * FROM dbo.SurveyFeedback WHERE id=@SurveyFeedbackId 
	END
******/
END
GO