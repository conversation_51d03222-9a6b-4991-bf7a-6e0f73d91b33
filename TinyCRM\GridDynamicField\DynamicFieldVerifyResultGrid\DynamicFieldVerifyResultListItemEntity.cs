﻿using System;
using Webaby.Data;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TinyCRM.GridDynamicField.DynamicFieldVerifyResultGrid
{
    [Table("DynamicFieldVerifyResultListItem", Schema = "dbo")]
    public class DynamicFieldVerifyResultListItemEntity : IEntity, ICreatedDateEnabledEntity, ICreatedByEnabledEntity
    {
        [Key]
        public Guid Id { get; set; }

        [Column]
        public string Title { get; set; }

        [Column]
        public Guid? KnowledgeItemId { get; set; }

        [Column]
        public bool Result { get; set; }

        [Column]
        public Guid? ValueGroupId { get; set; }

        [Column]
        public DateTime CreatedDate { get; set; }

        [Column]
        public Guid CreatedBy { get; set; }
    }
}
