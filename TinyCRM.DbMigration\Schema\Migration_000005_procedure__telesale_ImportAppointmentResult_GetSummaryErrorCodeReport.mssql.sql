
/****** Object:  StoredProcedure [telesale].[ImportAppointmentResult_GetSummaryErrorCodeReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE procedure [telesale].[ImportAppointmentResult_GetSummaryErrorCodeReport]
as
begin
	set nocount on;
	select count(*) Total,
		   sum(iif(ErrorCode & 1 <> 0, 1, 0)) LeadCodeNull,
		   sum(iif(ErrorCode & 2 <> 0, 1, 0)) LeadCodeDuplicate,
		   sum(iif(ErrorCode & 4 <> 0, 1, 0)) DmoCodeNotMatch,
		   sum(iif(ErrorCode & 8 <> 0, 1, 0)) ResultCodeNotMatch,
		   sum(iif(ErrorCode & 16 <> 0, 1, 0)) AppointmentNotMatch,
		   sum(iif(ErrorCode & 32 <> 0 AND ErrorCode & 256 = 0, 1, 0)) SuccessUpdateDMO,
		   sum(iif(ErrorCode & 64 <> 0 AND ErrorCode & 256 = 0, 1, 0)) SuccessUpdateResultCode,
		   sum(iif(ErrorCode & 128 <> 0 AND ErrorCode & 256 = 0, 1, 0)) SuccessUpdateFeedbackNotes,
		   sum(iif(ErrorCode & 256 <> 0, 1, 0)) NotAllowedToUpdateResults
	from dbo.ImportAppointmentFB
end
GO