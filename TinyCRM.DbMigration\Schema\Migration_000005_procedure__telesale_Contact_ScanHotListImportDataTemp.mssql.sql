
/****** Object:  StoredProcedure [telesale].[Contact_ScanHotListImportDataTemp]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- ALTER PROCEDURE [telesale].[Contact_ScanHotListImportDataTemp] 'df67d415-c2a0-41bf-b9fc-c75a2ebc3b23'
CREATE PROCEDURE [telesale].[Contact_ScanHotListImportDataTemp]

	@ImportSessionId	UNIQUEIDENTIFIER

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	DELETE dbo.StagingContact WHERE ImportSessionId = @ImportSessionId AND ISNULL(FullName,'')='' AND ISNULL(Phone,'')=''

	UPDATE	sc
	SET		Phone = '0' + sc.Phone
	FROM	dbo.StagingContact sc
	WHERE	sc.ImportSessionId = @ImportSessionId
			AND ISNULL(sc.Phone,'') <> ''
			AND LEFT(sc.Phone,1) <> '0'

	-- Internal duplicated
	UPDATE	sc
    SET		DuplicatedCase = 1
    FROM	dbo.StagingContact sc
		    JOIN
		    (
			    SELECT	Id,
					    Phone,
					    ROW_NUMBER() OVER (PARTITION BY Phone ORDER BY FullName) RowNumber
			    FROM	dbo.StagingContact
			    WHERE	ImportSessionId = @ImportSessionId
		    ) temp ON temp.Id = sc.Id
    WHERE	sc.ImportSessionId = @ImportSessionId
			AND temp.RowNumber >= 2

	-- Scan imported data
	UPDATE sc 
	SET	DuplicatedCase = CASE
							WHEN (c.Status = 2 OR c.Status = 3)						THEN 3 -- Không tiềm năng
							WHEN (p.ReprospectDate IS NOT NULL)						THEN 2 -- Chờ 90 ngày
							WHEN pa.AssignedTeamId IS NOT NULL 
								 AND pa.AssignedAgentId IS NULL						THEN 4 -- Trong rổ sup
							WHEN ISNULL(pa.Ignore15DaysRule,0)=1
								 AND pa.AssignedTeamId IS NOT NULL
								 AND pa.AssignedAgentId IS NOT NULL					THEN 5 -- Trong rổ TMR, Ưu tiên
							WHEN ISNULL(pa.Ignore15DaysRule,0)=0
								 AND pa.AssignedTeamId IS NOT NULL
								 AND pa.AssignedAgentId IS NOT NULL
								 AND cc.Id IS NULL									THEN 6 -- Trong rổ TMR, Thường - chưa gọi
							WHEN ISNULL(pa.Ignore15DaysRule,0)=0
								 AND pa.AssignedTeamId IS NOT NULL
								 AND pa.AssignedAgentId IS NOT NULL
								 AND cc.Id IS NOT NULL								THEN 7 -- Trong rổ TMR, Thường - đang theo
						END,
			CurrentContactId = c.Id,
			CurrentProspectId = p.Id,
			CurrentProspectAssignmentId = pa.Id,
			NeedClose = IIf(pa.Status=3 OR p.ReprospectDate IS NOT NULL OR (pa.AssignedTeamId IS NOT NULL AND pa.AssignedAgentId IS NULL) OR (p.IsHot=0 AND cc.Id IS NULL), 1, 0),
			NewContactId = IIF(c.Id IS NULL, NEWID(), NULL),
			NewProspectId = IIF(p.Id IS NULL, NEWID(), NULL)
	FROM	dbo.StagingContact sc
			LEFT JOIN dbo.Contact c ON c.Phone = sc.Phone
			LEFT JOIN dbo.Prospect p ON p.ContactId = c.Id
			LEFT JOIN dbo.ProspectAssignment pa ON pa.Id = p.CurrentAssignmentId
			LEFT JOIN dbo.ContactCall cc ON cc.ProspectAssignmentId = pa.Id
	WHERE	sc.ImportSessionId = @ImportSessionId
			AND ISNULL(sc.DuplicatedCase,0) = 0
			AND ISNULL(sc.DataErrorMessage,'')=''

	-- Các ProspectAssignment cần close, hoặc Prospect chưa có Current
	UPDATE	dbo.StagingContact
	SET		NewProspectAssignmentId = NEWID()
	WHERE	ImportSessionId = @ImportSessionId 
			AND ISNULL(DataErrorMessage,'')=''
			AND ISNULL(DuplicatedCase,0) NOT IN (1, 5, 7)
			AND (NeedClose=1 OR CurrentProspectAssignmentId IS NULL)

	UPDATE	dbo.StagingContact 
	SET		ToBeHotDistributePaId = NewProspectAssignmentId 
	WHERE	ImportSessionId = @ImportSessionId
			AND ISNULL(DataErrorMessage,'')=''
			AND ISNULL(DuplicatedCase,0) NOT IN (1, 5, 7)
			AND NewProspectAssignmentId IS NOT NULL
******/
END
GO