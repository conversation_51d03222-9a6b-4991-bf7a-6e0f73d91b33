
/****** Object:  StoredProcedure [telesale].[Contact_GetContactNotesHistories]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[Contact_GetContactNotesHistories]

	@ContactId		UNIQUEIDENTIFIER

AS
BEGIN

	SELECT	u.UserId, ISNULL(up.FullName,u.UserName) UserName, en.ModifiedDate, fi.NewValue Notes
	FROM	dbo.AuditEntityChange en
			JOIN dbo.AuditFieldChange fi ON fi.AuditId = en.Id
			JOIN dbo.UserProfiles up ON up.Id = en.ModifiedBy
			JOIN dbo.aspnet_Users u ON u.UserId = up.Id
	WHERE	en.KeyValue = @ContactId
			AND fi.MemberName = 'Notes'
	ORDER BY en.ModifiedDate DESC

END
GO