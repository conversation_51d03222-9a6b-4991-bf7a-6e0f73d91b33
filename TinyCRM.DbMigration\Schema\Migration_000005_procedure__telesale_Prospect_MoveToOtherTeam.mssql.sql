
/****** Object:  StoredProcedure [telesale].[Prospect_MoveToOtherTeam]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[Prospect_MoveToOtherTeam]

	@User UNIQUEIDENTIFIER,
	@ProspectAssignmentId UNIQUEIDENTIFIER,
	@NewTeamId UNIQUEIDENTIFIER,
	@NewTMRId UNIQUEIDENTIFIER = NULL

AS
BEGIN

	DECLARE @TempList dbo.IdList
	DECLARE @TempIntList dbo.IntList

	DECLARE @ProspectAssignmentList dbo.IdList
	INSERT INTO @ProspectAssignmentList (Id) VALUES (@ProspectAssignmentId)

	DECLARE @AssignedTeamId UNIQUEIDENTIFIER = (SELECT AssignedTeamId FROM dbo.ProspectAssignment WHERE Id = @ProspectAssignmentId)

	BEGIN TRY
		
		BEGIN TRANSACTION

		-- Tr<PERSON>ớ<PERSON> tiên, dùng chức năng rút về
		EXEC dbo.SearchCustomerAndRegainBySaleSupport
			@ImportSessionId = NULL,                      -- uniqueidentifier
		    @CampaignId = NULL,                           -- uniqueidentifier
		    @PageIndex = NULL,                               -- int
		    @PageSize = NULL,                                -- int
		    @DataSource = NULL,                            -- nvarchar(max)
		    @FollowStatuses = @TempIntList,                       -- IntList
		    @PhoneNumber = NULL,                            -- varchar(max)
		    @FullName = NULL,                              -- nvarchar(max)
		    @TMR = NULL,                                  -- uniqueidentifier
		    @TeamTMR = @AssignedTeamId,                              -- uniqueidentifier
		    @AssignedTeamDate = NULL,    -- datetime
		    @AssignedTeamDateTo = NULL,  -- datetime
		    @AssignedAgentDate = NULL,   -- datetime
		    @AssignedAgentDateTo = NULL, -- datetime
		    @NotCallFromDate = NULL,     -- datetime
		    @ContactSelectedList = @ProspectAssignmentList,                  -- IdList
		    @Manager = @User,                              -- uniqueidentifier
		    @Export = NULL,                               -- bit
		    @ProvinceList = @TempList,                         -- IdList
		    @RegainCount = 1,                             -- int
		    @CallResultIds = @TempList,                        -- IdList
		    @SurveyQuestionId = NULL,                     -- uniqueidentifier
		    @SurveyAnswerId = NULL,                       -- uniqueidentifier
		    @RetrieveToOrgId = NULL                       -- uniqueidentifier

		-- Sau đó, phân bổ xuống Team, Agent mới
		UPDATE	pa
		SET		AssignedTeamId = @NewTeamId, AssignedTeamDate = GETDATE(),
				AssignedAgentId = @NewTMRId, AssignedAgentDate = CASE WHEN @NewTMRId IS NULL THEN NULL ELSE GETDATE() END
		FROM	dbo.ProspectAssignment pa
		WHERE	pa.PreviousProspectAssingmentId = @ProspectAssignmentId

		-- Cập nhật ClosedReason và CreatedReason
		UPDATE	pa
		SET		ClosedReason = 4
		FROM	dbo.ProspectAssignment pa
		WHERE	pa.Id = @ProspectAssignmentId

		UPDATE	pa
		SET		CreatedReason = 5
		FROM	dbo.ProspectAssignment pa
		WHERE	pa.PreviousProspectAssingmentId = @ProspectAssignmentId

		COMMIT TRANSACTION

	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		--RAISERROR(N'Lỗi xảy ra trong quá trình xử lý', 15, 10)
		THROW;
	END CATCH

END
GO