﻿
/****** Object:  StoredProcedure [telesale].[GetDynamicFormValueStatusReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[GetDynamicFormValueStatusReport]

	@CampaignId		UNIQUEIDENTIFIER,
	@AgentId		UNIQUEIDENTIFIER,
	@TempLockDays	INT

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SELECT	dfv.Status, COUNT(*) TotalCount
	FROM	dbo.Prospect p
			JOIN dbo.ProspectAssignment pa ON pa.Id = p.CurrentAssignmentId
			JOIN dbo.DynamicFormValue dfv ON dfv.ProspectAssignmentId = pa.Id
	WHERE	p.CampaignId = @CampaignId
			AND pa.AssignedAgentId = @AgentId
			AND dfv.ReferencerObjectType = 1
	GROUP BY dfv.Status
******/
END
GO