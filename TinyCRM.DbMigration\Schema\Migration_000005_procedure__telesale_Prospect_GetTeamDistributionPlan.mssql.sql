
/****** Object:  StoredProcedure [telesale].[Prospect_GetTeamDistributionPlan]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[Prospect_GetTeamDistributionPlan]

	@DistributedDate	DATETIME,
	@DistributeNew			BIT,
	@Reprospect				BIT

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SELECT	org.Id, org.OrganizationName, org.ParentId, pOrg.OrganizationName ParentOrganizationName, tempTarget.RegionId, tempTarget.RegionName, org.DistributionPriority OrgDistributionPriority, tempTarget.DistributionPriority RegionDistributionPriority,
			ISNULL(agentCount.AgentCount, 0) TeamAgentCount,
			ISNULL(tempTarget.TargetContact, 0) TargetContact,
			ISNULL(tempAssinged.AssignedCount,0) AssignedCount,
			ISNULL(tempUnsssinged.UnassignedCount,0) TeamUnssignedCount,
			ISNULL(tempActual.ActualCount,0) ActualCount
	FROM	dbo.Organization org
			JOIN dbo.Organization pOrg ON pOrg.Id = org.ParentId
			LEFT JOIN
			(
				SELECT	o.Id TeamId, COUNT(DISTINCT up.Id) AgentCount
				FROM	dbo.Organization o
						JOIN dbo.UserProfiles up ON up.OrganizationId = o.Id
						JOIN dbo.aspnet_Membership m ON m.UserId = up.Id
						JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
				WHERE	uir.RoleId = 'EC6E1D89-6251-4812-9D17-4CBBECEF44ED'
						AND m.IsApproved = 1
						--AND o.OrganizationType='TMR Team'
				GROUP BY o.Id
			) agentCount ON agentCount.TeamId = org.Id
			JOIN
			(
				SELECT	o.Id TeamId, ISNULL(rg.Id, '00000000-0000-0000-0000-000000000000') RegionId, ISNULL(rg.RegionName, N'Không xác định') RegionName, ISNULL(rg.DistributionPriority,999) DistributionPriority, SUM(tm.TargetContact) TargetContact
				FROM	dbo.Organization o
						JOIN dbo.UserProfiles up ON up.OrganizationId = o.Id
						JOIN dbo.aspnet_Membership m ON m.UserId = up.Id
						JOIN dbo.AgentTargetByCompetenceModel tm ON tm.CompetenceLevel = up.CompetenceLevel
						JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
						LEFT JOIN dbo.Province p ON p.Id = tm.ProvinceId
						LEFT JOIN dbo.Region rg ON rg.Id = p.RegionId
				WHERE	uir.RoleId = 'EC6E1D89-6251-4812-9D17-4CBBECEF44ED'
						AND m.IsApproved = 1
						AND o.OrganizationType='TMR Team'
				GROUP BY o.Id, rg.Id, rg.RegionName, ISNULL(rg.DistributionPriority,999)
			) tempTarget ON tempTarget.TeamId = org.Id
			LEFT JOIN
			(
				SELECT	AssignedTeamId, ISNULL(rg.Id, '00000000-0000-0000-0000-000000000000') RegionId, COUNT(DISTINCT p.Id) AssignedCount
				FROM	dbo.Prospect p
						JOIN dbo.ProspectAssignment pa ON pa.ProspectId = p.Id
						JOIN dbo.Contact c ON c.Id = p.ContactId
						LEFT JOIN dbo.Province pv ON pv.Id = c.ProvinceId
						LEFT JOIN dbo.Region rg ON rg.Id = pv.RegionId
				WHERE	pa.AssignedTeamId IS NOT NULL
						AND pa.CreatedReason = 1
						AND DATEDIFF(DAY, pa.AssignedTeamDate, @DistributedDate) = 0
				GROUP BY AssignedTeamId, rg.Id
			) tempAssinged ON tempAssinged.AssignedTeamId = tempTarget.TeamId AND tempAssinged.RegionId = tempTarget.RegionId
			LEFT JOIN
			(
				SELECT	AssignedTeamId, ISNULL(rg.Id, '00000000-0000-0000-0000-000000000000') RegionId, COUNT(DISTINCT p.Id) UnassignedCount
				FROM	dbo.Prospect p
						JOIN dbo.ProspectAssignment pa ON pa.Id = p.CurrentAssignmentId
						JOIN dbo.Contact c ON c.Id = p.ContactId
						LEFT JOIN dbo.Province pv ON pv.Id = c.ProvinceId
						LEFT JOIN dbo.Region rg ON rg.Id = pv.RegionId
				WHERE	pa.AssignedTeamId IS NOT NULL
						AND pa.AssignedAgentId IS NULL
				GROUP BY AssignedTeamId, rg.Id
			) tempUnsssinged ON tempUnsssinged.AssignedTeamId = tempTarget.TeamId AND tempUnsssinged.RegionId = tempTarget.RegionId
			LEFT JOIN
            (
				SELECT	ISNULL(pv.RegionId, '00000000-0000-0000-0000-000000000000') RegionId, COUNT(*) ActualCount
				FROM	dbo.[vProspect_CurrentAssignmentStatus] c
						LEFT JOIN dbo.Province pv ON pv.Id = c.ProvinceId
				WHERE	(c.AssignedTeamId IS NULL AND c.HotListGroupId IS NULL)
						AND
						(
							(@DistributeNew=1 AND c.ProspectStatus < 3 AND c.HotListGroupId IS NULL) -- Phân bổ mới
							OR (@Reprospect=1 AND (c.ProspectStatus = 3 AND DATEDIFF(DAY, GETDATE(), c.ReprospectDate) <= 0)) -- Phân bổ đám được reprospect
							
						)
				GROUP BY ISNULL(pv.RegionId, '00000000-0000-0000-0000-000000000000')
			) tempActual ON tempActual.RegionId = tempTarget.RegionId
	WHERE	org.OrganizationType = 'TMR Team'
	ORDER BY pOrg.OrganizationName, org.DistributionPriority, org.OrganizationName, tempTarget.DistributionPriority, tempTarget.RegionName
******/
END
GO