﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Webaby
{
    public class DynamicField
    {
        public Guid ObjectId;

        public Guid FormId;

        public Guid FieldId;

        public Guid? DynamicDefinedTableSchemaId;

        public string Name;

        public string DisplayName;

        public string DataType;

        public string AdditionalFilter;

        public string UiHint;

        public bool Display;

        public string SelectOptions;

        public bool IsRequired;

        public bool IsReadOnly;

        public bool FreezeValue;

        public bool IsExportExcel;

        public bool IsExportByConditionBoolean;

        public Type Type;

        public FieldType FieldType;

        public string RequiredDependency;

        public string Inject;

        public Guid? DynamicFieldSectionId;

        public string DynamicFieldSectionName;

        public Dictionary<string, string> AdditionalMetadata;

        public string Color;

        public string BackgroundColor;

        public Guid? RepresentationDynamicFieldId;

        public string VersionCode;

        public string CustomDependencies;
    }

    public class DynamicCustomDependency
    {
        public string ByDynamicFieldName { get; set; }

        public string ClientOperator { get; set; }

        public string ClientValue { get; set; }

        public DynamicCustomDependencyClientAction? ClientAction { get; set; }

        public string ValidationOperator { get; set; }

        public string ValidationValue { get; set; }

        public DynamicCustomDependencyValidationType? ValidationType { get; set; }

        public string ValidationContent { get; set; }

        public string ValidationMessage { get; set; }
    }

    public enum DynamicCustomDependencyClientAction
    {
        [Description("Hiển thị")]
        Show = 1,
        [Description("Ẩn")]
        Hide = 2,
        [Description("Disabled")]
        Disabled = 3,
        [Description("Undisabled")]
        Undisabled = 4,
    }

    public enum DynamicCustomDependencyValidationType
    {
        [Description("Bắt buộc nhập")]
        Required = 1,
        [Description("Regex")]
        Regex = 100,
    }
}