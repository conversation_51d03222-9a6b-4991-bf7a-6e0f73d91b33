﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TinyCRM.Report
{
    public class ReportDataConversionItem
    {
        //public Guid GroupById { get; set; }

        public string GroupByName { get; set; }

        public int InReceptionTime { get; set; }

        public int OverReceptionTime { get; set; }
        
        public int OverReceptionProcessTime { get; set; }

        public int InProcessTime { get; set; }
        
        public int OverProcessTime { get; set; }

        public int Done { get; set; }

        public int OverDone { get; set; }
    }

    public class DataConversionItem
    {
        public string GroupByName { get; set; }

        public int Status { get; set; }

        public int InDueAccept { get; set; }

        public int OverDueAccept { get; set; }

        public int InDueProcess { get; set; }

        public int OverDueProcess { get; set; }

        public int Total { get; set; }


    }
}