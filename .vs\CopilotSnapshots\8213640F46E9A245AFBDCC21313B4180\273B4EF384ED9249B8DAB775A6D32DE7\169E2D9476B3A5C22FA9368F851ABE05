﻿using System;
using System.Linq;
using AutoMapper;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace Webaby.Core.Role.Queries
{
    public class GetRoleByIdQuery : QueryBase<RoleData>
    {
        public GetRoleByIdQuery(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; private set; }
    }

    internal class GetRoleByIdQueryHandler : QueryHandlerBase<GetRoleByIdQuery, RoleData>
    {
        public GetRoleByIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override QueryResult<RoleData> Execute(GetRoleByIdQuery query)
        {
            var entities = EntitySet.Get<AspNetRoleEntity>().Where(x => x.Id == query.Id);
            return QueryResult.Create(entities, Mapper.Map<RoleData>);
        }
    }
}
