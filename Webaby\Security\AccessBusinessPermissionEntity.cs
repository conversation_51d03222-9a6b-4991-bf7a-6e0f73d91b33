﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Webaby.Data;

namespace Webaby.Security
{
    [Table("AccessBusinessPermission", Schema = "dbo")]    
    public class AccessBusinessPermissionEntity : IEntity, ICreatedByEnabledEntity, ICreatedDateEnabledEntity, IModifiedByEnabledEntity, IModifiedDateEnabledEntity, ISoftDeleteEnabledEntity, IDeletedByEnabledEntity
    {
        [Key]
        public Guid Id { get; set; }

        [Column]
        public Guid AccessId { get; set; }

        [Column]
        public Guid BusinessPermissionId { get; set; }

        [Column]
        public DateTime CreatedDate { get; set; }

        [Column]
        public Guid CreatedBy { get; set; }

        [Column]
        public DateTime? ModifiedDate { get; set; }

        [Column]
        public Guid? ModifiedBy { get; set; }

        [Column]
        public Boolean Deleted { get; set; }

        [Column]
        public Guid? DeletedBy { get; set; }

        [Column]
        public DateTime? DeletedDate { get; set; }
    }
}
