
/****** Object:  StoredProcedure [telesale].[GetContactCallListReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[GetContactCallListReport]
	@CampaignId UNIQUEIDENTIFIER,
	@FromDate  DATETIME,
	@ToDate   DATETIME,
	@TeamId   UNIQUEIDENTIFIER,
	@Agents   IdList READONLY,
	@Provinces  IdList READONLY,
	@DataSource  NVARCHAR(MAX) = NULL,
	@TmrManagerTeam UNIQUEIDENTIFIER,
	@StartRow  INT,
	@EndRow   INT
AS
BEGIN

	SET @FromDate = DATETIMEFROMPARTS(DATEPART(YEAR, @FromDate), DATEPART(MONTH, @FromDate), DATEPART(DAY, @FromDate), 0, 0, 0, 0);
	SET @ToDate = DATETIMEFROMPARTS(DATEPART(YEAR, @ToDate), DATEPART(MONTH, @ToDate), DATEPART(DAY, @ToDate), 23, 59, 59, 0);
	DECLARE @FromDateAsStr NVARCHAR(MAX) = CAST(@FromDate AS NVARCHAR(MAX));
	DECLARE @ToDateAsStr NVARCHAR(MAX) = CAST(@ToDate AS NVARCHAR(MAX));

	CREATE TABLE #allTmrTeam (Id UNIQUEIDENTIFIER PRIMARY KEY);

	IF @TmrManagerTeam IS NOT NULL
	BEGIN
		WITH cte_team(Id, OrganizationType) AS
		(
			SELECT cur.Id, cur.Type OrganizationType FROM dbo.Organization cur
			WHERE cur.Id=@TmrManagerTeam
			UNION ALL
			SELECT child.Id, child.Type OrganizationType FROM cte_team
			JOIN dbo.Organization child ON child.ParentId=cte_team.Id
		)
		INSERT INTO #allTmrTeam
		SELECT DISTINCT cte_team.Id
		FROM cte_team
		WHERE cte_team.OrganizationType='TMR Team';
	END

	SELECT Id INTO #Agents FROM @Agents;
	SELECT Id INTO #Provinces FROM @Provinces;

	BEGIN TRANSACTION
	SET TRAN ISOLATION LEVEL READ UNCOMMITTED

	DECLARE @cteQuery NVARCHAR(MAX) = 
	N'
	WITH cte AS
	(
		SELECT	ROW_NUMBER() OVER (ORDER BY (SELECT 1)) RowNumber,
				c.DataSource,
				c.QualifiedProgram,
				c.BackSystemContactId Rel,
				c.FullName FullName,
				c.CMND,
				cc.PhoneNumber,
				pv.ProvinceName,
				c.Address,
				up.FullName TMRName,
				up.AgentCode,
				org.OrganizationName,
				sup.FullName SupTMRName,
				FORMAT(cc.CreatedDate,''dd.MM.yyyy'') CalledDate,
				FORMAT(cc.CreatedDate,''HH:mm'') CalledTime,
				cc.Duration,
				cr.Code CallResult,
				cc.Notes
		FROM	dbo.ContactCall cc
				LEFT JOIN dbo.CallResult cr ON cr.Id=cc.CallResultId
				LEFT JOIN dbo.Contact c ON cc.ContactId=c.Id
				LEFT JOIN dbo.Province pv ON c.ProvinceId=pv.Id
				LEFT JOIN dbo.UserProfiles up ON cc.CreatedBy=up.Id
				LEFT JOIN dbo.Organization org ON org.Id=cc.CreatedByTeamId
				LEFT JOIN
				(
					SELECT	TOP 1 lead.* 
					FROM	dbo.UserProfiles lead
							JOIN dbo.UserInRoles uir ON uir.UserId=lead.Id
					WHERE	uir.RoleId=''62217D0B-F045-4C4C-ACC4-B1D4DC9F1AAC''
				) sup ON sup.OrganizationId=org.Id 
				'+IIF((SELECT COUNT(*) FROM @Agents)=0, '', ' JOIN #Agents p_ag on p_ag.Id=up.Id ')+'
				'+IIF((SELECT COUNT(*) FROM @Provinces)=0, '', ' JOIN #Provinces p_pv on p_pv.Id=pv.Id ')+'
				'+IIF(@TmrManagerTeam IS NULL, '', ' JOIN #allTmrTeam at on at.Id=org.Id ')+'
		WHERE 1=1
		'+IIF(@CampaignId IS NULL, ' ', ' AND cc.CampaignId = ''' + CAST(@CampaignId AS NVARCHAR(50)) + ''' ') + '
		'+IIF(@FromDate IS NULL, ' ', ' AND cc.CreatedDate >= '''+@FromDateAsStr+''' ')+'
		'+IIF(@ToDate IS NULL, ' ', ' AND cc.CreatedDate <= '''+@ToDateAsStr+''' ')+'
		'+IIF(@TmrManagerTeam IS NULL, IIF(@TeamId IS NULL, ' ', ' AND org.Id='''+CAST(@TeamId AS NVARCHAR(MAX))+''' '), IIF(@TeamId IS NULL, ' ', ' AND org.Id='''+CAST(@TeamId AS NVARCHAR(MAX))+''' '))+'
		'+IIF(@DataSource IS NULL OR @DataSource='', ' ', ' AND c.DataSource+N''''='''+@DataSource+''' ')+'
	)
	'+IIF(@StartRow IS NULL AND @EndRow IS NULL,
	' SELECT * FROM cte ',
	' SELECT (SELECT COUNT(*) FROM cte) TotalCount, * FROM cte WHERE cte.RowNumber BETWEEN '+CAST(@StartRow AS NVARCHAR(MAX))+' AND '+CAST(@EndRow AS NVARCHAR(MAX))+' ');

	EXEC(@cteQuery);
	COMMIT
END
GO