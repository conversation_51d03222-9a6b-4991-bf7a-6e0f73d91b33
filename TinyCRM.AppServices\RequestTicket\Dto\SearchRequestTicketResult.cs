﻿using System;
using System.Collections.Generic;
using TinyCRM.Enums;
using Webaby.Core.DynamicForm.Queries;

namespace TinyCRM.AppServices.RequestTicket.Dto
{
    public class SearchRequestTicketResult
    {
        public List<SearchRequestTicketResultItem> RequestTicketList { get; set; }

        public int TotalCount { get; set; }
    }

    public class SearchRequestTicketResultItem
    {
        public Guid Id { get; set; }

        public string Code { get; set; }

        public DateTime CreatedDate { get; set; }

        public RequestTicketStatus? Status { get; set; }

        public Difficulty? DifficultyDegree { get; set; }

        public Guid? OwnerId { get; set; }
        public string OwnerName { get; set; }

        public Guid? Level1Id { get; set; }
        public string Level1Name { get; set; }

        public Guid? Level2Id { get; set; }
        public string Level2Name { get; set; }

        public Guid? Level3Id { get; set; }
        public string Level3Name { get; set; }

        public Guid? Level4Id { get; set; }
        public string Level4Name { get; set; }

        public Guid ServiceTypeId { get; set; }

        public string Notes { get; set; }

        public string Treatment { get; set; }

        public DateTime? FinishedTicketDate { get; set; }

        public string IsoCode { get; set; }

        public Guid? CustomerId { get; set; }
        public string CustomerName { get; set; }

        public string Phone1 { get; set; }

        public string Phone2 { get; set; }

        public string Phone3 { get; set; }

        public string Email { get; set; }

        public string FacebookId { get; set; }

        public int? SourceChannel { get; set; }

        public string SourceChannelName { get; set; }

        public string BehaviorClassifications { get; set; }

        public Guid CreatedBy { get; set; }
        public string CreatedByName { get; set; }

        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public DateTime? AcceptDueDate { get; set; }

        public DateTime? AcceptedTicketDate { get; set; }

        public DateTime? ProcessDueDate { get; set; }

        public int? SoonerAcceptDueMinutes { get; set; }

        public int? SoonerProcessDueMinutes { get; set; }

        public bool? IsWorkingTime { get; set; }

        public TimeSpan? StartWorkingTime { get; set; }
        public TimeSpan? EndWorkingTime { get; set; }
        public TimeSpan? StartBreakTime { get; set; }
        public TimeSpan? EndBreakTime { get; set; }

        public bool? WorkingSaturday { get; set; }

        public bool? WorkingSunday { get; set; }

        public string VacationDays { get; set; }

        public string OvertimeDays { get; set; }

        public Guid TaskId { get; set; }

        public string TaskType { get; set; }

        public TinyCRM.Enums.TaskStatus? TaskStatus { get; set; }

        public string TaskOwnerName { get; set; }

        public Guid? DynamicFormValueId { get; set; }
        public List<DynamicFieldValueInfo> DynamicFieldList { get; set; }
    }
}