
/****** Object:  StoredProcedure [telesale].[GetAgentCallResultReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[GetAgentCallResultReport]

	@FromDate		DATETIME,
	@ToDate			DATETIME,
	@TeamId			UNIQUEIDENTIFIER,
	@AgentId		UNIQUEIDENTIFIER

AS
BEGIN

	DECLARE @PivotColumns NVARCHAR(MAX) = '[00000000-0000-0000-0000-000000000000]'
	SELECT	@PivotColumns = COALESCE(@PivotColumns + ', ', '') + '[' + CAST(Id AS NVARCHAR(50)) + ']'
	FROM	Province

	IF @ToDate IS NOT NULL SET @ToDate = DATEADD(DAY, 1, @ToDate); 

	DECLARE @WhereString NVARCHAR(MAX) = 'cc.Id=cc.Id ' +
										 IIF(@FromDate IS NOT NULL, ' AND cc.CreatedDate >= @FromDate ', ' ') +
										 IIF(@ToDate IS NOT NULL, ' AND cc.CreatedDate < @ToDate ', ' ') +
										 IIF(@TeamId IS NOT NULL, ' AND up.OrganizationId IN (select Id from #TempOrg) ', ' ') +
										 IIF(@AgentId IS NOT NULL, ' AND cc.CreatedBy = @AgentId ', ' ');
	--'(@FromDate IS NULL OR cc.CreatedDate >= @FromDate) AND (@ToDate IS NULL OR cc.CreatedDate < DATEADD(DAY, 1, @ToDate)) AND (@TeamId IS NULL OR up.OrganizationId IN (select Id from #TempOrg)) AND (@AgentId IS NULL OR cc.CreatedBy = @AgentId) '

	DECLARE @ExecuteString NVARCHAR(MAX) = 
	'
		WITH cteOrg(Id, OrganizationType) AS
		(
			SELECT	Id, OrganizationType
			FROM	dbo.Organization
			WHERE	@TeamId IS NOT NULL 
					AND Id = @TeamId
			UNION ALL
			SELECT	chilOrg.Id, chilOrg.OrganizationType
			FROM	dbo.Organization chilOrg
					JOIN cteOrg cte ON chilOrg.ParentId = cte.Id
		)
		SELECT	Id
		INTO	#TempOrg
		FROM	cteOrg
		WHERE	OrganizationType = ''TMR Team''
		SELECT	cr.Code CallResultCode, cr.EnglishDescription, cr.VietnameseDescription, cr.IsConnected, cr.IsContacted, cr.IsConsulted, cr.IsInterested, cr.IsEligible, cr.IsAppointmentMade, tempTable.*
		FROM	dbo.CallResult cr
				LEFT JOIN
				(
					SELECT	*
					FROM	(
								SELECT	cr.Id CallResultId, cr.Code, ISNULL(c.ProvinceId,''00000000-0000-0000-0000-000000000000'') ProvinceId
								FROM	dbo.ContactCall cc
										JOIN dbo.ProspectAssignment pa ON pa.Id = cc.ProspectAssignmentId
										JOIN dbo.Prospect ps ON ps.Id = pa.ProspectId
										JOIN dbo.Contact c ON c.Id = ps.ContactId
										JOIN dbo.CallResult cr ON cr.Id = cc.CallResultId
										JOIN dbo.UserProfiles up ON up.Id = cc.CreatedBy
								WHERE	' + @WhereString + '
							) temp
							PIVOT
							(
								COUNT(Code) FOR ProvinceId IN (' + @PivotColumns + ')
							) tempPivot
				) tempTable ON tempTable.CallResultId = cr.Id
		UNION
		SELECT	''13'' CallResultCode, N''12 - Appointment (Old appointment)'' EnglishDescription, N''12 - Cuộc hẹn được thiết lập (Hẹn cũ)'' VietnameseDescription, NULL IsConnected, NULL IsContacted, NULL IsConsulted, NULL IsInterested, NULL IsEligible, NULL IsAppointmentMade, ''00000000-0000-0000-0000-000000000000'' CallResultId, tempPivot.*
		FROM	(
					SELECT	''00000000-0000-0000-0000-000000000000'' CallResultId, ISNULL(c.ProvinceId,''00000000-0000-0000-0000-000000000000'') ProvinceId
					FROM	dbo.Appointment cc
							JOIN dbo.Contact c ON c.Id = cc.ContactId
							JOIN dbo.UserProfiles up ON up.Id = cc.CreatedBy
					WHERE	cc.Status <> 4 AND cc.Status <> 5 AND cc.PreviousAppointmentId IS NULL
							AND ' + @WhereString + '
				) temp
				PIVOT
				(
					COUNT(CallResultId) FOR ProvinceId IN (' + @PivotColumns + ')
				) tempPivot
		ORDER BY cr.Code
	'

	EXEC sp_executesql	@ExecuteString, 
						N'@FromDate DATETIME, @ToDate DATETIME,	@TeamId UNIQUEIDENTIFIER, @AgentId UNIQUEIDENTIFIER', 
						@FromDate, @ToDate, @TeamId, @AgentId

END
GO