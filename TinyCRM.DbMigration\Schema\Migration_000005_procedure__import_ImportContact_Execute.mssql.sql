
/****** Object:  StoredProcedure [import].[ImportContact_Execute]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[ImportContact_Execute]
	@ImportSessionId UNIQUEIDENTIFIER,
	@UserId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	
	INSERT INTO dbo.Contact
	( 
		Id ,
        FullName ,
        Address ,
        WardId ,
        DistrictId ,
        ProvinceId ,
        Phone ,
        DOB ,
        DataSource ,
        CreatedDate ,
        Job ,
        Email ,
        Inactive ,
        LastCallId ,
        Notes ,
        CreatedBy ,
        AccountVisitId ,
        ResultTypeId ,
        AccountVisitUserId ,
        Income ,
        MaritalStatus ,
        Gender ,
        Status ,
        Phone2 ,
        Phone3 ,
        CMND,
		BackSystemContactId, 
		QualifiedProgram, 
		CompanyName, 
		CompanyAddress, 
		CompanyType, 
		CompanyPhone, 
		IncomeSource, 
		Nationality, 
		AddressMailing,
		AddressPermanent,
		AdditionalData,
		AdditionalTemplateId,
		DataQuality,
		CustomerId
	)
	SELECT  ContactId,
			IIF([FullName] IS NOT NULL, [FullName], N'') FullName,
			[Address] Address,
			NULL WardId,
			NULL DistrictId,
			ProvinceValid ProvinceId,
			[PhoneValid] Phone,
			DOBValid DOB,
			Source DataSource,
			GETDATE() CreatedDate,
			Job Job,
			EmailValid Email,
			0 Inactive,
			NULL LastCallId,
			IIF(LEN(Note) >= 1999, SUBSTRING(Note, 0, 1998), Note) Notes,
			@UserId CreatedBy,
			NULL AccountVisitId,
			NULL ResultTypeId,
			NULL AccountVisitUserId,
			Income Income,
			IIF(MaritalStatusValid IS NOT NULL, MaritalStatusValid, 0) MaritalStatus,
			IIF(GenderValid IS NOT NULL, GenderValid, 0) Gender,
			1 Status,
			''  Phone2,
			''  Phone3,
			CMND CMND,
			BackSystemContactId BackSystemContactId, 
			QualifiedProgram QualifiedProgram, 
			CompanyName CompanyName, 
			CompanyAddress CompanyAddress, 
			CompanyType CompanyType, 
			CompanyPhone CompanyPhone, 
			IncomeSource IncomeSource, 
			Nationality Nationality, 
			AddressMailing AddressMailing, 
			AddressPermanent AddressPermanent, 
			AdditionalData,
			AdditionalTemplateId,
			DataQuality,
			CustomerId
	FROM import.ContactRaw
	WHERE ImportSessionId = @ImportSessionId
		AND ErrorCode = 'NewContact'
END
GO