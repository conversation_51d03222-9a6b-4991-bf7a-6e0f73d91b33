
/****** Object:  StoredProcedure [telesale].[GetAppointmentListReport_XXX]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


--ALTER PROCEDURE [telesale].[GetAppointmentListReport]
Create PROCEDURE [telesale].[GetAppointmentListReport_XXX]
	@AppointmentId UNIQUEIDENTIFIER = NULL
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SELECT	Id, FullName, DOB, RelatedContactId,
			ROW_NUMBER() OVER (PARTITION BY RelatedContactId ORDER BY FullName) ChildRowNumber
	INTO	#TempChildTable
	FROM	dbo.ContactRelationship
	WHERE	RelationshipType = 4;

	SELECT	*, ROW_NUMBER() OVER (PARTITION BY RelatedContactId ORDER BY FullName) RowNumber
	INTO	#TempSpouse
	FROM	dbo.ContactRelationship
	WHERE	RelationshipType = 3;


		SELECT	ROW_NUMBER() OVER (ORDER BY a.MeetDate DESC) STT
				, a.FeedbackNotes
				, a.Id AppointmentId
				, (CASE WHEN a.PreviousAppointmentId IS NOT NULL THEN '(HC)' ELSE '' END) HC, l.UserDefinedFormatCode LeadID, dmoTeam.DmotLeaderName SUPDMO, dmoTeam.Phone SUPDMOPhone
				, sdmoUp.FullName + 
				IIF(	
					la.SuggestedFieldSaleReason=2,N' (Gửi)'
					,IIF(	la.SuggestedFieldSaleReason=3,N' (Cặp đôi)'
					,IIF(	la.SuggestedFieldSaleReason=5,N' (Ngoài giờ)'
					,IIF(	la.SuggestedFieldSaleReason=1,N' (HC)'
					,IIF(	la.SuggestedFieldSaleReason=6,N' (Import)'
					,IIF(	la.SuggestedFieldSaleReason=4,N' (Support phân)'
					,IIF(	la.SuggestedFieldSaleReason=7,N' (HC-)'
					,IIF(	la.SuggestedFieldSaleReason=8,N' (HC- chưa gặp)'
					,'')))))))
					) DMOName, 
				dmoUp.AgentCode DMOCode, tmrTeam.TmrLeaderName, tmrTeam.Phone SUPTMRPhone
				, tmrUp.FullName TMRNAME, tmrUp.AgentCode TMRCode, FORMAT(a.CreatedDate,'dd.MM.yyyy') CallDate, FORMAT(a.MeetDate,'dd.MM.yyyy') APPTDATE, FORMAT(a.MeetDate,'HH\Hmm') APPTTIME 
				, c.DataSource [SOURCE], c.FullName CLIENTNAME, FORMAT(c.DOB,'dd.MM.yyyy') DOB
				, ISNULL(spouse.FullName,'') SpouseName, FORMAT(spouse.DOB,'dd.MM.yyyy') SpouseDOB
				, ISNULL(child1.FullName,'') Child1Name, FORMAT(child1.DOB,'dd.MM.yyyy') Child1DOB
				, ISNULL(child2.FullName,'') Child2Name, FORMAT(child2.DOB,'dd.MM.yyyy') Child2DOB
				, ISNULL(child3.FullName,'') Child3Name, FORMAT(child3.DOB,'dd.MM.yyyy') Child3DOB
				, a.ProductBudget, c.Job, a.MeetAddress [Address], ISNULL(w.WardName,'') WardName, ISNULL(dt.DistrictName,'') DistrictName, ISNULL(pv.ProvinceName,'') ProvinceName
				, c.Phone, c.Phone2, c.Phone3, a.Notes, l.ExternalOldLeadCode, supOrg.OrganizationName AssignedFieldSaleTeamName, dmoUp.FullName AssignedFieldSaleName, aprc.ResultCode
				, p.IsHot,
				CASE
					WHEN a.Status = 1 THEN N'Chờ KQ'
					WHEN a.Status = 2 THEN N'Chưa gặp'
					WHEN a.Status = 3 THEN N'Đã gặp'
					WHEN a.Status = 4 THEN N'Bị hủy'
					WHEN a.Status = 5 THEN N'Trả về'
				END AppointmentStatus
		FROM	dbo.Appointment a
				LEFT JOIN dbo.AppointmentResultCode aprc ON aprc.Id = a.AppointmentResultCodeId
				JOIN dbo.LeadAssignment la ON a.Id=la.WaitingAppointmentId
				JOIN dbo.Lead l ON l.CurrentLeadAssignmentId=la.Id
				JOIN dbo.ProspectAssignment pa ON pa.Id=l.CreatedByProspectAssignmentId

				LEFT JOIN dbo.UserProfiles sdmoUp ON sdmoUp.Id=la.SuggestedFieldSaleId -- Suggested DMO

				LEFT JOIN dbo.Organization supOrg ON supOrg.Id = la.AssignedFieldSaleTeamId
				LEFT JOIN -- Sup DMO
				(
					SELECT	up.OrganizationId DmotTeamId,up.FullName DmotLeaderName,up.PhoneNumber Phone
					FROM	dbo.UserProfiles up 
							JOIN dbo.aspnet_Membership m ON m.UserId = up.Id
							JOIN dbo.UserInRoles ur ON ur.UserId=up.Id
							JOIN dbo.Roles r ON r.Id = ur.RoleId
					WHERE	m.IsApproved = 1
							AND r.Id = '94E5A159-A404-4D5C-9D6D-BE0C49ADC34C' -- Field Sale Leader
				
				) dmoTeam ON la.AssignedFieldSaleTeamId=dmoTeam.DmotTeamId
				LEFT JOIN dbo.UserProfiles dmoUp ON dmoUp.Id=la.AssignedFieldSaleId -- DMO

				LEFT JOIN dbo.UserProfiles tmrUp ON tmrUp.Id=pa.AssignedAgentId -- TMR
				LEFT JOIN -- Sup TMR
				(
					SELECT	up.OrganizationId TmrTeamId,ISNULL(o.Code,up.FullName) TmrLeaderName,up.PhoneNumber Phone, o.ParentId ParentOrg
					FROM	dbo.UserProfiles up 
							JOIN dbo.aspnet_Membership m ON m.UserId = up.Id
							JOIN dbo.UserInRoles ur ON ur.UserId=up.Id
							JOIN dbo.Organization o ON o.Id=up.OrganizationId
							JOIN dbo.Roles r ON r.Id = ur.RoleId
					WHERE	m.IsApproved = 1
							AND r.Id = '62217D0B-F045-4C4C-ACC4-B1D4DC9F1AAC' -- Team Leader
				) tmrTeam ON pa.AssignedTeamId = tmrTeam.TmrTeamId


				JOIN dbo.Prospect p ON pa.ProspectId =p.Id
				JOIN dbo.Contact c ON c.Id=p.ContactId
				JOIN dbo.Province pv ON pv.Id=a.ProvinceId
				LEFT JOIN dbo.District dt ON dt.Id=a.DistrictId
				LEFT JOIN dbo.Ward w ON w.Id=a.WardId
				LEFT JOIN #TempSpouse spouse ON spouse.RelatedContactId = c.Id AND spouse.RowNumber = 1
				LEFT JOIN #TempChildTable child1 ON child1.RelatedContactId = c.Id AND child1.ChildRowNumber = 1
				LEFT JOIN #TempChildTable child2 ON child2.RelatedContactId = c.Id AND child2.ChildRowNumber = 2
				LEFT JOIN #TempChildTable child3 ON child3.RelatedContactId = c.Id AND child3.ChildRowNumber = 3
		WHERE	(@AppointmentId IS NOT NULL AND a.Id=@AppointmentId)
	
******/
END
GO