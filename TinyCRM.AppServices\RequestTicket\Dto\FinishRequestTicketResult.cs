﻿using System;
using System.Collections.Generic;

namespace TinyCRM.AppServices.RequestTicket.Dto
{
    public class FinishRequestTicketArguments
    {
        public Guid RequestTicketId { get; set; }

        public int? SourceChannel { get; set; }

        public Guid? BusinessResultId { get; set; }

        public Guid? ModifiedBy { get; set; }
    }

    public class FinishRequestTicketResult
    {
        public bool IsSuccess { get; set; }

        public string ErrorMessage { get; set; }

        public string SuccessMessage { get; set; }

        public List<string> WarningMessage { get; set; }

        public string RequestTicketCode { get; set; }

        public string ReturnUrl { get; set; }
    }

    public class DeleteRequestTicketResultDto
    {
        public bool IsSuccess { get; set; }

        public string ErrorMessage { get; set; }

        public string SuccessMessage { get; set; }

        public List<string> WarningMessage { get; set; }
    }
}