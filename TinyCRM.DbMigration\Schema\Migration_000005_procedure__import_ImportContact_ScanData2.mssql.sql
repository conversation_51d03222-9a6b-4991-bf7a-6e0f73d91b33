﻿
/****** Object:  StoredProcedure [import].[ImportContact_ScanData2]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [import].[ImportContact_ScanData2]
	@UserId UNIQUEIDENTIFIER,
	--@File VARBINARY(MAX)=NULL,
--	@ImportDescription NVARCHAR(MAX),
	@ImportSessionId UNIQUEIDENTIFIER
	--@RawTable RawTableParam READONLY
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	SET NOCOUNT ON;
	
	--EXEC import.FillContactRaw @UserId = @UserId, @ImportDescription=@ImportDescription, @ImportSessionId =@ImportSessionId, @File = @File, @RawTable = @RawTable
	UPDATE import.ContactRaw SET ErrorCode=0 WHERE ImportSessionId=@ImportSessionId
	
	UPDATE import.ContactRaw SET ErrorCode=1 WHERE PhoneValid IS NULL AND ImportSessionId=@ImportSessionId
    
	----check dup internal
	UPDATE cig SET ErrorCode=2 
	FROM import.ContactRaw cig
	JOIN
	(
		SELECT
		ROW_NUMBER() OVER (PARTITION BY [PhoneValid] ORDER BY Id) rn,Id
		FROM import.ContactRaw
		WHERE ImportSessionId=@ImportSessionId
		AND ErrorCode=0
	) ci ON cig.Id=ci.Id
	WHERE ci.rn>1 --AND cig.PhoneValid IS NOT NULL AND cig.PhoneValid <> N'' 
	AND cig.ImportSessionId=@ImportSessionId
	
	----check dup master
	UPDATE cig SET cig.ErrorCode=3
	FROM import.ContactRaw cig
	JOIN dbo.Contact c ON c.Phone=cig.[PhoneValid]
	WHERE cig.ErrorCode=0 AND cig.ImportSessionId=@ImportSessionId
	
	----validate province
	UPDATE cig SET cig.ProvinceValid=p.Id 
	FROM import.ContactRaw cig
	JOIN dbo.Province p ON cig.Areas=p.ImportCodeName
	WHERE cig.ErrorCode=0 AND cig.ImportSessionId=@ImportSessionId
	
	UPDATE import.ContactRaw SET WarningCode=1 WHERE Areas IS NOT NULL AND ProvinceValid IS NULL AND ErrorCode=0 AND ImportSessionId=@ImportSessionId
	UPDATE import.ContactRaw SET WarningCode= WarningCode | 2 WHERE Gender IS NULL AND ImportSessionId=@ImportSessionId
******/
END
SET QUOTED_IDENTIFIER ON
SET ANSI_NULLS ON
GO