﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Webaby.Data;
using Webaby.Localization;

namespace Webaby.Core.File.Commands
{
    public class CreateEditFileCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string FileName { get; set; }

        public string Extensions { get; set; }

        public string Descriptions { get; set; }

        public byte[] Data { get; set; }

        public Guid? ReferenceObjectId { get; set; }

        public string ReferenceObjectType { get; set; }

        public CreateEditFileCommand()
        {

        }

        public CreateEditFileCommand(Guid id, IFormFile postedFile, Guid? referenceId, string referenceType)
        {
            Id = id;
            FileName = postedFile.FileName;
            Extensions = string.IsNullOrEmpty(Path.GetExtension(postedFile.FileName)) ? Extensions : Path.GetExtension(postedFile.FileName);
            ReferenceObjectId = referenceId;
            ReferenceObjectType = referenceType;

            byte[] buffer = new byte[postedFile.Length];

            using var memoryStream = new MemoryStream();
            postedFile.CopyToAsync(memoryStream).GetAwaiter().GetResult();
            Data = memoryStream.ToArray();
        }
    }

    public class CreateEditFileCommandHandler : CommandHandlerBase<CreateEditFileCommand>
    {
        public CreateEditFileCommandHandler(IText text, IMapper mapper, IRepository repository, IEntitySet entitySet, IQueryExecutor queryExecutor, ICommandExecutor commandExecutor, ILocalTransactionManager transactionManager, IEventBus eventBus)
            : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus) { }

        public override async Task ExecuteAsync(CreateEditFileCommand command)
        {
            var entity = await EntitySet.GetAsync<FileEntity>(command.Id);
            if (entity == null)
            {
                entity = new FileEntity();
            }
            Mapper.Map(command, entity);
            entity.Data = command.Data;
            
            await Repository.SaveAsync(entity);
        }
    }
}