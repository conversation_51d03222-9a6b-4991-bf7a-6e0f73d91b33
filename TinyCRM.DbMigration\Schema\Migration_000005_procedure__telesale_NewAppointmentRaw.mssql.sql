
/****** Object:  StoredProcedure [telesale].[NewAppointmentRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[NewAppointmentRaw]
	@UserId UNIQUEIDENTIFIER,
	@ImportSessionId UNIQUEIDENTIFIER,
	@File VARBINARY(MAX),
	@RawTable AppointmentRawType READONLY
AS
BEGIN
	SET NOCOUNT ON;
	INSERT INTO dbo.AppointmentImportSession
	        ( Id ,
	          BinaryFile ,
	          CreateBy ,
	          CreateDate ,
	          TotalRows
	        )
	VALUES  ( @ImportSessionId ,
	          @File,
	          @UserId,
	          GETDATE(),
	          (SELECT COUNT(*) FROM @RawTable)
	        )
	
	--TRUNCATE TABLE dbo.AppointmentRaw
	INSERT INTO dbo.AppointmentRaw
	        ( ImportSessionId,
			  HC ,
	          LeadCode ,
	          SupDMO ,
	          NameDMO ,
	          SupTMR ,
	          PhoneOfSupTMR ,
	          NameTMR ,
	          CodeTMR ,
	          CallDate ,
	          Appointment<PERSON>ime ,
	          ContactName ,
	          ContactDOB ,
	          SpouseName ,
	          SpouseDOB ,
	          Child1Name ,
	          Child1DOB ,
	          Child2Name ,
	          Child2DOB ,
	          Child3Name ,
	          Child3DOB ,
	          ProductValue ,
	          Carrier ,
	          Address ,
	          Street ,
	          Ward ,
	          District ,
	          Province ,
	          CellPhone ,
			  CellPhoneNote,
	          HomePhone ,
			  HomePhoneNote ,
	          DataSource ,
	          Remark ,
	          CodeDMO,
			  AppDatePart,
			  AppTimePart
	        )
	SELECT @ImportSessionId ImportSessionId, * FROM @RawTable
END
GO