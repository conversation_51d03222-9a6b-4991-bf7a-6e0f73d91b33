﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Webaby.Core.DynamicForm
{
    public static class DynamicValidateOperatorExtensions
    {
        public static bool Evaluate<T>(this T val, string expression)
        {
            Type type = val.GetType();
            var operators = new string[6] { "equal", "differ", "range", "in", "notin", "regex" };
            if (expression.IsNotNullOrEmpty())
            {
                if (operators.Any(x => expression.StartsWith(x, StringComparison.OrdinalIgnoreCase)))
                {
                    var strOperator = operators.First(x => expression.StartsWith(x, StringComparison.OrdinalIgnoreCase)).ToLower();
                    var strParenthese = expression.Substring(strOperator.Length);
                    var strCompareValue = (strParenthese.Remove(strParenthese.Length - 1, 1)).Remove(0, 1);
                    if (strOperator == "equal")
                    {
                        if (strCompareValue.ConvertToBasicType(type).Equals(val))
                        {
                            return true;
                        }
                    }
                    if (strOperator == "differ")
                    {
                        if (!strCompareValue.ConvertToBasicType(type).Equals(val))
                        {
                            return true;
                        }
                    }
                    if (strOperator == "range")
                    {
                        if (type.IsNumericType())
                        {
                            var arrValue = strCompareValue.Split(';');
                            var doubleObjValue = ((IConvertible)val).ToDouble(null);
                            bool leftOperator;
                            if (!string.IsNullOrEmpty(arrValue[0]))
                            {
                                var minCompareValue = ((IConvertible)arrValue[0].ConvertToBasicType(type)).ToDouble(null);
                                leftOperator = strParenthese[0] == '[' ?
                                    minCompareValue <= doubleObjValue
                                    : (strParenthese[0] == '(' ?
                                    minCompareValue < doubleObjValue : false);
                            }
                            else
                            {
                                leftOperator = true;
                            }

                            bool rightOperator;
                            if (!string.IsNullOrEmpty(arrValue[1]))
                            {
                                var maxCompareValue = ((IConvertible)arrValue[1].ConvertToBasicType(type)).ToDouble(null);
                                rightOperator = strParenthese[strParenthese.Length - 1] == ']' ?
                                    maxCompareValue >= doubleObjValue
                                    : (strParenthese[strParenthese.Length - 1] == ')' ?
                                        maxCompareValue > doubleObjValue : false);
                            }
                            else
                            {
                                rightOperator = true;
                            }

                            if (leftOperator && rightOperator)
                            {
                                return true;
                            }
                        }
                    }

                    if (strOperator == "in")
                    {
                        var arrValue = strCompareValue.Split(';');
                        if (arrValue.Any(x => !x.ConvertToBasicType(type).Equals(val)))
                        {
                            return true;
                        }
                    }

                    if (strOperator == "notin")
                    {
                        var arrValue = strCompareValue.Split(';');
                        if (arrValue.All(x => !x.ConvertToBasicType(type).Equals(val)))
                        {
                            return true;
                        }
                    }

                    if (strOperator == "regex")
                    {
                        Regex validateRegex = new Regex(strCompareValue);
                        if (validateRegex.IsMatch(val.ToString()))
                        {
                            return true;
                        }
                    }
                }
                return false;
            }
            return true;
        }
    }
}