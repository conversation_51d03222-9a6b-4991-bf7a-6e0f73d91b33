﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Text;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using Webaby.Core.File.Commands;
using Webaby.EntityData;
using Webaby.Web;

namespace Webaby.Core.File.Queries
{
    public class FileData : IEntityData, IFileContainer
    {
        public Guid Id { get; set; }

        public string FileName { get; set; }

        public string Extensions { get; set; }

        public string Descriptions { get; set; }

        public byte[] Data { get; set; }

        public Guid? ReferenceFileId { get; set; }

        public Guid? ReferenceObjectId { get; set; }

        public string ReferenceObjectType { get; set; }

        public bool IsEmpty { get; set; }

        private readonly IQueryExecutor _queryExecutor;
        private readonly ICommandExecutor _commandExecutor;
        private readonly IConfiguration _configuration;
        public FileData(IQueryExecutor queryExecutor, ICommandExecutor commandExecutor, IConfiguration configuration)
        {
            _queryExecutor = queryExecutor;
            _commandExecutor = commandExecutor;
            _configuration = configuration;
        }

        public bool TryMapping(string objectName,
            NameValueCollection values,
            HttpPostedFileCollection files,
            out Dictionary<string, List<string>> validateErrors)
        {
            validateErrors = new Dictionary<string, List<string>>();

            IFormFile file = null;
            if (files != null)
            {
                file = (IFormFile)files[objectName];
            }

            var id = values[objectName + ".Id"];
            if (!string.IsNullOrEmpty(id))
            {
                Id = new Guid(id);
            }
            else
            {
                if (file != null && file.Length > 0)
                {
                    Id = Guid.NewGuid();

                    var mem = new MemoryStream();
                    var extension = Path.GetExtension(file.FileName);
                    file.CopyToAsync(mem).GetAwaiter().GetResult();
                    Data = mem.ToArray();
                    Extensions = extension;
                    FileName = file.FileName;
                }
                else
                {
                    string base64String = values[objectName + ".Base64Data"];
                    if (base64String.IsNotNullOrEmpty())
                    {
                        Id = Guid.NewGuid();
                        Data = Convert.FromBase64String(base64String);

                        string base64FileName = values[objectName + ".Base64FileName"];
                        if (base64FileName.IsNotNullOrEmpty())
                        {
                            Extensions = Path.GetExtension(base64FileName);
                            FileName = Path.GetFileName(base64FileName);
                        }
                        else
                        {
                            FileName = Id.ToString();
                        }
                    }
                }
            }

            ReferenceObjectType = "dbo.DynamicForm";

            return !validateErrors.Any();
        }

        public async Task SaveAsync()
        {
            if (!string.IsNullOrEmpty(FileName))
            {
                var uploadFileCommand = new CreateEditFileCommand()
                {
                    Id = this.Id,
                    Data = this.Data,
                    Descriptions = string.Empty,
                    Extensions = this.Extensions,
                    FileName = this.FileName,
                    ReferenceObjectType = this.ReferenceObjectType
                };
                await _commandExecutor.ExecuteAsync(uploadFileCommand);
            }
        }

        public async Task LoadAsync()
        {
            var fileQuery = new GetFileByIdQuery()
            {
                Id = this.Id,
            };
            var file = await _queryExecutor.ExecuteOneAsync(fileQuery);
            if (file != null)
            {
                this.FileName = file.FileName;
                this.Extensions = file.Extensions;
                this.Descriptions = file.Descriptions;
                this.ReferenceObjectId = file.ReferenceObjectId;
                this.ReferenceObjectType = file.ReferenceObjectType;
            };
        }

        public Dictionary<string, string> GetAdditionalMetadata(Guid? dynamicFieldDefinitionId = null, Guid? dynamicFieldValueId = null)
        {
            return new Dictionary<string, string>();
        }

        public bool HasValue(string objectName, NameValueCollection values, HttpPostedFileCollection files)
        {
            var idValue = values[objectName + ".Id"];
            var fileValue = (IFormFile)files[objectName];
            return (idValue.IsNotNullOrEmpty() && idValue.Trim().IsNotNullOrEmpty()) || (fileValue != null && fileValue.Length > 0);
        }

        public string DisplayContent(string templateHint, bool isHtml = false)
        {
            var applicationUrl = _configuration.GetValue<string>("application.url");
            var link = string.Format("http://{0}/File/Download?fileId={1}", applicationUrl, this.Id);
            if (isHtml)
            {
                var builder = new TagBuilder("a");
                builder.MergeAttribute("href", link);
                builder.InnerHtml.Append(this.FileName);

                using var writer = new StringWriter();
                builder.WriteTo(writer, HtmlEncoder.Default);
                string html = writer.ToString();

                return html;
            }
            return link;
        }

        public string GetValue()
        {
            return Id.ToString();
        }

        public bool ExecuteExpression(string expression)
        {
            return true;
        }
    }
}