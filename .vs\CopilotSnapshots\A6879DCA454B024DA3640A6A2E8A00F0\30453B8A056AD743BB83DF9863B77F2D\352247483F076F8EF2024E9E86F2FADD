﻿using System;
using System.Linq;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DynamicForm.Queries
    {
    public class GetDynamicFieldInFormByOrderQuery : QueryBase<DynamicFieldDefinitionData>
    {
        public Guid? FieldId { get; set; }

        public int Order { get; set; }

        public Guid FormId { get; set; }
    }

    internal class GetDynamicFieldInFormByOrderQueryHandler : QueryHandlerBase<GetDynamicFieldInFormByOrderQuery, DynamicFieldDefinitionData>
    {
        public GetDynamicFieldInFormByOrderQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DynamicFieldDefinitionData>> ExecuteAsync(GetDynamicFieldInFormByOrderQuery query)
    {
            var fieldEntities = await EntitySet.GetAsync<DynamicFieldDefinitionEntity>();
            if (query.FieldId.HasValue)
    {
                fieldEntities = fieldEntities.Where(x => x.Id != query.FieldId);
            }
            var mainQuery = from field in fieldEntities
                where field.DynamicFormId == query.FormId && field.Order == query.Order
                select field;
            return QueryResult.Create(mainQuery, x => Mapper.Map<DynamicFieldDefinitionData>(x));
        }
    }
}

