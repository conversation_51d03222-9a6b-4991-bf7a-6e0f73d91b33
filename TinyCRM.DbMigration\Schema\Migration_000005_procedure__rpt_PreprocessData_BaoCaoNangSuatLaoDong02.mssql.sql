
/****** Object:  StoredProcedure [rpt].[PreprocessData_BaoCaoNangSuatLaoDong02]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [rpt].[PreprocessData_BaoCaoNangSuatLaoDong02]

	@ProcessDate DATETIME

AS
BEGIN

	DECLARE @FromDate DATETIME = CONVERT(DATE, @ProcessDate)
	DECLARE @ToDate DATETIME = DATEADD(DAY, 1, @ProcessDate)

	DECLARE @ErrorMessage NVARCHAR(MAX)

	-- 1. Thống kê tác động phiếu
	--==============================================================================
	BEGIN TRANSACTION trans_UserActivityTicket
	BEGIN TRY
		
		DELETE rpt.UserActivityTicket WHERE AuditEntityCreatedDate >= @FromDate AND AuditEntityCreatedDate < @ToDate

		INSERT rpt.UserActivityTicket
		        ( 
					Id ,
					WorkingShift ,
					RequestTicketId,
					ActivityUserId ,
					TicketCreatedDate ,
					AuditEntityCreatedDate ,
					SourceChannel ,
					Nation ,
					CustomerClass ,
					IsLookupActivityTicket,
					IsComplainActivityTicket
		        )
		SELECT	NEWID(), temp.WorkingShift, temp.RequestTicketId, temp.OwnerId, temp.CreatedDate, temp.AuditEntityCreatedDate, temp.SourceChannel, temp.Nation, temp.CustomerClass,
				CASE WHEN temp.Level1Id = 'EC1C1487-C15C-4CAC-8514-E2AEE6C17B67' THEN 1 ELSE 0 END IsLookupActivityTicket,
				CASE WHEN temp.Level1Id = 'BC592D9B-7443-4297-85A6-B7F803C33843' THEN 1 ELSE 0 END IsComplainActivityTicket
		FROM	(
					SELECT	DISTINCT CONVERT(DATE, rt.CreatedDate) CreatedDate, CONVERT(DATE, au.ModifiedDate) AuditEntityCreatedDate,
							au.ModifiedBy OwnerId, SourceChannel,
							rt.Id RequestTicketId, rt.Level1Id,
							CASE
								WHEN CONVERT(TIME(7), rt.CreatedDate) >= '07:00:00' AND CONVERT(TIME(7), rt.CreatedDate) <= '21:00:00' THEN N'7h đến 21h'
								ELSE N'21h đến 7h'
							END WorkingShift,
							CASE WHEN c.Nation IS NULL THEN N'VN' ELSE N'Other' END Nation,
							ISNULL(c.CustomerClass,0) CustomerClass
					FROM	dbo.RequestTicket rt WITH(NOLOCK)
							JOIN dbo.Customer c WITH(NOLOCK) ON c.Id = rt.CustomerId
							JOIN dbo.AuditEntityChange au WITH(NOLOCK) ON rt.Id = au.KeyValue
					WHERE	rt.Deleted = 0
							AND rt.CreatedBy <> au.ModifiedBy AND rt.OwnerId <> au.ModifiedBy
							AND au.ModifiedDate >= @FromDate
							AND au.ModifiedDate < @ToDate
				) temp
				LEFT JOIN rpt.UserActivityTicket aut ON aut.RequestTicketId = temp.RequestTicketId AND aut.ActivityUserId = temp.OwnerId
		WHERE	aut.Id IS NULL

		-- Insert Success log
		INSERT INTO rpt.PreprocessUserActivityTicket_Log (Id, PreprocessDate, IsSuccess) VALUES (NEWID(), @ProcessDate, 1)

		COMMIT TRANSACTION trans_UserActivityTicket

    END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION trans_UserActivityTicket
		-- Insert Success log
		SET @ErrorMessage = (SELECT ERROR_MESSAGE())
		INSERT INTO rpt.PreprocessUserActivityTicket_Log (Id, PreprocessDate, IsSuccess, ErrorMessage) VALUES (NEWID(), @ProcessDate, 0, @ErrorMessage)
	END CATCH

	-- 2. Thống kê BÁN CHÉO
	--==============================================================================
	BEGIN TRANSACTION  trans_UserCrossSaleTicket
	BEGIN TRY

		DELETE rpt.UserCrossSaleTicket WHERE TicketCreatedDate >= @FromDate AND TicketCreatedDate < @ToDate

		INSERT rpt.UserCrossSaleTicket
		        ( 
					Id ,
					WorkingShift ,
					ActivityUserId ,
					TicketCreatedDate ,
					SourceChannel ,
					Nation ,
					CustomerClass ,
					ActivityTicketCount
		        )
		SELECT  NEWID(), 
				CASE
					WHEN CONVERT(TIME(7), rt.CreatedDate) >= '07:00:00' AND CONVERT(TIME(7), rt.CreatedDate) <= '21:00:00' THEN N'7h đến 21h'
					ELSE N'21h đến 7h'
				END WorkingShift,
				rt.OwnerId,
				CONVERT(DATE, rt.CreatedDate) CreatedDate, 
				 rt.SourceChannel,
				CASE WHEN c.Nation IS NULL THEN N'VN' ELSE N'Other' END Nation,
				ISNULL(c.CustomerClass,0) CustomerClass,
				COUNT(*) TotalCount
		FROM	dbo.RequestTicket rt WITH(NOLOCK)
				JOIN dbo.DynamicFieldValue dfv WITH(NOLOCK) ON dfv.DynamicFormValueId = rt.DynamicFormValueId
				JOIN dbo.DynamicFieldDefinition dfd WITH(NOLOCK) ON dfv.DynamicFieldId = dfd.Id
				JOIN dbo.Customer c WITH(NOLOCK) ON rt.CustomerId = c.Id
		WHERE	rt.Deleted = 0
				AND rt.CreatedDate >= @FromDate
				AND rt.CreatedDate < @ToDate
				AND rt.Level4Id = '057861DD-1336-453D-A3E8-B79DD7478AA1'	-- Tư vấn đặc điểm
				AND dfd.Name = N'bancheo'
				AND dfv.Value = N'Có'
		GROUP BY rt.OwnerId, rt.SourceChannel, c.Nation, c.CustomerClass, rt.CreatedDate

		INSERT rpt.PreprocessUserCrossSaleTicket_Log (Id, PreprocessDate, IsSuccess) VALUES (NEWID(), @ProcessDate, 1)

		COMMIT TRANSACTION trans_UserCrossSaleTicket

    END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION trans_UserCrossSaleTicket
		SET @ErrorMessage = (SELECT ERROR_MESSAGE())
		INSERT rpt.PreprocessUserCrossSaleTicket_Log (Id, PreprocessDate, IsSuccess) VALUES (NEWID(), @ProcessDate, 1)
	END CATCH


	-- 3. Thống kê ĐỔI ĐIỂM
	--==============================================================================
	BEGIN TRANSACTION  trans_UserPointExchangeTicket
	BEGIN TRY

		DELETE rpt.UserPointExchangeTicket WHERE TicketCreatedDate >= @FromDate AND TicketCreatedDate < @ToDate

		INSERT rpt.UserPointExchangeTicket
		        ( 
					Id ,
					WorkingShift ,
					ActivityUserId ,
					TicketCreatedDate ,
					SourceChannel ,
					Nation ,
					CustomerClass ,
					ActivityTicketCount
		        )
		SELECT  NEWID(),
				CASE
					WHEN CONVERT(TIME(7), rt.CreatedDate) >= '07:00:00' AND CONVERT(TIME(7), rt.CreatedDate) <= '21:00:00' THEN N'7h đến 21h'
					ELSE N'21h đến 7h'
				END WorkingShift,
				rt.OwnerId,
				CONVERT(DATE, rt.CreatedDate) CreatedDate,
				rt.SourceChannel,
				CASE WHEN c.Nation IS NULL THEN N'VN' ELSE N'Other' END Nation,
				ISNULL(c.CustomerClass,0) CustomerClass,
				COUNT(*) TotalCount
		FROM	dbo.RequestTicket rt WITH(NOLOCK)
				JOIN dbo.Customer c WITH(NOLOCK) ON rt.CustomerId = c.Id
		WHERE	rt.Deleted = 0
				AND rt.CreatedDate >= @FromDate
				AND rt.CreatedDate < @ToDate
				AND rt.Level2Id = 'D3493CEA-7039-4AAE-B1EB-2C0428EE2B49'	-- Đổi điểm
		GROUP BY rt.OwnerId, rt.SourceChannel, c.Nation, c.CustomerClass, rt.CreatedDate

		INSERT rpt.PreprocessUserPointExchangeTicket_Log (Id, PreprocessDate, IsSuccess) VALUES (NEWID(), @ProcessDate, 1)

		COMMIT TRANSACTION trans_UserPointExchangeTicket

    END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION trans_UserPointExchangeTicket
		SET @ErrorMessage = (SELECT ERROR_MESSAGE())
		INSERT rpt.PreprocessUserPointExchangeTicket_Log (Id, PreprocessDate, IsSuccess) VALUES (NEWID(), @ProcessDate, 1)
	END CATCH

END
GO