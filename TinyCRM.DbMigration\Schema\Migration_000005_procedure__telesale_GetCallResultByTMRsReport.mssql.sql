
/****** Object:  StoredProcedure [telesale].[GetCallResultByTMRsReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[GetCallResultByTMRsReport]

	@FromDate		DATETIME,
	@ToDate			DATETIME,
	@TeamId			UNIQUEIDENTIFIER

AS
BEGIN

	DECLARE @PivotColumns NVARCHAR(MAX)
	SELECT	@PivotColumns = COALESCE(@PivotColumns + ', ', '') + '[' + CAST(up.Id AS NVARCHAR(50)) + ']'
	FROM	dbo.UserProfiles up
			JOIN dbo.UserInRoles uir ON uir.UserId = up.Id
	WHERE	up.OrganizationId = @TeamId

	DECLARE @WhereString NVARCHAR(MAX) = '(@FromDate IS NULL OR cc.CreatedDate >= @FromDate) AND (@ToDate IS NULL OR cc.CreatedDate < DATEADD(DAY, 1, @ToDate)) AND (@TeamId IS NULL OR up.OrganizationId = @TeamId) '

	DECLARE @ExecuteString NVARCHAR(MAX) = 
	'
		SELECT	cr.Code CallResultCode, cr.VietnameseDescription, cr.IsConnected, cr.IsContacted, cr.IsConsulted, cr.IsInterested, cr.IsEligible, cr.IsAppointmentMade, tempTable.*
		FROM	dbo.CallResult cr
				LEFT JOIN
				(
					SELECT	*
					FROM	(
								SELECT	cr.Id CallResultId, cr.Code, cc.CreatedBy AgentId
								FROM	dbo.ContactCall cc
										JOIN dbo.CallResult cr ON cr.Id = cc.CallResultId
										JOIN dbo.UserProfiles up ON up.Id = cc.CreatedBy
								WHERE	' + @WhereString + '
							) temp
							PIVOT
							(
								COUNT(Code) FOR AgentId IN (' + @PivotColumns + ')
							) tempPivot
				) tempTable ON tempTable.CallResultId = cr.Id
		UNION
		SELECT	''13'' CallResultCode, N''Cuộc hẹn được thiết lập (Hẹn mới)'' VietnameseDescription, NULL IsConnected, NULL IsContacted, NULL IsConsulted, NULL IsInterested, NULL IsEligible, NULL IsAppointmentMade, ''00000000-0000-0000-0000-000000000000'' CallResultId, tempPivot.*
		FROM	(
					SELECT	''00000000-0000-0000-0000-000000000000'' CallResultId, cc.CreatedBy AgentId
					FROM	dbo.Appointment cc
							JOIN dbo.UserProfiles up ON up.Id = cc.CreatedBy
					WHERE	cc.Status <> 4 AND cc.Status <> 5 AND cc.PreviousAppointmentId IS NULL
							AND ' + @WhereString + '
				) temp
				PIVOT
				(
					COUNT(CallResultId) FOR AgentId IN (' + @PivotColumns + ')
				) tempPivot
		ORDER BY cr.Code
	'

	PRINT @ExecuteString

	EXEC sp_executesql	@ExecuteString, 
						N'@FromDate DATETIME, @ToDate DATETIME,	@TeamId UNIQUEIDENTIFIER', 
						@FromDate, @ToDate, @TeamId

END
GO