
/****** Object:  StoredProcedure [telesale].[Prospect_ExecuteTeamDistribution]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[Prospect_ExecuteTeamDistribution]

	@CampaignId						UNIQUEIDENTIFIER,
	@TeamRegionContactNumbers		dbo.CoupleIdNumber READONLY,
	@DistributedDate				DATETIME,
	@DistributeNew					BIT,
	@Reprospect						BIT,
	@DistributedUserId				UNIQUEIDENTIFIER

AS
BEGIN

	--=============================================================================
	-- Descripton: Create new ProspectAssignment in case Prospect has no Current assignment yet.
	-- Note: This region should be removed after other logic make sure to always create a pa for any prospect
	--=============================================================================
	SELECT	NEWID() ProspectAssignmentId, p.Id ProspectId
	INTO	#NewAssignments
	FROM	dbo.Prospect p
			LEFT JOIN dbo.ProspectAssignment pa ON pa.Id = p.CurrentAssignmentId
	WHERE	pa.Id IS NULL
			--p.CampaignId = @CampaignId AND  ---- Commented because This line cause additional index, but not neccessary in case of Prudential logic
			
	INSERT	dbo.ProspectAssignment
        ( 
			Id,
			ProspectId,
			Status,
			CreatedReason,
			CreatedDate,
			CreatedBy,
			DistributedMoment
        )
	SELECT	ProspectAssignmentId, ProspectId, 1, 1, GETDATE(), @DistributedUserId, GETDATE()
	FROM	#NewAssignments

	UPDATE	p
	SET		CurrentAssignmentId = na.ProspectAssignmentId
	FROM	dbo.Prospect p
			JOIN #NewAssignments na ON na.ProspectId = p.Id
	
	--=============================================================================
	--END::Create new ProspectAssignment for orphan Prospect
	--=============================================================================

	
	DECLARE @TeamAssignNumbersTable TABLE (
		iIndex		INT PRIMARY KEY,
		TeamId		UNIQUEIDENTIFIER,
		RegionId		UNIQUEIDENTIFIER,
		Number		INT
	)

	INSERT @TeamAssignNumbersTable(iIndex, TeamId, RegionId, Number)
	SELECT	ROW_NUMBER() OVER (ORDER BY SortOrder), Id1, Id2, Number
	FROM	@TeamRegionContactNumbers
	WHERE Number>0

	-- Prepare region list to work on 
	DECLARE @RegionTable TABLE (
		iIndex		INT PRIMARY KEY,
		RegionId		UNIQUEIDENTIFIER,
		Number		INT
	)
	INSERT @RegionTable(iIndex, RegionId,Number)
	SELECT ROW_NUMBER() OVER (ORDER BY (SELECT 0)), RegionId,SUM(Number)
	FROM   @TeamAssignNumbersTable
	GROUP BY RegionId
	
	--SELECT * FROM @RegionTable

	
	-- 2.Iterate through each region
	DECLARE @indexRegion INT=1, @totalRegions INT = (SELECT COUNT(*) FROM @RegionTable)
	
	WHILE (@indexRegion <= @totalRegions)
	BEGIN
		DECLARE @workingRegionId UNIQUEIDENTIFIER
		DECLARE @Number int

		SELECT TOP 1 @workingRegionId  = RegionId,@Number=Number FROM @RegionTable WHERE iIndex=@indexRegion
		
			SELECT	TOP (@Number) ROW_NUMBER() OVER (ORDER BY (SELECT 0) ) RowNumber, ProspectId, CurrentProspectAssignmentId ProspectAssignmentId
			,c.CurrentProspectAssignmentId AS AssignedTeamid,AssignedAgentId
			INTO	#TempTable
			FROM	vProspect_CurrentAssignmentStatus c
					LEFT JOIN dbo.Province pv ON pv.Id = c.ProvinceId
			WHERE	(pv.RegionId=@workingRegionId OR (pv.RegionId IS NULL and '00000000-0000-0000-0000-000000000000'= @workingRegionId))
					AND (c.AssignedTeamId IS NULL)
					AND
					(
					(@DistributeNew=1 AND ProspectStatus < 3 AND HotListGroupId IS NULL) -- Phân bổ mới + lấy về rổ
					OR (@Reprospect=1 AND (ProspectStatus = 3 AND DATEDIFF(DAY, GETDATE(), ReprospectDate) <= 0)) -- Phân bổ đám được reprospect
					)

			
			--- 3.Distribute contact in the region to individual teams
		
			--SELECT xx.TeamId,xx.Number,d.ProspectAssignmentId --,xx.RowEnd-xx.Number+1 RowStart,xx.RowEnd RowEnd
			UPDATE d SET AssignedTeamid = xx.TeamId
			FROM (
				SELECT TeamId,SUM(Number) OVER (ORDER BY iIndex) RowEnd,Number
				FROM @TeamAssignNumbersTable 
				WHERE RegionId=@workingRegionId
			) xx
			JOIN #TempTable d ON d.RowNumber>=(xx.RowEnd-xx.Number+1) AND d.RowNumber <=xx.rowEnd

			
			
			BEGIN TRY
				BEGIN TRANSACTION
					UPDATE	pa
					SET		AssignedTeamId = temp.AssignedTeamid, AssignedTeamDate = @DistributedDate, CreatedReason = 1, DistributedMoment = GETDATE()
					FROM	dbo.ProspectAssignment pa
							JOIN #TempTable temp ON temp.ProspectAssignmentId = pa.Id

					UPDATE	p
					SET		p.Status = 2, ReprospectDate=NULL
					FROM	dbo.Prospect p
							JOIN #TempTable temp ON temp.ProspectId = p.Id
			COMMIT
			END TRY
			BEGIN CATCH
			 ROLLBACK
			END CATCH
			

			DROP TABLE #TempTable
			SET @indexRegion=@indexRegion+1
    end

	
END
GO