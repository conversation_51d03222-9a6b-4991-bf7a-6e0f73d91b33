
/****** Object:  StoredProcedure [testUtil].[InboundPage_TicketList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROC [testUtil].[InboundPage_TicketList] 
      @inbound_phone varchar(50)
as
BEGIN

IF OBJECT_ID('testUtil.DateTimeData') IS NOT NULL DROP TABLE testUtil.DateTimeData

CREATE TABLE testUtil.DateTimeData (id INT PRIMARY KEY,dates DATETIME)

--- 8 trường hợp CreatedDate sẽ được thay đổi thành text tùy vào cấu hình trong BusinessSetting
INSERT INTO testUtil.DateTimeData(id, dates) VALUES(1,GETDATE())
INSERT INTO testUtil.DateTimeData(id, dates) VALUES(2,DATEADD(MINUTE,-15,GETDATE()))  --- thay đổi giá trị phút (cách về 15 phút trước)
INSERT INTO testUtil.DateTimeData(id, dates) VALUES(3,DATEADD(MINUTE,-45,GETDATE()))  --- thay đổi giá trị phút
INSERT INTO testUtil.DateTimeData(id, dates) VALUES(4,DATEADD(HOUR,-1,GETDATE()))     --- thay đổi giá trị giờ  (cách về 1 giờ trước)
INSERT INTO testUtil.DateTimeData(id, dates) VALUES(5,DATEADD(HOUR,-6,GETDATE()))     --- thay đổi giá trị giờ
INSERT INTO testUtil.DateTimeData(id, dates) VALUES(6,GETDATE()-1)                             --- thay đổi giá trị ngày (cách về 1 ngày trước)
INSERT INTO testUtil.DateTimeData(id, dates) VALUES(7,GETDATE()-4)                             --- thay đổi giá trị ngày
INSERT INTO testUtil.DateTimeData(id, dates) VALUES(8,GETDATE()-7)                             --- thay đổi giá trị ngày

DECLARE @inbound_cus_id UNIQUEIDENTIFIER

SELECT @inbound_cus_id = Id FROM dbo.Customer WHERE Phone1 = @inbound_phone OR Phone2 = @inbound_phone OR Code=@inbound_phone

UPDATE TOP(8) dbo.RequestTicket SET CustomerId = @inbound_cus_id, IsoCode=Code WHERE IsoCode IS NULL

UPDATE rt SET CreatedDate = dt.dates
FROM ( SELECT ROW_NUMBER() OVER (ORDER BY Id) STT,* FROM  dbo.RequestTicket WHERE CustomerId = @inbound_cus_id) rt
JOIN ( SELECT * FROM testUtil.DateTimeData) dt ON dt.id = rt.STT
 SELECT rt.CreatedDate, DATEDIFF(MINUTE,GETDATE(),rt.CreatedDate) CalculatedMinutes FROM  dbo.RequestTicket rt WHERE rt.CustomerId = @inbound_cus_id

 
--EXEC sampledata.GenerateCustomer  @NumOfCustomer = 1002,
--                                  @CoreCustomerPercentage = 100

--EXEC testUtil.CustomerImport	  @GenerateExcelFileData = 8, -- bit
--								  @GenerateExcelFileData_FillAllInfoFields = 0, -- bit
--								  @936ExistingCases_ExecuteUpdate = 1,
--								  @936ExistingCases_ExecuteUpdate_FillAllInfoFields = 1,
--								  @936ExistingCases_Show=0, -- bit
--								  @KeepTempTablesAfterFinish=1

--EXEC sampledata.GenTicket_SALData @ct_creatTimeFrom = '2019-04-15 04:17:38', -- datetime
--                                  @ct_createTimeTo = '2019-04-19 04:17:38',  -- datetime
--                                  @replicaNumberOwnershipModel14 = 0,        -- int
--                                  @exactNumberOfTicketsToCreate = 2000,         -- int
--                                  @ProcessDueMinute = 0,                     -- int
--                                  @AcceptDueMinute = 0,                      -- int
--                                  @ct_rate2 = 10,                             -- int
--                                  @ct_rate3 = 40,                             -- int
--                                  @ct_rate3_lateAccept = 0,                  -- int
--                                  @ct_rate3_lateProcess = 0,                 -- int
--                                  @ct_rate4_lateAccept = 0,                  -- int
--                                  @ct_rate4_lateProcess = 0,                 -- int 
--                                  @cleanAllRelatedTable = 1,				-- bit
--                                  @debugLogOn = NULL                         -- bit

--EXEC sampledata.InitSurveyCampaign @clearAllRelevantTable = 1 -- bit

--

--EXEC testUtil.InboundPage
--EXEC testUtil.InboundPage_TicketList 'CO1649'


end
GO