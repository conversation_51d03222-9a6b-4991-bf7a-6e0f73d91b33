
/****** Object:  StoredProcedure [sampledata].[ScriptAccessesBusinessPermission]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [sampledata].[ScriptAccessesBusinessPermission]
AS
BEGIN
	---Generate AccessBusinessPermission (have Accesses)
	SELECT 'DELETE FROM dbo.AccessBusinessPermission WHERE Id = ''' + CAST(aba.Id AS VARCHAR(100)) + '''
	INSERT INTO dbo.AccessBusinessPermission 
			(	Id, 
				AccessId, 
				BusinessPermissionId, 
				CreatedDate, 
				CreatedBy, 
				ModifiedDate, 
				ModifiedBy, 
				Deleted, 
				DeletedDate, 
				DeletedBy
			)
	SELECT ''' + CAST(aba.Id AS VARCHAR(100)) + ''',
		   a.Id,	
		   ''' + CAST(aba.BusinessPermissionId AS VARCHAR(100)) + ''',
		   ' + IIF(aba.CreatedDate IS NULL, 'NULL' ,'''' + CAST(aba.CreatedDate AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.CreatedBy IS NULL, 'NULL' ,'''' + CAST(aba.CreatedBy AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.ModifiedDate IS NULL, 'NULL' ,'''' + CAST(aba.ModifiedDate AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.ModifiedBy IS NULL, 'NULL' ,'''' + CAST(aba.ModifiedBy AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.Deleted IS NULL, 'NULL' ,CAST(aba.Deleted AS VARCHAR(100))) + ',
		   ' + IIF(aba.DeletedDate IS NULL, 'NULL' ,'''' + CAST(aba.DeletedDate AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.DeletedBy IS NULL, 'NULL' ,'''' + CAST(aba.DeletedBy AS VARCHAR(100)) + '''') + '
	FROM dbo.Accesses a WHERE a.ActionName = ''' + aba.ActionName + ''' AND a.ControllerName = ''' + aba.ControllerName + ''' AND a.Method = ' + CAST(aba.Method AS VARCHAR(50))
	FROM
	(
	SELECT ab.*, a.ActionName, a.ControllerName, a.Method FROM dbo.AccessBusinessPermission ab
	JOIN dbo.Accesses a ON ab.AccessId = a.Id
	) aba
	
	UNION
	
	---Generate AccessBusinessPermission (ConstCodePermission)
	SELECT 'DELETE FROM dbo.AccessBusinessPermission WHERE Id = ''' + CAST(aba.Id AS VARCHAR(100)) + '''
	INSERT INTO dbo.AccessBusinessPermission 
			(	Id, 
				AccessId, 
				BusinessPermissionId, 
				CreatedDate, 
				CreatedBy, 
				ModifiedDate, 
				ModifiedBy, 
				Deleted, 
				DeletedDate, 
				DeletedBy
			)
	VALUES (''' + CAST(aba.Id AS VARCHAR(100)) + ''',
		   ''' + CAST(aba.AccessId AS VARCHAR(100)) + ''',	
		   ''' + CAST(aba.BusinessPermissionId AS VARCHAR(100)) + ''',
		   ' + IIF(aba.CreatedDate IS NULL, 'NULL' ,'''' + CAST(aba.CreatedDate AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.CreatedBy IS NULL, 'NULL' ,'''' + CAST(aba.CreatedBy AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.ModifiedDate IS NULL, 'NULL' ,'''' + CAST(aba.ModifiedDate AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.ModifiedBy IS NULL, 'NULL' ,'''' + CAST(aba.ModifiedBy AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.Deleted IS NULL, 'NULL' ,CAST(aba.Deleted AS VARCHAR(100))) + ',
		   ' + IIF(aba.DeletedDate IS NULL, 'NULL' ,'''' + CAST(aba.DeletedDate AS VARCHAR(100)) + '''') + ',
		   ' + IIF(aba.DeletedBy IS NULL, 'NULL' ,'''' + CAST(aba.DeletedBy AS VARCHAR(100)) + '''') + '
			)'
	FROM
	(
	SELECT ab.* FROM dbo.AccessBusinessPermission ab
	LEFT JOIN dbo.Accesses a ON ab.AccessId = a.Id WHERE a.Id IS NULL
	) aba
END
GO