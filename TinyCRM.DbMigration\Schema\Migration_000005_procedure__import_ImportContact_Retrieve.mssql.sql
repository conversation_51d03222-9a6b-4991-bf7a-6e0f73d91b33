
/****** Object:  StoredProcedure [import].[ImportContact_Retrieve]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[ImportContact_Retrieve]
	@Manager UNIQUEIDENTIFIER,
	@ImportSessionId uniqueidentifier,
	@CampaignId UNIQUEIDENTIFIER,
	@Status INT,
	@TMR UNIQUEIDENTIFIER
AS
BEGIN
	
	DECLARE @EmptyIds IdList;
	DECLARE @FollowStatuses IntList;
	INSERT INTO @FollowStatuses(Number) VALUES (@Status)

	EXEC telesale.SearchContactAndRegainBySaleSupport 
		@ImportSessionId = @ImportSessionId,
	    @CampaignId = @CampaignId,
	    @PageIndex = 0,
	    @PageSize = 99999,
	    @DataSource = NULL,
	    @FollowStatuses = @FollowStatuses,
	    @PhoneNumber = NULL,
	    @FullName = NULL,
	    @TMR = @TMR,
	    @TeamTMR = NULL,
	    @AssignedTeamDate = NULL,
	    @AssignedTeamDateTo = NULL,
	    @AssignedAgentDate = NULL,
	    @AssignedAgentDateTo = NULL,
	    @NotCallFromDate = NULL,
	    @ContactSelectedList = @EmptyIds,
	    @Manager = @Manager,
	    @Export = 0,
	    @ProvinceList = @EmptyIds,
	    @RegainCount = 99999,
	    @CallResultIds = @EmptyIds,
	    @RetrieveToOrgId = NULL
END
GO