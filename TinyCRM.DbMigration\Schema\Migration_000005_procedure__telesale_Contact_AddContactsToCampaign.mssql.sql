
/****** Object:  StoredProcedure [telesale].[Contact_AddContactsToCampaign]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[Contact_AddContactsToCampaign]
    @CampaignId          UNIQUEIDENTIFIER,
	@ReferCampaignId     UNIQUEIDENTIFIER = NULL, --//Add contact vào campaign từ 1 campaign khác
    @ProvinceId          UNIQUEIDENTIFIER,
    @DataSource          NVARCHAR(500),
    @IncomeFrom          INT,
    @IncomeTo            INT,
    @MaritalStatus       INT,
    @Gender              INT,
    @Job                 NVARCHAR(MAX),
    @SelectedCampaignId  UNIQUEIDENTIFIER,
    @ExcludeCampaignIds  IdList READONLY,
    @ResultCodeIds       IdList READONLY,
    @ImportSessionId     UNIQUEIDENTIFIER,
    @ContactSelectedList IdList READONLY,
    @CreatedBy           UNIQUEIDENTIFIER,
    @AssignedNumber      INT
AS
BEGIN

    DECLARE @ContactListId AS TABLE (Id UNIQUEIDENTIFIER NULL);
    IF (SELECT COUNT(*)FROM @ContactSelectedList) > 0
    BEGIN
        INSERT INTO @ContactListId
        SELECT s.Id
        FROM @ContactSelectedList s
    END;
    ELSE
    BEGIN
        INSERT INTO @ContactListId
        EXEC telesale.Contact_SearchContactsToAddToCampaign @CampaignId = @CampaignId,
                                                            @ProvinceId = @ProvinceId,
                                                            @DataSource = @DataSource,
                                                            @Job = @Job,
                                                            @IncomeFrom = @IncomeFrom,
                                                            @IncomeTo = @IncomeTo,
                                                            @MaritalStatus = @MaritalStatus,
                                                            @Gender = @Gender,
                                                            @SelectedCampaignId = @SelectedCampaignId,
                                                            @ExcludeCampaignIds = @ExcludeCampaignIds,
                                                            @ResultCodeIds = @ResultCodeIds,
                                                            @ImportSessionId = @ImportSessionId,
                                                            @Limit = @AssignedNumber,
                                                            @StartRow = NULL,
                                                            @EndRow = NULL;
    END;
    SELECT Id, NEWID() ProspectId, NEWID() ProspectAssignmentId INTO #ContactListId FROM @ContactListId;

    DECLARE @CampaignType INT = (SELECT CampaignType FROM dbo.Campaign WHERE Id = @CampaignId);

    DECLARE @ReferenceResultType NVARCHAR(256);
    IF @CampaignType = 1
    BEGIN
        SET @ReferenceResultType = N'ContactCall';
    END;
    ELSE BEGIN
SET @ReferenceResultType = N'SurveyFeedback';
    END;

    --WAITFOR DELAY '00:00:10'
    BEGIN TRY

        BEGIN TRANSACTION;

        INSERT dbo.Prospect (Id, CustomerId, CampaignId, CreatedBy, Notes, CreatedDate, IsHot, Status, NextCallbackDate, BackToCommonBasketDate, ReprospectDate, CurrentAssignmentId, HotListGroupId,
                             LastCallId, CallResultId, AlertCallbackDate, ReferenceResultType, ReferCampaignId)
        SELECT ProspectId Id,
               Id CustomerId,
               @CampaignId CampaignId,
               @CreatedBy CreatedBy,
               NULL Notes,
               GETDATE() CreatedDate,
               0 IsHot,
               1 Status,
               NULL NextCallbackDate,
               NULL BackToCommonBasketDate,
               NULL ReprospectDate,
               ProspectAssignmentId CurrentAssignmentId,
               NULL HotListGroupId,
               NULL LastCallId,
               NULL CallResultId,
               NULL AlertCallbackDate,
               @ReferenceResultType,
			   @ReferCampaignId
        FROM #ContactListId;

        -- Insert a Prospect Assignment to be ready
        INSERT INTO dbo.ProspectAssignment (Id, ProspectId, CampaignId, CustomerId, AssignedTeamId, AssignedTeamDate, AssignedAgentId, AssignedAgentDate, UnassignedDate, UnassignedBy, Status,
                                            CreatedDate, CreatedBy, ClosedReason, FieldSaleAppointmentId, FieldSaleFeedbackDate, ReminderId, Ignore15DaysRule, CreatedReason,
                                            PreviousProspectAssingmentId, EnterCampaignBasketReason, DistributedMoment, ReferenceResultType)
        SELECT ProspectAssignmentId Id,
               ProspectId ProspectId,
               @CampaignId CampaignId,
               Id CustomerId,
               NULL AssignedTeamId,
               NULL AssignedTeamDate,
               NULL AssignedAgentId,
               NULL AssignedAgentDate,
               NULL UnassignedDate,
               NULL UnassignedBy,
               1 Status,
               GETDATE() CreatedDate,
               @CreatedBy CreatedBy,
               NULL ClosedReason,
               NULL FieldSaleAppointmentId,
               NULL FieldSaleFeedbackDate,
               NULL ReminderId,
               0 Ignore15DaysRule,
               1 CreatedReason,
               NULL PreviousProspectAssingmentId,
               1 EnterCampaignBasketReason,
               NULL DistributedMoment,
               @ReferenceResultType
        FROM #ContactListId;

        COMMIT TRANSACTION;

    END TRY
    BEGIN CATCH
        ROLLBACK;
        DECLARE @error NVARCHAR(MAX);
        SELECT @error = ERROR_MESSAGE();
        RAISERROR(15600, -1, -1, @error);
    END CATCH;

END;
GO