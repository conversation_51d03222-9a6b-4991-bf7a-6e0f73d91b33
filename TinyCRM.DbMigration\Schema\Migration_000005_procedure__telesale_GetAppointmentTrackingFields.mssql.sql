
/****** Object:  StoredProcedure [telesale].[GetAppointmentTrackingFields]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE  PROCEDURE [telesale].[GetAppointmentTrackingFields]
    @AppointmentId UNIQUEIDENTIFIER, @LeadAssignmentId UNIQUEIDENTIFIER
AS
BEGIN
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    CREATE TABLE #AppointmentStatus (Id NVARCHAR(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS, Message NVARCHAR(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS);
    CREATE TABLE #LeadAssignmentStatus (Id NVARCHAR(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS, Message NVARCHAR(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS);
    CREATE TABLE #SeenStatus (Id NVARCHAR(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS, Message NVARCHAR(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS);
    INSERT INTO #AppointmentStatus (Id, Message)
    VALUES (N'New', N'Chờ kết quả'),
           (N'NotMeet', N'Chưa gặp'),
           (N'Meet', N'Đã gặp'),
           (N'Canceled', N'Bị hủy'),
           (N'Returned', N'Trả về');
    INSERT INTO #LeadAssignmentStatus (Id, Message)
    VALUES (N'New', N'Chưa phân bổ'),
           (N'Processing', N'Team DMO đang xử lý'),
           (N'Done', N'Hoàn thành');
    INSERT INTO #SeenStatus (Id, Message) VALUES (N'True', N'Đã xem'), (N'False', N'Chưa xem');

    SELECT en.Id AuditId,
           en.KeyValue,
           en.[Action],
           en.ModifiedBy,
           ISNULL(up.FullName, u.UserName) ModifiedByUserName,
           en.ModifiedDate,
           ef.Id FieldId,
           ef.MemberName,
           CASE WHEN ef.MemberName = 'ProvinceId' THEN N'Tỉnh thành'
                WHEN ef.MemberName = 'MeetAddress' THEN N'Địa chỉ'
                WHEN ef.MemberName = 'DistrictId' THEN N'Quận huyện'
                WHEN ef.MemberName = 'WardId' THEN N'Phường xã'
                WHEN ef.MemberName = 'MeetDate' THEN N'Ngày hẹn'
                WHEN ef.MemberName = 'AppointmentResultCodeId' THEN N'Kết quả hẹn'
                WHEN ef.MemberName = 'RetryCount' THEN N'Lần gọi'
                WHEN ef.MemberName = 'Notes' THEN 'Ghi chú'
                WHEN ef.MemberName = 'Status'
                     AND en.TableName = 'dbo.Appointment' THEN N'Trạng thái gặp'
                WHEN ef.MemberName = 'Status'
                     AND en.TableName = 'dbo.LeadAssignment' THEN N'Trạng thái xử lý hẹn'
                WHEN ef.MemberName = 'ProductId' THEN N'Sản phẩm'
                WHEN ef.MemberName = 'Notes' THEN N'Ghi chú'
                WHEN ef.MemberName = 'ProductBudget' THEN N'Mệnh giá'
                WHEN ef.MemberName = 'FeedbackNotes' THEN N'Phản hồi'
                WHEN ef.MemberName = 'SeenByFieldSale' THEN N'Trạng thái DMO theo dõi'
                WHEN ef.MemberName = 'AssignedFieldSaleTeamId' THEN N'Nhóm DMO được phân bổ'
                WHEN ef.MemberName = 'AssignedFieldSaleTeamDate' THEN N'Ngày phân bổ nhóm DMO'
                WHEN ef.MemberName = 'AssignedFieldSaleId' THEN N'DMO được phân bổ'
                WHEN ef.MemberName = 'AssignedFieldSaleDate' THEN N'Ngày phân bổ DMO' ELSE ef.MemberName END MemberDescription,
           CASE WHEN ef.MemberName = 'ProvinceId' THEN ISNULL(pv1.Name, '')
                WHEN ef.MemberName = 'DistrictId' THEN ISNULL(di1.Name, '')
                WHEN ef.MemberName = 'WardId' THEN ISNULL(wa1.Name, '')
                WHEN ef.MemberName = 'AppointmentResultCodeId' THEN ISNULL(aprc1.ResultCode + ' - ' + aprc1.DescriptionVn, '')
                WHEN ef.MemberName = 'Status'
                     AND en.TableName = 'dbo.Appointment' THEN ISNULL(as1.Message, '')
                WHEN ef.MemberName = 'Status'
                     AND en.TableName = 'dbo.LeadAssignment' THEN ISNULL(las1.Message, '')
                WHEN ef.MemberName = 'ProductId' THEN ISNULL(prd1.Name, '')
                WHEN ef.MemberName = 'SeenByFieldSale' THEN ISNULL(see1.Message, '')
                WHEN ef.MemberName = 'AssignedFieldSaleTeamId' THEN ISNULL(dteam1.Name, '')
                WHEN ef.MemberName = 'AssignedFieldSaleId' THEN ISNULL(team1.FullName, '')ELSE ISNULL(ef.OldValue, '')END OldValue,
           CASE WHEN ef.MemberName = 'ProvinceId' THEN ISNULL(pv2.Name, '')
                WHEN ef.MemberName = 'DistrictId' THEN ISNULL(di2.Name, '')
                WHEN ef.MemberName = 'WardId' THEN ISNULL(wa2.Name, '')
                WHEN ef.MemberName = 'AppointmentResultCodeId' THEN ISNULL(aprc2.ResultCode + ' - ' + aprc2.DescriptionVn, '')
                WHEN ef.MemberName = 'Status'
                     AND en.TableName = 'dbo.Appointment' THEN ISNULL(as2.Message, '')
                WHEN ef.MemberName = 'Status'
                     AND en.TableName = 'dbo.LeadAssignment' THEN ISNULL(las2.Message, '')
                WHEN ef.MemberName = 'ProductId' THEN ISNULL(prd2.Name, '')
                WHEN ef.MemberName = 'SeenByFieldSale' THEN ISNULL(see2.Message, '')
                WHEN ef.MemberName = 'AssignedFieldSaleTeamId' THEN ISNULL(dteam2.Name, '')
                WHEN ef.MemberName = 'AssignedFieldSaleId' THEN ISNULL(team2.FullName, '')ELSE ISNULL(ef.NewValue, '')END NewValue
    FROM AuditEntityChange en
    INNER JOIN AuditFieldChange ef ON en.Id = ef.AuditId
    INNER JOIN aspnet_Users u ON u.UserId = en.ModifiedBy
    INNER JOIN UserProfiles up ON u.UserId = up.Id
    LEFT JOIN dbo.Geolocation pv1 ON pv1.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT)
    LEFT JOIN dbo.Geolocation di1 ON di1.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT)
    LEFT JOIN dbo.Geolocation wa1 ON wa1.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT)
    LEFT JOIN dbo.Geolocation pv2 ON pv2.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT)
    LEFT JOIN dbo.Geolocation di2 ON di2.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT)
    LEFT JOIN dbo.Geolocation wa2 ON wa2.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT)
    LEFT JOIN dbo.AppointmentResultCode aprc1 ON aprc1.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT)
    LEFT JOIN dbo.AppointmentResultCode aprc2 ON aprc2.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT)
    LEFT JOIN #AppointmentStatus as1 ON as1.Id = ef.OldValue
                                        AND en.TableName = 'dbo.Appointment'
                                        AND ef.MemberName = 'Status'
    LEFT JOIN #AppointmentStatus as2 ON as2.Id = ef.NewValue
                                        AND en.TableName = 'dbo.Appointment'
                                        AND ef.MemberName = 'Status'
    LEFT JOIN #LeadAssignmentStatus las1 ON las1.Id = ef.OldValue
                                            AND en.TableName = 'dbo.LeadAssignment'
                                            AND ef.MemberName = 'Status'
    LEFT JOIN #LeadAssignmentStatus las2 ON las2.Id = ef.NewValue
                                            AND en.TableName = 'dbo.LeadAssignment'
                                            AND ef.MemberName = 'Status'
    LEFT JOIN dbo.Product prd1 ON prd1.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT)
    LEFT JOIN dbo.Product prd2 ON prd2.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT)
    LEFT JOIN #SeenStatus see1 ON see1.Id = ef.OldValue
    LEFT JOIN #SeenStatus see2 ON see2.Id = ef.NewValue
    LEFT JOIN dbo.Organization dteam1 ON dteam1.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT)
    LEFT JOIN dbo.Organization dteam2 ON dteam2.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT)
    LEFT JOIN dbo.UserProfiles team1 ON team1.Id = dbo.ConvertToGuid(ef.OldValue, DEFAULT)
    LEFT JOIN dbo.UserProfiles team2 ON team2.Id = dbo.ConvertToGuid(ef.NewValue, DEFAULT)
    WHERE en.KeyValue IN (@AppointmentId, @LeadAssignmentId)
    ORDER BY en.ModifiedDate DESC;
END;
GO