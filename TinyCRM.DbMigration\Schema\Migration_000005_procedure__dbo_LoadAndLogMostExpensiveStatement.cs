using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using System.Reflection;
using Webaby.Data;

namespace TinyCRM.DbMigration.Schema
{
    [DbContext(typeof(MigrationDbContext))]
    [Migration(nameof(Migration_000005_procedure__dbo_LoadAndLogMostExpensiveStatement))]
    internal class Migration_000005_procedure__dbo_LoadAndLogMostExpensiveStatement : RawMigration
    {
        protected override Assembly ResourceAssembly => Assembly.GetExecutingAssembly();
    }
}