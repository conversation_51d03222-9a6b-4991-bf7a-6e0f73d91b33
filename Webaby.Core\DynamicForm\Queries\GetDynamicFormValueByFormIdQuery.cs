﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFormValueByFormIdQuery : QueryBase<DynamicFormValueData>
    {
        public Guid FormId { get; set; }
    }

    internal class GetDynamicFormValueByFormIdQueryHandler : QueryHandlerBase<GetDynamicFormValueByFormIdQuery, DynamicFormValueData>
    {
        public GetDynamicFormValueByFormIdQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
            : base(entitySet, repository, text, mapper)
        { }

        public override async Task<QueryResult<DynamicFormValueData>> ExecuteAsync(GetDynamicFormValueByFormIdQuery query)
        {
            var entities = await EntitySet.Get<DynamicFormValueEntity>().Where(dfv => dfv.DynamicFormId == query.FormId).ToListAsync();
            var mapped = entities.Select(x => Mapper.Map<DynamicFormValueData>(x));
            return QueryResult.Create(mapped);
        }
    }
}
