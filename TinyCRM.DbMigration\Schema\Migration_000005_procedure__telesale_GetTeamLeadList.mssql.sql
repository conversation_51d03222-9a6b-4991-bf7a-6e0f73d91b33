
/****** Object:  StoredProcedure [telesale].[GetTeamLeadList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



--ALTER PROCEDURE [telesale].[GetTeamLeadList]
CREATE PROCEDURE [telesale].[GetTeamLeadList]

	@UserLogin UNIQUEIDENTIFIER,
	@TeamId			UNIQUEIDENTIFIER,
	@IsAssigned		BIT,
	@MeetDate		DATETIME

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	DECLARE @TeamList IdList;
	 IF (SELECT r.Name FROM dbo.UserInRoles uir
	 JOIN dbo.Roles r ON r.Id=uir.RoleId
	 WHERE uir.UserId=@UserLogin)='DMO Manager'
		IF @TeamId IS NULL
			INSERT INTO @TeamList SELECT o.Id FROM dbo.UserProfiles up JOIN dbo.Organization o ON up.OrganizationId=o.ParentId WHERE up.Id=@UserLogin;
		ELSE
			INSERT INTO @TeamList SELECT @TeamId Id;
	ELSE
		INSERT INTO @TeamList SELECT @TeamId Id;

	SELECT	ap.FeedbackNotes, arc.ResultCode AppointmentResultCode, arc.DescriptionVn ResultCodeVN, la.Id, ap.Id AppointmentId, l.UserDefinedFormatCode, c.Id ContactId, c.FullName,c.DOB ClientDob,c.MaritalStatus ClientMaritalStatus,c.Job ClientJob, c.DataSource,
			pr.ProductName, ap.MeetDate, FORMAT(ap.MeetDate, 'HH\H') MeetHour, ap.MeetAddress, ap.ProductBudget,
			d.DistrictName, pv.ProvinceName, ap.Notes, ISNULL(tmrPro.FullName,tmrUser.UserName) TMR, ISNULL(sugDMOUp.FullName,sugDMOU.UserName) SuggestedDMOName, 
			la.SuggestedFieldSaleReason, dmoU.UserId FieldSaleId, ISNULL(dmoUp.FullName,dmoU.UserId) DMOName, ap.Status AppointmentStatus,
			p.IsHot, p.HotListGroupId
	FROM	dbo.LeadAssignment la
			JOIN dbo.Lead l ON la.Id = l.CurrentLeadAssignmentId
			JOIN dbo.ProspectAssignment pa ON pa.Id = l.CreatedByProspectAssignmentId
			JOIN dbo.aspnet_Users tmrUser ON tmrUser.UserId = pa.AssignedAgentId
			JOIN dbo.UserProfiles tmrPro ON tmrPro.Id = tmrUser.UserId
			JOIN dbo.Prospect p ON p.Id = pa.ProspectId
			JOIN dbo.Contact c ON c.Id = p.ContactId
			LEFT JOIN dbo.Product pr ON pr.Id = l.ProductId
			JOIN dbo.Appointment ap ON ap.Id = la.WaitingAppointmentId
			LEFT JOIN dbo.District d ON d.Id = ap.DistrictId
			JOIN dbo.Province pv ON pv.Id = ap.ProvinceId
			LEFT JOIN dbo.aspnet_Users dmoU ON dmoU.UserId = la.AssignedFieldSaleId
			LEFT JOIN dbo.UserProfiles dmoUp ON dmoUp.Id = dmoU.UserId
			LEFT JOIN dbo.aspnet_Users sugDMOU ON sugDMOU.UserId = la.SuggestedFieldSaleId
			LEFT JOIN dbo.UserProfiles sugDMOUp ON sugDMOUp.Id = sugDMOU.UserId
			LEFT JOIN dbo.AppointmentResultCode arc ON arc.Id=ap.AppointmentResultCodeId
	WHERE	(la.AssignedFieldSaleTeamId IN (SELECT Id FROM @TeamList))
			AND ((@IsAssigned = 0 AND la.AssignedFieldSaleId IS NULL) OR (@IsAssigned = 1 AND la.AssignedFieldSaleId IS NOT NULL))
			AND CAST(ap.MeetDate AS DATE) = CAST(@MeetDate AS DATE)
			AND ap.Status <> 4 AND ap.Status <> 5 -- Hẹn bị hủy và hẹn bị trả
	ORDER BY ap.MeetDate
******/
END
GO