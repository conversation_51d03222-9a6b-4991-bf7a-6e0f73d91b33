
/****** Object:  StoredProcedure [telesale].[CreateContactRelationshipFromAppointmentRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[CreateContactRelationshipFromAppointmentRaw]
	@UserId UNIQUEIDENTIFIER,
	@SessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	INSERT INTO dbo.ContactRelationship
	        ( Id ,
	          FullName ,
	          DOB ,
	          Phone ,
	          Address ,
	          OwnContactId ,
	          RelatedContactId ,
	          RelationshipType ,
	          CreatedDate ,
	          CreatedBy ,
	          MaritalStatus ,
	          Gender
	        )
	SELECT  
			  NEWID() Id,
			  ar.SpouseName FullName,
			  ar.SpouseDOB DOB,
			  N'' Phone,
			  N'' Address,
			  NULL OwnContactId,
			  ar.ContactId RelatedContactId,
			  3 RelationshipType,
			  GETDATE() CreatedDate,
			  tmr.Id CreatedBy,
			  2 MaritalStatus,
			  0 Gender
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles tmr ON ar.CodeTMR=tmr.AgentCode
	LEFT JOIN dbo.ContactRelationship cr ON (cr.RelatedContactId=ar.ContactId AND cr.RelationshipType=3 AND cr.FullName=ar.SpouseName)
	WHERE ar.IsInvalid=0 AND ar.IsDupApp=0 AND ar.SpouseName IS NOT NULL AND ar.ImportSessionId=@SessionId AND cr.Id IS NULL

	INSERT INTO dbo.ContactRelationship
	        ( Id ,
	          FullName ,
	          DOB ,
	          Phone ,
	          Address ,
	          OwnContactId ,
	          RelatedContactId ,
	          RelationshipType ,
	          CreatedDate ,
	          CreatedBy ,
	          MaritalStatus ,
	          Gender
	        )
	SELECT  
			  NEWID() Id,
			  ar.Child1Name FullName,
			  ar.Child1DOB DOB,
			  N'' Phone,
			  N'' Address,
			  NULL OwnContactId,
			  ar.ContactId RelatedContactId,
			  4 RelationshipType,
			  GETDATE() CreatedDate,
			  tmr.Id CreatedBy,
			  0 MaritalStatus,
			  0 Gender
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles tmr ON ar.CodeTMR=tmr.AgentCode
	LEFT JOIN dbo.ContactRelationship cr ON (cr.RelatedContactId=ar.ContactId AND cr.RelationshipType=4 AND (
	ar.Child1Name=cr.FullName AND ar.Child1DOB=cr.DOB
	))
	WHERE ar.IsInvalid=0 AND ar.IsDupApp=0 AND ar.Child1Name IS NOT NULL AND ar.ImportSessionId=@SessionId
	AND cr.Id IS NULL

	INSERT INTO dbo.ContactRelationship
	        ( Id ,
	          FullName ,
	          DOB ,
	          Phone ,
	          Address ,
	          OwnContactId ,
	          RelatedContactId ,
	          RelationshipType ,
	          CreatedDate ,
	          CreatedBy ,
	          MaritalStatus ,
	          Gender
	        )
	SELECT  
			  NEWID() Id,
			  ar.Child2Name FullName,
			  ar.Child2DOB DOB,
			  N'' Phone,
			  N'' Address,
			  NULL OwnContactId,
			  ar.ContactId RelatedContactId,
			  4 RelationshipType,
			  GETDATE() CreatedDate,
			  tmr.Id CreatedBy,
			  0 MaritalStatus,
			  0 Gender
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles tmr ON ar.CodeTMR=tmr.AgentCode
	LEFT JOIN dbo.ContactRelationship cr ON (cr.RelatedContactId=ar.ContactId AND cr.RelationshipType=4 AND (
	ar.Child2Name=cr.FullName AND ar.Child2DOB=cr.DOB
	))
	WHERE ar.IsInvalid=0 AND ar.IsDupApp=0 AND ar.Child2Name IS NOT NULL AND ar.ImportSessionId=@SessionId
	AND cr.Id IS NULL

	INSERT INTO dbo.ContactRelationship
	        ( Id ,
	          FullName ,
	          DOB ,
	          Phone ,
	          Address ,
	          OwnContactId ,
	          RelatedContactId ,
	          RelationshipType ,
	          CreatedDate ,
	          CreatedBy ,
	          MaritalStatus ,
	          Gender
	        )
	SELECT  
			  NEWID() Id,
			  ar.Child3Name FullName,
			  ar.Child3DOB DOB,
			  N'' Phone,
			  N'' Address,
			  NULL OwnContactId,
			  ar.ContactId RelatedContactId,
			  4 RelationshipType,
			  GETDATE() CreatedDate,
			  tmr.Id CreatedBy,
			  0 MaritalStatus,
			  0 Gender
	FROM dbo.AppointmentRaw ar
	LEFT JOIN dbo.UserProfiles tmr ON ar.CodeTMR=tmr.AgentCode
	LEFT JOIN dbo.ContactRelationship cr ON (cr.RelatedContactId=ar.ContactId AND cr.RelationshipType=4 AND (
	ar.Child3Name=cr.FullName AND ar.Child3DOB=cr.DOB
	))
	WHERE ar.IsInvalid=0 AND ar.IsDupApp=0 AND ar.Child3Name IS NOT NULL AND ar.ImportSessionId=@SessionId
	AND cr.Id IS NULL
END
GO