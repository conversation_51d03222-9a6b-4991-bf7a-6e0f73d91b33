
/****** Object:  StoredProcedure [telesale].[DataCallResultSumary]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [telesale].[DataCallResultSumary]

	@CampaignId			UNIQUEIDENTIFIER,
	@OrganizationId		UNIQUEIDENTIFIER,
	@AssignedAgentId	UNIQUEIDENTIFIER

AS
BEGIN

	WITH cte(Id) AS
	(
		SELECT	Id
		FROM	dbo.Organization org
		WHERE	org.Id = @OrganizationId
		UNION ALL
		SELECT	org.Id
		FROM	dbo.Organization org
				JOIN cte temp ON temp.Id = org.ParentId
	)
	SELECT	cte.Id
	INTO	#TempOrgs
	FROM	cte

	SELECT	Id, Code, EnglishDescription, VietnameseDescription
	INTO	#TempCallResult
	FROM	dbo.CallResult
	WHERE	ResultCodeSuiteId = (SELECT ResultCodeSuiteId FROM dbo.Campaign WHERE Id = @CampaignId)
	UNION
    SELECT	'00000000-0000-0000-0000-000000000000' Id, '' Code, N'Not call' EnglishDescription, N'Chưa gọi' VietnameseDescription

	-- Nếu là Chiến dịch Auto Dial
	IF (SELECT CampaignType FROM dbo.Campaign WHERE Id = @CampaignId) = 3 BEGIN
		INSERT #TempCallResult (Id, Code, EnglishDescription, VietnameseDescription)
		SELECT	Id, Code, EnglishDescription, VietnameseDescription
		FROM	dbo.CallResult
		WHERE	Id = '00000000-1111-2222-3333-444444444444'
	END

	DECLARE @WhereString NVARCHAR(MAX) = N'WHERE	pa.IsNotCurrent = 0 '
	IF @CampaignId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' pa.CampaignId = @CampaignId '
	END
	IF @AssignedAgentId IS NOT NULL
	BEGIN
		SET @WhereString = @WhereString + IIF(@WhereString='', N' WHERE	', ' AND ') + ' pa.AssignedAgentId = @AssignedAgentId '
	END

	DECLARE @SelectString NVARCHAR(MAX) = N'
	SELECT	cr.Code CallResultCode, cr.EnglishDescription, cr.VietnameseDescription,
			temp.OrganizationName, temp.TelesaleName,
			ISNULL(temp.TotalCount,0) TotalCount
	'

	DECLARE @FromString NVARCHAR(MAX) = N'
	FROM	#TempCallResult cr
			LEFT JOIN
			(
				SELECT	ISNULL(cr.Id, ''00000000-0000-0000-0000-000000000000'') CallResultId, org.Name OrganizationName, up.FullName TelesaleName, COUNT(*) TotalCount
				FROM	dbo.ProspectAssignment pa
						LEFT JOIN dbo.Organization org ON org.Id = pa.AssignedTeamId
						LEFT JOIN dbo.UserProfiles up ON up.Id = pa.AssignedAgentId
						LEFT JOIN dbo.CallResult cr ON cr.Id = pa.CallResultId '
						+ IIF (@OrganizationId IS NULL, N'', N'JOIN #TempOrgs tempOrg ON tempOrg.Id = org.Id')  + N'
				' + @WhereString + N'
				GROUP BY cr.Id, org.Name, up.FullName
			) temp ON cr.Id = temp.CallResultId
	'

	DECLARE @ParamDefs NVARCHAR(MAX) = N'
		@CampaignId			UNIQUEIDENTIFIER,
		@OrganizationId		UNIQUEIDENTIFIER,
		@AssignedAgentId	UNIQUEIDENTIFIER
	'

	DECLARE @FullExecString NVARCHAR(MAX) = @SelectString + @FromString

	EXECUTE sp_executesql @FullExecString, @ParamDefs,
											@CampaignId = @CampaignId,
											@OrganizationId = @OrganizationId,
											@AssignedAgentId = @AssignedAgentId

END
GO