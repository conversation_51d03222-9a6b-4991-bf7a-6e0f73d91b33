
/****** Object:  StoredProcedure [telesale].[Contact_GetHotListMasterDuplication]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

--ALTER PROCEDURE [telesale].[Contact_GetHotListMasterDuplication]
CREATE PROCEDURE [telesale].[Contact_GetHotListMasterDuplication]

	@ImportSessionId		UNIQUEIDENTIFIER,
	@LastCallBefore			DATETIME,
	@StartRow				INT,
	@EndRow					INT

AS
BEGIN

	WITH tempTable AS
    (
		SELECT	sc.*, cc.CreatedDate LastCallDate,
				ROW_NUMBER() OVER (ORDER BY c.ContactID) RowNumber
		FROM	dbo.StagingContact sc
				JOIN dbo.Contact c ON c.Phone = sc.Phone
				LEFT JOIN dbo.ContactCall cc ON cc.Id = c.LastCallId
		WHERE	sc.ImportSessionId = @ImportSessionId
				AND (@LastCallBefore IS NULL OR cc.Id IS NULL OR cc.CreatedDate <= @LastCallBefore)
	)
	SELECT	*, (SELECT COUNT(*) FROM tempTable) TotalCount
	FROM	tempTable
	WHERE	tempTable.RowNumber BETWEEN @StartRow AND @EndRow

END
GO