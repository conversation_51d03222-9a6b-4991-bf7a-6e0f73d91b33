
/****** Object:  StoredProcedure [import].[Import_ProspectAssigmentFromTMRData]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[Import_ProspectAssigmentFromTMRData]
AS
BEGIN

BEGIN TRANSACTION
BEGIN try
	INSERT INTO dbo.Contact
	        ( Id ,
	          FullName ,
	          Address ,
	          ProvinceId ,
	          Phone ,
	          DOB ,
	          DataSource ,
	          CreatedDate ,
	          Job ,
	          Inactive ,
	          Notes ,
	          CreatedBy ,
	          Income ,
	          MaritalStatus ,
	          Gender ,
	          Status
	        )
	SELECT tc.ContactId,ISNULL(tc.ContactName,''),tc.Address,tc.ProvinceId,tc.PhoneNumber,tc.DOB,tc.DataSource,GETDATE(),tc.Career,1,SUBSTRING(tc.Note,1,1999),(SELECT userid FROM dbo.aspnet_Users WHERE UserName='manager'),tc.Income
	,0,0,1
	FROM import.TMRContact_Valid tc
	WHERE IsNewContact=1
		
	INSERT INTO dbo.Prospect
	        ( Id ,
	          ContactId ,
	          CampaignId ,
	          ReferenceObjectId ,
	          CreatedBy ,
	          Notes ,
	          CreatedDate ,
	          IsHot ,
	          Status ,
	          NextCallbackDate ,
	          BackToCommonBasketDate ,
	          ReprospectDate ,
	          CurrentAssignmentId
	        )
	SELECT ProspectId,ContactId,(SELECT TOP 1 Id FROM dbo.Campaigns),NULL,(SELECT userid FROM dbo.aspnet_Users WHERE UserName='manager'),Note,GETDATE(),0,1,NULL,DATEADD(DAY,90,GETDATE()),NULL,ProspectAssignmentId
	FROM import.TMRContact_Valid 
	WHERE IsNewProspect=1

	INSERT INTO dbo.ProspectAssignment
	        ( Id ,
	          ProspectId ,
	          AssignedTeamId ,
	          AssignedTeamDate ,
	          AssignedAgentId ,
	          AssignedAgentDate ,
	          Status ,
	          CreatedDate ,
	          CreatedBy ,
	          Ignore15DaysRule
	        )
	SELECT ProspectAssignmentId,ProspectId,up.OrganizationId,GETDATE(),tc.AssignedUserId,GETDATE(),1,GETDATE(),(SELECT userid FROM dbo.aspnet_Users WHERE UserName='manager'),1
	FROM import.TMRContact_Valid tc
	JOIN dbo.UserProfiles up ON up.Id=tc.AssignedUserId 
END TRY

BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK
END CATCH
    IF @@TRANCOUNT > 0 COMMIT


END
GO