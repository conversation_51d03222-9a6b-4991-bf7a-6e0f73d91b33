
/****** Object:  StoredProcedure [import].[ImportContact_GetScanDataSummaryReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[ImportContact_GetScanDataSummaryReport]
	@ImportSessionId UNIQUEIDENTIFIER,
	@ErrorCode NVARCHAR(MAX),
	@WarningCode INT
AS
BEGIN
	SELECT Source, 
		   FullName,
		   PhoneNo,
		   IIF(PhoneValid IS NULL AND PhoneNo IS NOT NULL, N'Số điện thoại không đúng định dạng', NULL) PhoneValid, 
		   Email,
		   IIF(EmailValid IS NULL AND Email IS NOT NULL, N'Email không đúng định dạng', NULL) EmailValid, 
		   Areas,
		   IIF(ProvinceValid IS NULL AND Areas IS NOT NULL, N'Không tìm thấy tỉnh thành tương ứng', NULL) ProvinceValid,
		   DOB,
		   IIF(DOB IS NOT NULL AND DOBValid IS NULL, N'Ngày sinh không đúng định dạng', NULL) DOBValid,
		   Gender,
		   IIF(Gender IS NOT NULL AND GenderValid IS NULL, N'Không tìm thấy giới tính tương ứng', NULL) GenderValid,
		   MaritalStatus,
		   IIF(MaritalStatus IS NOT NULL AND MaritalStatusValid IS NULL, N'Không tìm thấy tình trạng hôn nhân tương ứng', NULL) MaritalStatusValid,
		   Note,
		   STUFF(CONCAT('; ' + IIF(ErrorCode = 'InvalidData', N'Dữ liệu không hợp lệ', NULL),
		    '; ' + IIF(ErrorCode = 'DuplicateInternal',N'Trùng lặp trong file import', NULL),
			'; ' + IIF(ErrorCode = 'NewContact',N'Mới - chưa có trong kho', NULL),
			'; ' + IIF(ErrorCode = 'NotInCampaign',N'Đã có trong kho nhưng chưa vào chiến dịch', NULL),
			'; ' + IIF(ErrorCode = 'Campaign_NotAssigned',N'Trong chiến dịch chưa phân công', NULL),
			'; ' + IIF(ErrorCode = 'Team_NotCall',N'Trong chiến dịch, rổ team, dữ liệu mới', NULL),
			'; ' + IIF(ErrorCode = 'Agent_NotCall',N'Trong chiến dịch, rổ agent, dữ liệu mới', NULL),
			'; ' + IIF(ErrorCode = 'Agent_SimpleResult',N'Trong chiến dịch, rổ agent, đã được xử lý sơ bộ', NULL),
			'; ' + IIF(ErrorCode = 'Team_SimpleResult',N'Trong chiến dịch, rổ team, đã được xử lý sơ bộ', NULL),
			'; ' + IIF(ErrorCode = 'Agent_DoneLost',N'Trong chiến dịch, rổ agent, đã hoàn thành - thất bại', NULL),
			'; ' + IIF(ErrorCode = 'Team_DoneLost',N'Trong chiến dịch, rổ team, đã hoàn thành - thất bại', NULL),
			'; ' + IIF(ErrorCode = 'Agent_Appointment',N'Trong chiến dịch, rổ agent, có hẹn ưu tiên', NULL),
			'; ' + IIF(ErrorCode = 'Team_Appointment',N'Trong chiến dịch, rổ team, có hẹn ưu tiên', NULL),
			'; ' + IIF(ErrorCode = 'Agent_DoneSuccess',N'Trong chiến dịch, rổ agent, đã hoàn thành - thành công', NULL),
			'; ' + IIF(ErrorCode = 'Team_DoneSuccess',N'Trong chiến dịch, rổ team, đã hoàn thành - thành công', NULL),
			'; ' + IIF(ErrorCode = 'ExistInBasket',N'Đã có trong kho', NULL)),1,2,'') ErrorMessage
	FROM import.ContactRaw
	WHERE ImportSessionId = @ImportSessionId 
		AND (@ErrorCode IS NULL OR ErrorCode = @ErrorCode)
		AND (@WarningCode IS NULL OR (ISNULL(WarningCode,0) & @WarningCode <> 0))
END
GO