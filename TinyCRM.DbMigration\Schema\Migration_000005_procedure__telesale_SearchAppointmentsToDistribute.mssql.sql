
/****** Object:  StoredProcedure [telesale].[SearchAppointmentsToDistribute]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[SearchAppointmentsToDistribute]

	@FromDate			DATETIME,
	@ToDate				DATETIME,
	@LeadID				VARCHAR(MAX),
	@TeamList			dbo.IdList READONLY,
	@TeamListTMR		dbo.IdList READONLY,
	@AssignedStatus		BIT,
	@IncludeReturned	BIT,
	@DataSource			NVARCHAR(MAX) = NULL

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	DECLARE @TotalTeam INT = (SELECT COUNT(*) FROM @TeamList)
	DECLARE @TotalTeamTMR INT = (SELECT COUNT(*) FROM @TeamListTMR)

	SELECT	a.LeadAssignmentId, a.Id AppointmentId, pa.Id ProspectAssignmentId, la.AssignedFieldSaleTeamId, la.AssignedFieldSaleId, aprc.ResultCode AppointmentResultCode
			, (CASE WHEN a.PreviousAppointmentId IS NOT NULL THEN '(HC)' ELSE '' END) HC, l.LeadCode LeadID, dmot.OrganizationName AssignedFieldSaleTeamName, dmo_up.FullName AssignedFieldSaleName, dmo_up.AgentCode DMOCode, tmrt.OrganizationName TMRTeamName
			, tmr_ur.FullName TMRNAME, tmr_ur.AgentCode TMRCode, FORMAT(a.CreatedDate,'dd.MM.yyyy') CallDate, FORMAT(a.MeetDate,'dd.MM.yyyy') MeetDate, FORMAT(a.MeetDate,'HH\Hmm') MeetTime, a.MeetAddress
			, c.DataSource [SOURCE], c.FullName CLIENTNAME, FORMAT(c.DOB,'dd.MM.yyyy') DOB
			, a.ProductBudget, c.Job, c.Address, ISNULL(w.WardName,'') WardName, ISNULL(dt.DistrictName,'') DistrictName, ISNULL(pv.ProvinceName,'') ProvinceName
			, c.Phone, c.Phone2, c.Phone3, a.Notes, a.ProvinceId, pr.ProductName, la.SuggestedFieldSaleId, ISNULL(upDMOProfile.FullName,sugDMOUser.UserName) SuggestedFieldSaleName, la.SuggestedFieldSaleTeamId, sugOrg.OrganizationName SuggestedFieldSaleTeamName, la.SuggestedFieldSaleReason
			, p.IsHot, p.HotListGroupId, pv.Id ProvinceId
			, COUNT(a.ProvinceId) OVER (PARTITION BY a.ProvinceId) ProvinceCount
	FROM	dbo.Appointment a
			JOIN dbo.LeadAssignment la ON a.Id=la.WaitingAppointmentId
			JOIN dbo.Lead l ON l.CurrentLeadAssignmentId=la.Id
			JOIN dbo.UserProfiles createdProfile ON createdProfile.Id = a.CreatedBy
			LEFT JOIN @TeamList tempList ON tempList.Id = la.AssignedFieldSaleTeamId
			JOIN dbo.ProspectAssignment pa ON l.CreatedByProspectAssignmentId=pa.Id
			LEFT JOIN @TeamListTMR tempListTmr ON tempListTmr.Id = pa.AssignedTeamId
			LEFT JOIN dbo.Organization dmot ON la.AssignedFieldSaleTeamId = dmot.Id
			LEFT JOIN dbo.UserProfiles dmo_up ON dmo_up.Id=la.AssignedFieldSaleId -- DMO
			LEFT JOIN dbo.UserProfiles tmr_ur ON tmr_ur.Id=pa.AssignedAgentId -- TMR
			LEFT JOIN dbo.Organization tmrt ON pa.AssignedTeamId = tmrt.Id
			JOIN dbo.Prospect p ON pa.ProspectId =p.Id
			JOIN dbo.Contact c ON c.Id=p.ContactId
			JOIN dbo.Province pv ON pv.Id=a.ProvinceId
			LEFT JOIN dbo.District dt ON dt.Id=a.DistrictId
			LEFT JOIN dbo.Ward w ON w.Id=a.WardId
			LEFT JOIN dbo.Product pr ON pr.Id = a.ProductId --ko có product khi import từ excel
			LEFT JOIN dbo.aspnet_Users sugDMOUser ON sugDMOUser.UserId = la.SuggestedFieldSaleId
			LEFT JOIN dbo.UserProfiles upDMOProfile ON upDMOProfile.Id = sugDMOUser.UserId
			LEFT JOIN dbo.Organization sugOrg ON sugOrg.Id = la.SuggestedFieldSaleTeamId
			LEFT JOIN dbo.AppointmentResultCode aprc ON aprc.Id = a.AppointmentResultCodeId
	WHERE	(@FromDate='2000-01-01' OR (
			a.MeetDate >= @FromDate
			AND a.MeetDate < @ToDate
			))
			AND (ISNULL(@LeadID,'')='' OR l.LeadCode=@LeadID)
			AND a.Status <> 4 -- Hủy hẹn
			AND ((@IncludeReturned = 0 AND a.Status <> 5) OR (@IncludeReturned = 1 AND a.Status = 5))
			AND (@AssignedStatus IS NULL OR @AssignedStatus = 0 OR @TotalTeam = 0 OR tempList.Id IS NOT NULL)
			AND (@AssignedStatus IS NULL OR ((@AssignedStatus = 0 AND la.AssignedFieldSaleTeamId IS NULL) OR (@AssignedStatus = 1 AND la.AssignedFieldSaleTeamId IS NOT NULL)))
			AND (@DataSource IS NULL OR @DataSource='' OR c.DataSource=@DataSource)
			AND (@TotalTeamTMR=0 OR tempListTmr.Id IS NOT NULL)
	ORDER BY pv.ProvinceName, a.MeetDate
******/
END
GO