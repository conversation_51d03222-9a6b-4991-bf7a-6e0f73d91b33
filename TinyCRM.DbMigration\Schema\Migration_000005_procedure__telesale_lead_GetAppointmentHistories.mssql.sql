
/****** Object:  StoredProcedure [telesale].[lead_GetAppointmentHistories]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


--ALTER PROCEDURE [telesale].[lead_GetAppointmentHistories]
CREATE PROCEDURE [telesale].[lead_GetAppointmentHistories]

	@ProspectAssignmentId		UNIQUEIDENTIFIER

AS
BEGIN

	SELECT	ap.*, l.LeadCode, apr.Id AppointmentResultCodeId, apr.ResultCode, apr.DescriptionVn AppointmentResultDescription, apr.LeadStatus, ap.CreatedBy, ISNULL(up.FullName,u.UserName) CreatedByName,
			ISNULL(dmoPr.FullName,dmoU.UserName) FieldSaleName, dmoPr.AgentCode FieldSaleCode, dmoPr.PhoneNumber FieldSalePhone,
			fileSaleLeader.FieldSaleLeaderName, fileSaleLeader.FieldSaleLeaderPhone,
			p.Name ProductName, ap.ProductBudget, la.AssignedFieldSaleTeamId, la.AssignedFieldSaleId, la.SuggestedFieldSaleId, la.SuggestedFieldSaleTeamId, la.SuggestedFieldSaleReason
	FROM	dbo.Appointment ap
			JOIN dbo.aspnet_Users u ON u.UserId = ap.CreatedBy
			JOIN dbo.UserProfiles up ON up.Id = u.UserId
			LEFT JOIN dbo.Product p ON p.Id = ap.ProductId
			JOIN dbo.LeadAssignment la ON la.Id = ap.LeadAssignmentId
			JOIN dbo.Lead l ON l.Id = la.LeadId
			LEFT JOIN dbo.aspnet_Users dmoU ON dmoU.UserId = la.AssignedFieldSaleId
			LEFT JOIN dbo.UserProfiles dmoPr ON dmoPr.Id = dmoU.UserId
			LEFT JOIN dbo.AppointmentResultCode apr ON apr.Id = ap.AppointmentResultCodeId
			LEFT JOIN
			(
				SELECT	org.Id FieldSaleTeamId, ISNULL(up.FullName,u.UserName) FieldSaleLeaderName, up.PhoneNumber FieldSaleLeaderPhone
				FROM	dbo.Organization org
						JOIN dbo.UserProfiles up ON up.OrganizationId = org.Id
						JOIN dbo.aspnet_Users u ON u.UserId = up.Id
						JOIN dbo.UserInRoles uir ON uir.UserId = u.UserId
				WHERE	--org.OrganizationType = 'DMO Team' AND
						uir.RoleId = '94E5A159-A404-4D5C-9D6D-BE0C49ADC34C'
			) fileSaleLeader ON fileSaleLeader.FieldSaleTeamId = la.AssignedFieldSaleTeamId
	WHERE	l.CreatedByProspectAssignmentId = @ProspectAssignmentId
	ORDER BY ap.CreatedDate DESC

END
GO