﻿using Microsoft.AspNetCore.Http;
using System.Collections.Specialized;
using System.Data;
using TinyCRM.AppServices.RequestTicket.Dto;
using TinyCRM.Enums;
using Webaby.Core.DynamicForm.Queries;

namespace TinyCRM.AppServices.RequestTicket
{
    public interface IRequestTicketAppService
    {
        CreateEditRequestTicketResult CreateRequestTicket(RequestTicketCreateEditArguments arguments, IEnumerable<InsertProductRequestTicketItem> productRequestTickets, NameValueCollection form, IFormFileCollection files, bool checkExistedRequestTicket = true, bool auditDynamicForm = true, bool ignoreTransaction = false);

        CreateEditRequestTicketResult CreateRequestTicket(Guid customerId, Guid serviceTypeId, Guid ownerId, Guid currentUserId, List<DynamicFieldValueInfo> dynamicFieldList, bool syncLinkedField = true, bool checkExistedRequestTicket = true, bool auditDynamicForm = true);

        CreateEditRequestTicketResult EditRequestTicket(RequestTicketCreateEditArguments arguments, IEnumerable<InsertProductRequestTicketItem> productRequestTickets, NameValueCollection form, IFormFileCollection files);

        FinishRequestTicketResult FinishRequestTicket(FinishRequestTicketArguments arguments, bool ignoreBusinessPermission = false, bool forcedFinish = false);

        SearchRequestTicketResult SearchRequestTicket(SearchRequestTicketArguments arguments, int pageIndex, int pageSize, bool includeDynamicForm = false);

        DataSet SearchRequestTicket(SearchRequestTicketArguments arguments, int pageIndex, int pageSize);

        byte[] ExportSearchRequestTicketResultToImport(SearchRequestTicketArguments arguments, string templateName, bool? isDisplay);

        byte[] ExportSearchRequestTicketResultByDynamicFieldList(SearchRequestTicketArguments arguments, string templateName, bool? isDisplay);

        bool IsRequiredBusinessResult(RequestTicketStatus Status, Guid ServiceTypeId, Guid? TicketBusinessResultId);

        DataSet SearchRequestTicketResultHasDisplay(SearchRequestTicketArguments arguments, bool isDisplay);

        void BatchCreateRequestTicketList(Guid serviceTypeId, Guid ownerId, Guid currentUserId, List<Guid> customerIdList, bool trackAuditChange, bool triggerNoticase, bool triggerPhase);

        bool CheckUpdateDefinedTable(Guid serviceTypeId, Guid ticketId);

        DeleteRequestTicketResultDto DeleteRequestTicket(Guid requestTicketId, bool ignoreBusinessPermission = false, bool forcedFinish = false);

        string CreateFirstTasks(Guid requestTicketId);
    }
}