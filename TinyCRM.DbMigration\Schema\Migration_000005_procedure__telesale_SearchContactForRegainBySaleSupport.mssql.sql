
/****** Object:  StoredProcedure [telesale].[SearchContactForRegainBySaleSupport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [telesale].[SearchContactForRegainBySaleSupport]

	@ImportSessionId UNIQUEIDENTIFIER,
	@CampaignId UNIQUEIDENTIFIER,
	@IsNotCurrent BIT = 0,
	@FollowStatuses dbo.IntList READONLY,
    @TMR UNIQUEIDENTIFIER = NULL,
	@TeamTMR UNIQUEIDENTIFIER = NULL,
	@AssignedTeamDate DATETIME = NULL,
	@AssignedTeamDateTo DATETIME = NULL,
	@AssignedAgentDate DATETIME = NULL,
	@AssignedAgentDateTo DATETIME = NULL,
	@DataSource NVARCHAR(MAX) = NULL,
	@PhoneNumber VARCHAR(500) = NULL,
	@FullName NVARCHAR(MAX) = NULL,
	@ProvinceList IdList READONLY,
	@CallResultIds IdList READONLY,
	@NotCallFromDate DATETIME = NULL,
	@PageIndex INT = 0,
	@PageSize INT = 25,
	@ExportExcel BIT = 0,
	@TobeRegain BIT = 0 -- gọi từ web => luôn luôn set = 0

AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @AssignedTeamDateBegin DATETIME = DATEFROMPARTS(YEAR(@AssignedTeamDate), MONTH(@AssignedTeamDate), DAY(@AssignedTeamDate));
	DECLARE @AssignedTeamDateEnd DATETIME = DATETIMEFROMPARTS(YEAR(@AssignedTeamDateTo), MONTH(@AssignedTeamDateTo), DAY(@AssignedTeamDateTo), 23, 59, 59, 0);

	DECLARE @AssignedAgentDateBegin DATETIME = DATEFROMPARTS(YEAR(@AssignedAgentDate), MONTH(@AssignedAgentDate), DAY(@AssignedAgentDate));
	DECLARE @AssignedAgentDateEnd DATETIME = DATETIMEFROMPARTS(YEAR(@AssignedAgentDateTo), MONTH(@AssignedAgentDateTo), DAY(@AssignedAgentDateTo), 23, 59, 59, 0);

	SELECT * INTO #provinceList FROM @ProvinceList
	SELECT * INTO #CallResultList FROM @CallResultIds
	SELECT * INTO #FollowUpStatusList FROM @FollowStatuses

	DECLARE @cteQuery NVARCHAR(MAX) =
	N'
		SELECT	ROW_NUMBER() OVER (ORDER BY (SELECT 1)) rn,
				c.Id CustomerId, pa.ProspectId, pa.Id ProspectAssignmentId, pa.AssignedTeamDate
		FROM	dbo.ProspectAssignment pa
				JOIN dbo.Customer c ON c.Id=pa.CustomerId
		'+IIF(@ImportSessionId IS NULL, '', ' JOIN import.ContactRaw_Log crl on c.Id=crl.CustomerId AND crl.ImportSessionId='''+CAST(@ImportSessionId AS NVARCHAR(MAX))+''' ')+'
		'+IIF((SELECT COUNT(*) FROM @ProvinceList)=0, '',
			'
				JOIN #provinceList prv on prv.Id=dbo.GuidEmpty() AND c.ProvinceId IS NULL OR prv.Id=c.ProvinceId
			')+'
		'+IIF((SELECT COUNT(*) FROM @CallResultIds)=0, '',
			'
				JOIN #CallResultList crList ON crList.Id = pa.CallResultId 
			')+'
		'+IIF((SELECT COUNT(*) FROM @FollowStatuses)=0, '',
			'
				LEFT JOIN dbo.CallResult lcr ON lcr.Id=pa.CallResultId
				JOIN #FollowUpStatusList fwUpSttList ON fwUpSttList.Number = lcr.FollowUpStatus OR (fwUpSttList.Number=0 AND pa.CallResultId IS NULL)
			')+'
		'+IIF(@NotCallFromDate IS NOT NULL, ' LEFT JOIN dbo.ContactCall lcc ON lcc.Id=pa.LastCallId ', '')+'

		WHERE	pa.CampaignId='''+CAST(@CampaignId AS NVARCHAR(MAX))+''' 
				AND pa.AssignedTeamId IS NOT NULL 
				AND pa.IsNotCurrent = @IsNotCurrent 
		'+IIF(@NotCallFromDate IS NOT NULL, ' AND (lcc.Id IS NULL OR lcc.CreatedDate <= '''+CAST(@NotCallFromDate AS NVARCHAR(MAX))+''') ', '')+'
		'+IIF(@TeamTMR IS NOT NULL, ' AND pa.AssignedTeamId='''+CAST(@TeamTMR AS NVARCHAR(MAX))+''' ', '')+'
		'+CASE ISNULL(@TMR, '*************-2222-2222-222222222222')
			WHEN '*************-2222-2222-222222222222' THEN ''
			WHEN '11111111-1111-1111-1111-111111111111' THEN ' AND pa.AssignedAgentId IS NULL '
			WHEN '00000000-0000-0000-0000-000000000000' THEN ' AND pa.AssignedAgentId IS NOT NULL '
			ELSE ' AND pa.AssignedAgentId='''+CAST(@TMR AS NVARCHAR(MAX))+''' '
		  END+'
		'+IIF(@PhoneNumber IS NOT NULL AND @PhoneNumber<>'', ' AND '''+CAST(@PhoneNumber AS NVARCHAR(MAX))+''' IN (c.Phone, c.Phone2, c.Phone3) ', '')+'
		'+IIF(@DataSource IS NOT NULL AND @DataSource<>'', ' AND c.DataSource='''+CAST(@DataSource AS NVARCHAR(MAX))+''' ', '')+'
		'+IIF(ISNULL(@FullName,'') <> '', ' AND CONTAINS(c.FullName, @FullName) ', '')+'
		'+IIF(@AssignedTeamDate IS NOT NULL, ' AND pa.AssignedTeamDate >= '''+CAST(@AssignedTeamDateBegin AS NVARCHAR(MAX))+''' ', '')+'
		'+IIF(@AssignedTeamDateTo IS NOT NULL, ' AND pa.AssignedTeamDate <= '''+CAST(@AssignedTeamDateEnd AS NVARCHAR(MAX))+''' ', '')+'
		'+IIF(@AssignedAgentDate IS NOT NULL, ' AND pa.AssignedAgentDate >= '''+CAST(@AssignedAgentDateBegin AS NVARCHAR(MAX))+''' ', '')+'
		'+IIF(@AssignedAgentDateTo IS NOT NULL, ' AND pa.AssignedAgentDate <= '''+CAST(@AssignedAgentDateEnd AS NVARCHAR(MAX))+''' ', '')+'
	';

	DECLARE @fullQuery NVARCHAR(MAX) =
	N'
		WITH cte AS
		(
			'+@cteQuery+'
		)
		SELECT
		1 rn_l,
		'+IIF(@TobeRegain=1, N' cte.ProspectAssignmentId, cte.AssignedTeamDate ',
			N'
				(SELECT COUNT(cte.ProspectAssignmentId) FROM cte) TotalCount,
				cte.ProspectAssignmentId,
				c.Code [REF ID],
				c.DataSource [FORM ID],
				'''' SUBMITTED,
				'''' [FORM NAME],
				'''' Nationality,
				c.Name FULL_NAME,
				c.Email EMAIL_ADDRESS,
				'''' MOBILE_NUM,
				'''' [Name of Employer],
				c.Income [Monthly Income],
				FORMAT(c.DOB, ''dd/MM/yyyy'') [Date of birth],
				c.Notes [Gift selected],
				pv.ProvinceName [thành_phố],
				'''' [Product id],
				'''' UTM,
				'''' [Referrer url],
				'''' [Campaign Id],
				'''' Phone2,
				pa.AssignedAgentDate,
				tmr.FullName Agent,
				tmr.AgentCode,
				team.Name Team,
				CASE
					WHEN cr.FollowUpStatus = 1 THEN N''Đang theo''
					WHEN cr.FollowUpStatus = 2 THEN N''Không thành công''
					WHEN cr.FollowUpStatus = 3 THEN N''Có hẹn - Tiếp tục theo dõi''
					WHEN cr.FollowUpStatus = 4 THEN N''Thắng''
					ELSE N''Chưa gọi''
				END Status,
				pa.ClosedReason,
				pa.UnassignedDate,
				p.TotalCallCount,
				p.LastCallDate1,
				p.LastCallCode1 + '' - '' + cr1.EnglishDescription LastCallCode1,
				p.LastCallDate2,
				p.LastCallCode2 + '' - '' + cr2.EnglishDescription LastCallCode2,
				p.LastCallDate3,
				p.LastCallCode3 + '' - '' + cr3.EnglishDescription LastCallCode3,
				cc.Notes Note_FINAL,
				cc.CreatedDate LastCallDate_FINAL,
				cr.Code + '' - '' + cr.EnglishDescription LastCallCode_FINAL
			')+'
		FROM cte
		'+IIF(@TobeRegain=1, '',
			'
				JOIN dbo.Customer c on c.Id=cte.CustomerId
				JOIN dbo.ProspectAssignment pa on pa.Id=cte.ProspectAssignmentId
				JOIN dbo.Prospect p on p.Id=cte.ProspectId
				LEFT JOIN dbo.CallResult cr on pa.CallResultId=cr.Id
				LEFT JOIN dbo.ContactCall cc on cc.Id=pa.LastCallId
				LEFT JOIN dbo.UserProfiles tmr on tmr.Id=pa.AssignedAgentId
				LEFT JOIN dbo.Organization team on team.Id=pa.AssignedTeamId
				LEFT JOIN dbo.Province pv on pv.Id=c.ProvinceId
				LEFT JOIN dbo.ContactCall cc1 on cc1.Id=p.LastCallId1
				LEFT JOIN dbo.ContactCall cc2 on cc2.Id=p.LastCallId2
				LEFT JOIN dbo.ContactCall cc3 on cc3.Id=p.LastCallId3
				LEFT JOIN dbo.CallResult cr1 on cc1.CallResultId=cr1.Id
				LEFT JOIN dbo.CallResult cr2 on cc2.CallResultId=cr2.Id
				LEFT JOIN dbo.CallResult cr3 on cc3.CallResultId=cr3.Id
			')+'
		'+IIF(@ExportExcel=0, ' WHERE cte.rn BETWEEN '''+CAST(@PageIndex*@PageSize+1 AS NVARCHAR(MAX))+''' AND '''+CAST(@PageIndex*@PageSize+@PageSize AS NVARCHAR(MAX))+''' ', '')+'
	';

	EXEC sp_executesql @fullQuery, N'@FullName NVARCHAR(500),
									 @IsNotCurrent BIT',
									 @FullName = @FullName,
									 @IsNotCurrent = @IsNotCurrent
END
GO