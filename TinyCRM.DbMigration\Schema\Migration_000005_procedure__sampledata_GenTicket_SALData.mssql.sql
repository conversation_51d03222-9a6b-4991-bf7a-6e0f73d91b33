
/****** Object:  StoredProcedure [sampledata].[GenTicket_SALData]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


-- DueTime FIX process : 2 hour ; Accept : 5 minute
CREATE PROCEDURE [sampledata].[GenTicket_SALData]
			 @ct_creatTimeFrom  datetime  
			,@ct_createTimeTo	datetime

			,@replicaNumberOwnershipModel14 int = 1000
			,@exactNumberOfTicketsToCreate INT = 0   -- If > 0, this param will take more priority than @replica

			,@ProcessDueMinute int = 240 --4*60
			,@AcceptDueMinute INT  = 5

			-- Status Ratio    @ct_rate4= 100 - @ct_rate3 - @ct_rate2
			,@ct_rate2 INT = 5
			,@ct_rate3 INT = 30

			-- SAL ratio  
			,@ct_rate3_lateAccept INT  = 0   -- Processing ticket means having been accepted --> could be late or not accepting
			,@ct_rate3_lateProcess INT = 20 -- ???? For task only

			,@ct_rate4_lateAccept INT = 6  -- Done ticket means having been accepted --> could be late or not accepting
			,@ct_rate4_lateProcess INT = 12 -- Done ticket means having been finished processing --> could be late or not processing

			,@cleanAllRelatedTable bit = 0
			,@debugLogOn BIT = 0
AS BEGIN

declare @ct_frequenceInSecond int
		,@ct_numberOfTicket int
		,@ct_rate4 int

IF @exactNumberOfTicketsToCreate > 0 
BEGIN
 SET @replicaNumberOwnershipModel14 = CEILING( CAST((@exactNumberOfTicketsToCreate) AS FLOAT)/ 15)
 SET @ct_numberOfTicket = @exactNumberOfTicketsToCreate
END
ELSE SET @ct_numberOfTicket = @replicaNumberOwnershipModel14 *15


PRINT CONCAT('replica ',@replicaNumberOwnershipModel14)


set @ct_frequenceInSecond = datediff(second,@ct_creatTimeFrom,@ct_createTimeTo) / @ct_numberOfTicket
if @ct_frequenceInSecond=0 set @ct_frequenceInSecond=1


DECLARE     @numTicketStatus4 INT
		  , @numTicketStatus3 INT
		  , @numTicketStatus3_t4 INT =0 -- if generate for task, save some for 20% for status 4 Commited
		  , @numTicketStatus3_t6 INT  =0-- if generate for task, save some for 5% for status 6 Returned
		  , @numTicketStatus2 INT

if 100 < @ct_rate2 set @ct_rate2=100
if 100 < @ct_rate2 + @ct_rate3 set @ct_rate3=100-@ct_rate2

set @ct_rate4=100-@ct_rate2-@ct_rate3
if @ct_rate4<0 set @ct_rate4=0

SET @numTicketStatus2 = @ct_numberOfTicket*@ct_rate2/100
SET @numTicketStatus4 = @ct_numberOfTicket*@ct_rate4/100
SET @numTicketStatus3 = @ct_numberOfTicket - @numTicketStatus2 - @numTicketStatus4


--SET @numTicketStatus3_t4 = @numTicketStatus3*30/100   -- Task Status 4 take 30%
--SET @numTicketStatus3_t6 = @numTicketStatus3*30/100   -- Task Status 6 take 30%

SET @numTicketStatus3_t4 = 0   
SET @numTicketStatus3_t6 = 0   

SET @numTicketStatus3 =@numTicketStatus3 -@numTicketStatus3_t4-@numTicketStatus3_t6;



DECLARE		 @numTicketStatus3_lateAccept  INT  
			,@numTicketStatus3_lateProcess INT  

			,@numTicketStatus4_lateAccept  INT  
			,@numTicketStatus4_lateProcess INT  
			
SET @numTicketStatus3_lateAccept  = @ct_rate3_lateAccept*@numTicketStatus3 /100
SET @numTicketStatus3_lateProcess = @ct_rate3_lateProcess*(@numTicketStatus3 + @numTicketStatus3_t4 + @numTicketStatus3_t6) /100
																			   

SET @numTicketStatus4_lateAccept  = @ct_rate4_lateAccept*@numTicketStatus4 /100
SET @numTicketStatus4_lateProcess = @ct_rate4_lateProcess*@numTicketStatus4 /100

IF @debugLogOn=1
BEGIN

print '********** REPORT **************'
PRINT  CONCAT('@total : ' , @ct_numberOfTicket,' ticket')
PRINT  CONCAT('@frequent creating : ' , @ct_frequenceInSecond,' second')
PRINT  CONCAT('@numTicketStatus2  : ' ,@numTicketStatus2)
PRINT  CONCAT('@numTicketStatus3  : ' ,@numTicketStatus3,' - late accept: ',@numTicketStatus3_lateAccept, ' :: late Process : ',@numTicketStatus3_lateProcess)


PRINT  CONCAT('@numTicketStatus3_t4 : ' ,@numTicketStatus3_t4)
PRINT  CONCAT('@numTicketStatus3_t6 : ' ,@numTicketStatus3_t6)

PRINT  CONCAT('@numTicketStatus4 : ' ,@numTicketStatus4,' - late accept: ', @numTicketStatus4_lateAccept,' late process:', @numTicketStatus4_lateProcess)


print 'start gen numbers'
END

EXEC sampledata.CreateNumberTable @maxNumber = @ct_numberOfTicket -- int

print 'start gen sal'

if exists(select * from tempdb..sysobjects where name like '%GenSAL%')
DROP TABLE #GenSAL
CREATE TABLE #GenSAL 
(
	id				INT,
	randomOrder		int,
	status00		int,
	taskStatus		int,
	createdDate		datetime null,
	acceptDue		datetime null,
	processDue		DATETIME null,
	soonProcessDue		DATETIME null,
	accepted		DATETIME null,
	processed		datetime  null
)


INSERT #GenSAL
        ( id ,
	      randomOrder,
          status00 ,
		  taskStatus,
          createdDate ,
          acceptDue ,
          processDue ,
		  soonProcessDue,
		  accepted,
		  processed
        )

SELECT n	, ROW_NUMBER() over (order by created.randomOrder) 
			,statusSuffle.Status00
			,taskStatus,created.createdDate
			,created.acceptDue
			,iif(Status00<4 AND statusSuffle.LateProcess IS NOT NULL , DATEADD(MINUTE,-10*statusSuffle.LateProcess, GETDATE()), created.processDue )   ProcessDue   --  If not finished, late process when dueProcess < now
			
			,iif(Status00=3 AND statusSuffle.LateProcess=-1 , DATEADD(MINUTE,-10*statusSuffle.LateProcess - IIF(statusSuffle.StatusOrder%10=0 ,15, 5) , GETDATE()), created.processDue )   ProcessDue   --  If not finished, late process when dueProcess < now
			

			--, IIF(n%10=0, DATEADD(MINUTE,-10, GETDATE()),DATEADD(MINUTE,10, GETDATE()) )  )
			,DATEADD(MINUTE,	2 *statusSuffle.LateAccept,		created.acceptDue)   --  +-2 minute if lateAccept or not
			, iif(statusSuffle.Status00=4, DATEADD(MINUTE,	10*statusSuffle.LateProcess,	created.processDue),NULL) --   +-2 minute if lateProcess or not                   iif(statusSuffle.Status00=4,created.processDue,getdate())   
FROM 
(   -- 
	SELECT  TOP(@ct_numberOfTicket) 
			 n 
			 ,NEWID() randomOrder
			,DATEADD(SECOND,@ct_frequenceInSecond*(n-1)							, @ct_creatTimeFrom) AS createdDate
			,DATEADD(SECOND,@ct_frequenceInSecond*(n-1) + @AcceptDueMinute *60	, @ct_creatTimeFrom) AS acceptDue
			,DATEADD(SECOND,@ct_frequenceInSecond*(n-1) + @ProcessDueMinute*60  , @ct_creatTimeFrom) AS processDue   
			,DATEADD(SECOND,@ct_frequenceInSecond*(n-1) + @ProcessDueMinute*60*0.9  , @ct_creatTimeFrom) AS soonProcessDue   
			--,DATEADD(SECOND,@ct_frequenceInSecond*n + abs(checksum(NewId()) % 1000)*60,@ct_creatTimeFrom) AS processDue   -- random Duetime 1-1000 hour  --back.up

	FROM sampledata.numbers
	ORDER BY n
) created
JOIN
(
	SELECT xx.Status00,xx.taskStatus,xx.LateAccept,xx.LateProcess, ROW_NUMBER() OVER (ORDER BY xx.randomOrder) RowNo,ROW_NUMBER() OVER (ORDER BY Status00,LateProcess) StatusOrder
	FROM
	(
						SELECT TOP(@numTicketStatus4)	 4 Status00 ,5 taskStatus,IIF(n<=@numTicketStatus4_lateAccept,1,-1) AS LateAccept,IIF(n<=@numTicketStatus4_lateProcess,1,-1) AS LateProcess							,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
						-- Ticket vs.Task
						SELECT TOP(@numTicketStatus3)	 3          ,3			 ,IIF(n<=@numTicketStatus3_lateAccept,1,-1)				 ,IIF(n<=@numTicketStatus3_lateProcess,1,-1)										,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
				      --SELECT TOP(@numTicketStatus3_t4) 3			,4			 ,IIF(n<=@numTicketStatus3_lateAccept,1,-1)				 ,IIF(n+@numTicketStatus3<=@numTicketStatus3_lateProcess,1,-1)                      ,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
					  --SELECT TOP(@numTicketStatus3_t6) 3			,6	         ,IIF(n<=@numTicketStatus3_lateAccept,1,-1)				 ,IIF(n+@numTicketStatus3+@numTicketStatus3_t4<=@numTicketStatus3_lateProcess,1,-1)	,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
						SELECT TOP(@numTicketStatus2)    2			,2           ,NULL													 ,NULL																				,NEWID() randomOrder FROM sampledata.numbers ORDER BY n
	) xx 
) statusSuffle ON statusSuffle.RowNo=created.n


IF (@debugLogOn=1)
BEGIN
	
   
SELECT * FROM (	
	SELECT xx.Status00,xx.taskStatus,xx.LateAccept,xx.LateProcess, ROW_NUMBER() OVER (ORDER BY xx.randomOrder) RowNo,ROW_NUMBER() OVER (ORDER BY Status00,LateProcess) StatusOrder
	FROM
	(
						SELECT TOP(@numTicketStatus4)	 4 Status00 ,5 taskStatus,IIF(n<=@numTicketStatus4_lateAccept,1,-1) AS LateAccept,IIF(n<=@numTicketStatus4_lateProcess,1,-1) AS LateProcess							,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
						-- Ticket vs.Task
						SELECT TOP(@numTicketStatus3)	 3          ,3			 ,IIF(n<=@numTicketStatus3_lateAccept,1,-1)				 ,IIF(n<=@numTicketStatus3_lateProcess,1,-1)										,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
				      --SELECT TOP(@numTicketStatus3_t4) 3			,4			 ,IIF(n<=@numTicketStatus3_lateAccept,1,-1)				 ,IIF(n+@numTicketStatus3<=@numTicketStatus3_lateProcess,1,-1)                      ,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
					  --SELECT TOP(@numTicketStatus3_t6) 3			,6	         ,IIF(n<=@numTicketStatus3_lateAccept,1,-1)				 ,IIF(n+@numTicketStatus3+@numTicketStatus3_t4<=@numTicketStatus3_lateProcess,1,-1)	,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
						
						SELECT TOP(@numTicketStatus2)    2			,2           ,NULL													 ,NULL																				,NEWID() randomOrder FROM sampledata.numbers ORDER BY n
	) xx 
	) yy 
	WHERE status00=3 AND LateProcess=-1 AND statusOrder%10=0

	ORDER BY StatusOrder 



	--SELECT xx.Status00,xx.LateAccept,xx.LateProcess,COUNT(*)
	--FROM
	--(
	--					SELECT TOP(@numTicketStatus4)	 4 Status00 ,5 taskStatus,IIF(n<=@numTicketStatus4_lateAccept,1,-1) AS LateAccept,IIF(n<=@numTicketStatus4_lateProcess,1,-1) AS LateProcess							,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
	--					SELECT TOP(@numTicketStatus3)	 3          ,3			 ,IIF(n<=@numTicketStatus3_lateAccept,1,-1)				 ,IIF(n<=@numTicketStatus3_lateProcess,1,-1)										,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
	--					--SELECT TOP(@numTicketStatus3_t4) 3			,4			 ,IIF(n<=@numTicketStatus3_lateAccept,1,-1)				 ,IIF(n+@numTicketStatus3<=@numTicketStatus3_lateProcess,1,-1)                      ,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
	--					--SELECT TOP(@numTicketStatus3_t6) 3			,6	         ,IIF(n<=@numTicketStatus3_lateAccept,1,-1)				 ,IIF(n+@numTicketStatus3+@numTicketStatus3_t4<=@numTicketStatus3_lateProcess,1,-1)	,NEWID() randomOrder FROM sampledata.numbers ORDER BY n			UNION all
	--					SELECT TOP(@numTicketStatus2)    2			,2           ,NULL													 ,NULL																				,NEWID() randomOrder FROM sampledata.numbers ORDER BY n
	--) xx
	--GROUP BY xx.Status00,xx.LateAccept,xx.LateProcess

	--SELECT  TOP(@ct_numberOfTicket) 
	--		 'created',
	--		 n 
	--		 ,NEWID() randomOrder
	--		,DATEADD(SECOND,@ct_frequenceInSecond*n, @ct_creatTimeFrom) AS createdDate
	--		,DATEADD(SECOND,@ct_frequenceInSecond*n + @AcceptDueMinute*60,@ct_creatTimeFrom) AS acceptDue
	--		,DATEADD(SECOND,@ct_frequenceInSecond*n + @ProcessDueMinute*60,@ct_creatTimeFrom) AS processDue   -- random Duetime 1-1000 hour
	--FROM sampledata.numbers
	--ORDER BY n

	
END

--select DATEDIFF(hour,createdDate,processDue) from #GenSAL


--------------- Data Part 1. OWNERSHIP 


--print concat('start gen Ownership' ,@replicaNumberOwnershipModel14)

if exists (select * from tempdb..sysobjects where name like '%OwnershipData%')
begin
	DROP TABLE #OwnershipData
end


CREATE TABLE #OwnershipData 
(
	id				int identity,
	ti_createdBy	UNIQUEIDENTIFIER,
	ti_ownedBy		UNIQUEIDENTIFIER,
	ta1_asssignedTo UNIQUEIDENTIFIER,
	ta2_asssignedTo UNIQUEIDENTIFIER,
	ta1_reviewedBy	UNIQUEIDENTIFIER,
	
	cus_id			UNIQUEIDENTIFIER,
	ti_id			UNIQUEIDENTIFIER,
	ti_code			VARCHAR(20),	
	ti_acceptDue	UNIQUEIDENTIFIER,
	ti_processDue	UNIQUEIDENTIFIER,
	phaseId			UNIQUEIDENTIFIER,
	ta1_Id			UNIQUEIDENTIFIER,
	ta2_Id			UNIQUEIDENTIFIER,
)

PRINT CONCAT('gen ownership ',@replicaNumberOwnershipModel14,' replica')
insert #OwnershipData 
EXEC sampledata.GenTicket_OwnershipData @replicaNumberOwnershipModel14


IF @exactNumberOfTicketsToCreate>0
BEGIN
	DELETE #OwnershipData WHERE id>@exactNumberOfTicketsToCreate
END


--SELECT * FROM #OwnershipData
--print 'gen ownership successed'

---  insert real tables

IF (@cleanAllRelatedTable=1)
begin
	TRUNCATE TABLE dbo.AuditEntityChange
	TRUNCATE TABLE dbo.AuditFieldChange
	TRUNCATE TABLE dbo.TaskFeedback
	TRUNCATE TABLE dbo.Task
	TRUNCATE TABLE dbo.DueTimeReference
	TRUNCATE TABLE dbo.Phase
	TRUNCATE TABLE dbo.RequestTicket
	TRUNCATE TABLE dbo.DynamicFieldValue
	TRUNCATE TABLE dbo.DynamicFormValue
END 

declare @currentTicketCount int
set @currentTicketCount = (select count(*) from RequestTicket)

INSERT INTO [dbo].[RequestTicket] ([Id], [Type], [CustomerId], [SourceChannel], [Code], [IsoCode], [ProcessDueDate], [AcceptDueDate]
, [OwnerId], [Status], [CreatedDate], [CreatedBy], [ModifiedDate], [ModifiedBy], [Deleted], [DeletedDate], [DeletedBy], [Level1Id], [Level2Id], [Level3Id], [Level4Id]
, [Notes], [CustomerAlternativeAddressId], [ProcessDueTimeId], [AcceptDueTimeId], [RpName], [RpEmail], [RpPhone], [RpAddress], [Treatment], [OwnedByOrganizationId]
, [DifficultyDegree], [FinishedTicketDate], [AcceptedTicketDate], [ProvinceId], [SoonerAcceptDueMinutes], [SoonerProcessDueMinutes], [ProcessSoonDueDate], [DynamicFormValueId]
, [ServiceTypeId], [ContextToken], [IsNoneCustomerTicket], [DelegatedTicket], [DelegatedRelationship], [DelegatedOtherRelationship])
SELECT ti_id,
       0,
       cus_id,
       1,
       'A' + RIGHT('00000000'+CAST(s.id+@currentTicketCount AS VARCHAR(20)),8) ,
       NULL,
       s.processDue,   --[ProcessDueDate], 
       s.acceptDue,   --[AcceptDueDate]
       ti_ownedBy,
       s.status00,
       --DATEADD(MINUTE,-id, GETDATE()),
	   s.createdDate,
       ti_createdBy,
       NULL,
       NULL,
       0,
       NULL,
       NULL,
       st.Level1Id,
       st.Level2Id,
       st.Level3Id,
       st.Level4Id,
       NULL,
       NULL,
       ti_processDue,
       ti_acceptDue,
       NULL,
       NULL,
       NULL,
       NULL,
       NULL,
       '045364aa-8749-4568-9667-705249e40848',
       1,
       s.processed,   --  [FinishedTicketDate] 
       s.accepted,   --  [AcceptedTicketDate]
       NULL,
       NULL,
       NULL,
       s.soonProcessDue,
       NULL,
       st.Id,
       N'ninQpmBUrE6p3CsNj1nB8w',
        0,
        0,
        NULL,
        NULL
FROM #OwnershipData o
join #GenSAL s on o.id=s.randomOrder
LEFT JOIN (SELECT *,ROW_NUMBER() OVER (ORDER BY (SELECT 1)) RowOrder FROM dbo.ServiceType) st ON st.RowOrder =( s.id % ((SELECT COUNT(*) FROM dbo.ServiceType)/5) )+1




--INSERT INTO [dbo].[RequestTicket] ([Id], [Type], [CustomerId], [BehaviorClassification], [SourceChannel], [Code], [IsoCode], [ProcessDueDate], [AcceptDueDate], [OwnerId], [Status], [CreatedDate], [CreatedBy], [ModifiedDate], [ModifiedBy], [Deleted], [DeletedDate], [DeletedBy], [Level1Id], [Level2Id], [Level3Id], [Level4Id], [Notes], [CustomerAlternativeAddressId], [ProcessDueTimeId], [AcceptDueTimeId], [RpName], [RpEmail], [RpPhone], [RpAddress], [Treatment], [OwnedByOrganizationId], [DifficultyDegree], [FinishedTicketDate], [AcceptedTicketDate], [ProvinceId], [SoonerAcceptDueMinutes], [SoonerProcessDueMinutes], [ProcessSoonDueDate], [DynamicFormValueId], [ServiceTypeId], [ContextToken], [IsNoneCustomerTicket], [DelegatedTicket], [DelegatedRelationship], [DelegatedOtherRelationship]) 
--SELECT ti_id, 0, cus_id, 0, 1, ti_code, NULL, '2019-03-01 08:00:00.000', '2019-02-28 08:10:00.000',ti_ownedBy, ti_status, DATEADD(MINUTE,-id,GETDATE()), ti_createdBy, NULL, NULL, 0, NULL, NULL, '08a3b585-ebf4-4985-923a-24294fa5b909', '7adac9c0-140a-483d-bdf1-4061b8f9cc96', '74f1e436-8352-46f4-81aa-5058a037eb02', NULL, NULL, NULL,ti_processDue, ti_acceptDue, NULL, NULL, NULL, NULL, NULL, '045364aa-8749-4568-9667-705249e40848', 1, NULL, NULL, NULL, NULL, NULL, '2019-03-04 14:36:00.000', NULL, 'bd8c14fd-9915-4ad7-b385-c35c84ee8607', N'ninQpmBUrE6p3CsNj1nB8w', 0, 0, NULL, NULL
--FROM #OwnershipData


INSERT INTO [dbo].[DueTimeReference] ([Id], [Name], [Duration], [IsWorkingTime], [StartWorkingTime], [EndWorkingTime], [StartBreakTime], [EndBreakTime], [WorkingSaturday], [WorkingSunday], [VacationDays], [OvertimeDays], [PercentPassedWhenAlertDue], [CreatedDate], [CreatedBy], [ReferenceObjectId], [ReferenceObjectType]) 
SELECT ti_processDue, NULL, 1440, 1, '08:00:00.0000000', '17:00:00.0000000', '12:00:00.0000000', '13:00:00.0000000', 0, 0, '28/06/2016; 1/1/2018', '28/07/2016; 1/7/2018', 90, '2019-02-27 17:42:01.837', ti_createdBy, ti_id, 'RequestTicket.ProcessDueTime'
FROM #OwnershipData

INSERT INTO [dbo].[DueTimeReference] ([Id], [Name], [Duration], [IsWorkingTime], [StartWorkingTime], [EndWorkingTime], [StartBreakTime], [EndBreakTime], [WorkingSaturday], [WorkingSunday], [VacationDays], [OvertimeDays], [PercentPassedWhenAlertDue], [CreatedDate], [CreatedBy], [ReferenceObjectId], [ReferenceObjectType]) 
SELECT ti_acceptDue, N'System Chung', 10, 1, '08:00:00.0000000', '17:00:00.0000000', '12:00:00.0000000', '13:00:00.0000000', 0, 0, '28/06/2016; 1/1/2018', '28/07/2016; 1/7/2018', 100, '2019-02-27 17:42:01.840', ti_createdBy, ti_id, 'RequestTicket.AcceptDueTime'
FROM #OwnershipData

INSERT INTO [dbo].[Phase] ([Id], [CreatedBy], [CreatedDate], [ModifiedBy], [ModifiedDate], [Deleted], [DeletedBy], [DeletedDate], [TicketId], [Order], [Status], [Notes]) 
SELECT phaseId, ti_createdBy, '2019-02-27 17:42:01.903', ta1_asssignedTo, '2019-02-27 17:44:04.450', 0, NULL, NULL, ti_id, 1, 1, NULL
FROM #OwnershipData

INSERT INTO [dbo].[Task] ([Id], [CreatedBy], [CreatedDate], [ModifiedBy], [ModifiedDate], [Deleted], [DeletedBy], [DeletedDate], [PhaseId], [TaskTypeId], [AssignedTo], [NotificationSMS], [Description], [Status], [ProcessDueTime], [SendEmail], [ProcessDueTimeId], [CompleteReason], [FinishedDate], [DynamicFormValueId], [AssignedDate], [AcceptDueTimeId], [AcceptDueTime], [OwnedByOrganizationId], [AcceptDate], [ConfirmedBy], [ConfirmedDate]) 
SELECT ta1_Id, ti_createdBy, s.createdDate, ta1_asssignedTo, '2019-02-27 17:43:43.113', 0, NULL, NULL, phaseId, 'ae332617-e2c3-4536-a9a3-98fac773e12c', ta1_asssignedTo, 0, NULL, s.taskStatus , s.processDue, 1, 'e4013a1b-6bc4-43c4-bacd-0c9deb02f1a5', 2, s.processed, NULL, '2019-02-27 17:43:06.590', 'ad2192aa-e2c8-4f71-baa1-79defa51b63b', s.acceptDue, 'e649ada0-6027-444d-b105-9ed4c03879cb', s.accepted, ta1_asssignedTo, '2019-02-27 17:43:43.113'
FROM #OwnershipData o
join #GenSAL s on o.id=s.randomOrder

INSERT INTO [dbo].[Task] ([Id], [CreatedBy], [CreatedDate], [ModifiedBy], [ModifiedDate], [Deleted], [DeletedBy], [DeletedDate], [PhaseId], [TaskTypeId], [AssignedTo], [NotificationSMS], [Description], [Status], [ProcessDueTime], [SendEmail], [ProcessDueTimeId], [CompleteReason], [FinishedDate], [DynamicFormValueId], [AssignedDate], [AcceptDueTimeId], [AcceptDueTime], [OwnedByOrganizationId], [AcceptDate], [ConfirmedBy], [ConfirmedDate]) 
SELECT ta2_Id, ta1_asssignedTo, s.createdDate, ta2_asssignedTo, '2019-02-27 17:44:39.687', 0, NULL, NULL, phaseId, '1e92dcbb-5721-4318-82c4-53d446d41790', ta2_asssignedTo, 0, NULL, s.taskStatus, s.processDue, 1, 'e4013a1b-6bc4-43c4-bacd-0c9deb02f1a5', 2, s.processed, NULL, '2019-02-27 17:44:04.460', 'ad2192aa-e2c8-4f71-baa1-79defa51b63b', s.acceptDue, 'd3fdebe5-b1cf-44d3-a18e-da7669f8a741', s.accepted, ta1_reviewedBy, NULL
FROM #OwnershipData o
join #GenSAL s on o.id=s.randomOrder


INSERT INTO [dbo].[TaskFeedback] ([Id], [CreatedBy], [CreatedDate], [ModifiedBy], [ModifiedDate], [Deleted], [DeletedBy], [DeletedDate], [TaskId], [Descriptions]) 
SELECT NEWID(), ti_createdBy, '2019-02-27 17:43:06.593', ta1_asssignedTo, '2019-02-27 17:43:43.113', 0, NULL, NULL, ta1_Id, NULL
FROM #OwnershipData

INSERT INTO [dbo].[TaskFeedback] ([Id], [CreatedBy], [CreatedDate], [ModifiedBy], [ModifiedDate], [Deleted], [DeletedBy], [DeletedDate], [TaskId], [Descriptions]) 
SELECT NEWID(), ta1_asssignedTo, '2019-02-27 17:44:04.467', ta2_asssignedTo, '2019-02-27 17:44:39.687', 0, NULL, NULL, ta2_Id, NULL
FROM #OwnershipData

DROP TABLE #OwnershipData

END
GO