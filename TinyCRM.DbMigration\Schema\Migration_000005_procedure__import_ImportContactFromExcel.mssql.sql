
/****** Object:  StoredProcedure [import].[ImportContactFromExcel]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[ImportContactFromExcel]
	@RawTable [import].[ContactType] READONLY
AS
BEGIN
	SET NOCOUNT ON;

    INSERT INTO import.TMRContact
            ( Id ,
              DataSource ,
              PhoneNumber ,
              ContactName ,
              Address ,
              ProvinceName ,
              DOB ,
              Gender ,
              Career ,
              Marital ,
              Income ,
              Note ,
              UserName ,
              PhoneNumberValid ,
              ProvinceIdValid ,
              UserIdValid ,
              DOBValid ,
			  IsInDbContact
            )
    SELECT    
			  NEWID(),
			  DataSource ,
              PhoneNumber ,
              ContactName ,
              Address ,
              ProvinceName ,
              DOB ,
              Gender ,
              Career ,
              Marital ,
              Income ,
              Note ,
              UserName ,
              PhoneNumberValid ,
			  NULL ,
              NULL ,
              DOBValid,
			  0
    FROM @RawTable WHERE DataSource!=N'DataSource'

END
GO