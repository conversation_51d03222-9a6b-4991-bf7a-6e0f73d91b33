
/****** Object:  StoredProcedure [telesale].[GetImportedHotListResults]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[GetImportedHotListResults]
	@ImportSessionId			UNIQUEIDENTIFIER,
	@StartRow					INT,
	@EndRow						INT
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	WITH cte AS
    (
		SELECT	[DataSource]
				,sc.[Phone]
				,sc.[FullName]
				,sc.[Address]
				,pv.ProvinceName Province
				,FORMAT(sc.[DOB], 'dd-MM-yyyy') DOB
				,CASE 
					WHEN sc.[Gender] = 'Male' THEN N'Nam'
					WHEN sc.[Gender] = 'Female' THEN N'Nữ'
					ELSE N'Không xác định'
				END Gender
				,sc.[Job]
				,CASE 
					WHEN sc.[MaritalStatus] = 'Single' THEN N'Độc thân'
					WHEN sc.[MaritalStatus] = 'Married' THEN N'Đã kết hôn'
					ELSE N'Không xác định'
				END [MaritalStatus]
				,CAST(sc.[Income] AS NVARCHAR(MAX)) Income
				,sc.[Notes]
				,CASE
					--WHEN ISNULL(sc.DataErrorMessage,'') <> '' THEN sc.DataErrorMessage
					WHEN ISNULL(sc.DuplicatedCase,0) = 0 THEN N'Available to assign'
					WHEN ISNULL(sc.DuplicatedCase,0) = 1 THEN N'Internal Duplication'
					WHEN ISNULL(sc.DuplicatedCase,0) = 2 THEN N'Phân bổ lại (Chờ 90 ngày)'
					WHEN ISNULL(sc.DuplicatedCase,0) = 3 THEN N'Không tiềm năng'
					WHEN ISNULL(sc.DuplicatedCase,0) = 4 THEN N'Trong rổ sup'
					WHEN ISNULL(sc.DuplicatedCase,0) = 5 THEN N'Trong rổ TMR, Ưu tiên'
					WHEN ISNULL(sc.DuplicatedCase,0) = 6 THEN N'Trong rổ TMR, Thường-chưa gọi'
					WHEN ISNULL(sc.DuplicatedCase,0) = 7 THEN N'Trong rổ TMR, Thường-đang theo'
					ELSE ISNULL(sc.DataErrorMessage,'')
				END DuplicatedCaseName,
				CASE
					WHEN ISNULL(sc.DuplicatedCase,0) = 4 OR ISNULL(sc.DuplicatedCase,0) = 6 THEN preOrg.OrganizationName
					WHEN ISNULL(sc.DuplicatedCase,0) = 5 OR ISNULL(sc.DuplicatedCase,0) = 7 THEN currentOrg.OrganizationName
					ELSE ''
				END DuplicatedTMRTeamName,
				CASE
					WHEN ISNULL(sc.DuplicatedCase,0) = 6 THEN ISNULL(preUp.AgentCode + '-', '') + ISNULL(preUp.FullName,preU.UserName)
					WHEN ISNULL(sc.DuplicatedCase,0) = 5 OR ISNULL(sc.DuplicatedCase,0) = 7 THEN ISNULL(currentUp.AgentCode + '-', '') + ISNULL(currentUp.FullName,currentU.UserName)
					ELSE ''
				END DuplicatedTMRName,
				ISNULL(assignedUp.AgentCode + '-', '') + ISNULL(assignedUp.FullName,assignedU.UserName) AssignedTMRName,
				assignedOrg.OrganizationName AssignedOrgName,
				ROW_NUMBER() OVER (ORDER BY sc.RowOrder) RowNumber
		FROM	dbo.StagingContact sc
				LEFT JOIN dbo.Contact c ON c.Phone = sc.Phone
				LEFT JOIN dbo.Prospect p ON p.ContactId = c.Id
				LEFT JOIN dbo.ProspectAssignment pa ON pa.Id = sc.ToBeHotDistributePaId
				LEFT JOIN dbo.Province pv ON pv.Id = c.ProvinceId
				LEFT JOIN dbo.Organization assignedOrg ON assignedOrg.Id = pa.AssignedTeamId
				LEFT JOIN dbo.aspnet_Users assignedU ON assignedU.UserId = pa.AssignedAgentId
				LEFT JOIN dbo.UserProfiles assignedUp ON assignedUp.Id = assignedU.UserId
				LEFT JOIN dbo.ProspectAssignment prePa ON prePa.Id = pa.PreviousProspectAssingmentId
				LEFT JOIN dbo.Organization preOrg ON preOrg.Id = prePa.AssignedTeamId
				LEFT JOIN dbo.aspnet_Users preU ON preU.UserId = prePa.AssignedAgentId
				LEFT JOIN dbo.UserProfiles preUp ON preUp.Id = preU.UserId
				LEFT JOIN dbo.ProspectAssignment cpa ON cpa.Id = sc.CurrentProspectAssignmentId
				LEFT JOIN dbo.Organization currentOrg ON currentOrg.Id = cpa.AssignedTeamId
				LEFT JOIN dbo.aspnet_Users currentU ON currentU.UserId = cpa.AssignedAgentId
				LEFT JOIN dbo.UserProfiles currentUp ON currentUp.Id = currentU.UserId
		WHERE	sc.ImportSessionId = @ImportSessionId
	)
	SELECT	*, (SELECT COUNT(*) FROM cte) TotalCount
	FROM	cte
	WHERE	cte.RowNumber BETWEEN @StartRow AND @EndRow
******/
END
GO