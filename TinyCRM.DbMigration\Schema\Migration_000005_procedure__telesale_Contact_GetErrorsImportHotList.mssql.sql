
/****** Object:  StoredProcedure [telesale].[Contact_GetErrorsImportHotList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[Contact_GetErrorsImportHotList]

	@ImportSessionId		UNIQUEIDENTIFIER

AS
BEGIN

	SELECT	COUNT(*) ItemCount,
			DataErrorMessage ErrorType
	FROM	dbo.StagingContact
	WHERE	ImportSessionId = @ImportSessionId
			AND ISNULL(DataErrorMessage,'') <> ''
			AND isnull(DuplicatedCase,0) <> -1
	GROUP BY DataErrorMessage

END
GO