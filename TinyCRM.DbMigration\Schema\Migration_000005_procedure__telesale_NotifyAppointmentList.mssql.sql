
/****** Object:  StoredProcedure [telesale].[NotifyAppointmentList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


--ALTER PROCEDURE [telesale].[NotifyAppointmentList]
CREATE PROCEDURE [telesale].[NotifyAppointmentList]

	@FromDate		DATETIME,
	@ToDate			DATETIME,
	@TeamId			UNIQUEIDENTIFIER,
	@ExportUserId	UNIQUEIDENTIFIER

AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	INSERT INTO dbo.Notification (Id, Value, ReferenceObjectId, ReferenceObjectType, NotifyTo, Status, CreatedBy, CreatedDate)
	SELECT	NEWID(), N'Cuộc hẹn với khách hàng ' + c.FullName + N' đã được lấy.', ap.Id, 'Appointment', ap.CreatedBy, 1, @ExportUserId, GETDATE()
	FROM	dbo.Appointment ap
			JOIN dbo.LeadAssignment la ON la.Id = ap.LeadAssignmentId
			JOIN dbo.Lead l ON l.Id = la.LeadId
			JOIN dbo.ProspectAssignment pa ON pa.Id = l.CreatedByProspectAssignmentId
			JOIN dbo.Prospect p ON p.Id = pa.ProspectId
			JOIN dbo.Contact c ON c.Id = p.ContactId
			LEFT JOIN dbo.[Notification] n ON n.ReferenceObjectId = ap.Id
	WHERE	(@FromDate IS NULL OR ap.CreatedDate >= @FromDate)
			AND (@ToDate IS NULL OR ap.CreatedDate < @ToDate)
			AND ap.Status = 1
			AND n.Id IS NULL
******/
END
GO