
/****** Object:  StoredProcedure [telesale].[CreateContactFromAppointmentRaw]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[CreateContactFromAppointmentRaw]
	@UserId UNIQUEIDENTIFIER,
	@SessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	WITH cte AS
	(
		SELECT
		ar.ContactId Id,
		ar.ContactName FullName,
		ar.[Address] [Address],
		wf.WardId,
		df.DistrictId,
		pf.ProvinceId,
		ar.CellPhone Phone,
		ar.ContactDOB DOB,
		ar.DataSource,
		GETDATE() CreatedDate,
		ar.Carrier Job,
		N'' Email,
		0 Inactive,
		NULL LastCallId,
		N'' Notes,
		@UserId CreatedBy,
		NULL AccountVisitId,
		NULL ResultTypeId,
		NULL AccountVisitUserId,
		0 Income,
		0 MaritalStatus,
		0 Gender,
		0 [Status],
		ar.HomePhone Phone2,
		NULL Phone3,
		NULL CMND
		FROM dbo.AppointmentRaw ar
		LEFT JOIN
		(
			SELECT p.ImportCodeName ImportCodeNameP, p.Id ProvinceId, p.ProvinceName FROM dbo.Province p
		) pf ON pf.ImportCodeNameP=ar.Province
		LEFT JOIN
		(
			SELECT p.ImportCodeName ImportCodeNameP, d.ImportCodeName ImportCodeNameD, p.ProvinceName, d.Id DistrictId, d.DistrictName FROM dbo.Province p
			INNER JOIN dbo.District d ON d.ProvinceId=p.Id
		) df ON df.ImportCodeNameD=ar.District AND df.ImportCodeNameP=ar.Province
		LEFT JOIN
		(
			SELECT p.ImportCodeName ImportCodeNameP, d.ImportCodeName ImportCodeNameD, p.ProvinceName, d.DistrictName, w.Id WardId, w.WardName FROM dbo.Province p
			INNER JOIN dbo.District d ON d.ProvinceId=p.Id
			INNER JOIN dbo.Ward w ON w.DistrictId=d.Id
		) wf ON wf.WardName=ar.Ward AND wf.ImportCodeNameD=ar.District AND wf.ImportCodeNameP=ar.Province
		WHERE ar.IsInvalid=0 AND ar.ImportSessionId=@SessionId AND ar.IsDupContact=0
	)
	INSERT INTO dbo.Contact
	        ( Id ,
	          FullName ,
	          Address ,
	          WardId ,
	          DistrictId ,
	          ProvinceId ,
	          Phone ,
	          DOB ,
	          DataSource ,
	          CreatedDate ,
	          Job ,
	          Email ,
	          Inactive ,
	          LastCallId ,
	          Notes ,
	          CreatedBy ,
	          AccountVisitId ,
	          ResultTypeId ,
	          AccountVisitUserId ,
	          Income ,
	          MaritalStatus ,
	          Gender ,
	          Status ,
	          Phone2 ,
	          Phone3 ,
	          CMND
	        )
	SELECT * FROM cte
END
GO