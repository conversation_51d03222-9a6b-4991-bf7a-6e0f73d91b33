﻿using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Webaby.Caching;

namespace Webaby.Core.BusinessSettings.Queries
{
    public class GetBusinessSettingsByImportKeyQuery : QueryBase<BusinessSettingEntity>
    {
        public string ImportKey { get; set; }
    }

    internal class GetBusinessSettingsByImportKeyQueryHandler : QueryHandlerBase<GetBusinessSettingsByImportKeyQuery, BusinessSettingEntity>
    {
        public override QueryResult<BusinessSettingEntity> Execute(GetBusinessSettingsByImportKeyQuery query)
        {
            var businessSettingData = EntitySet.Get<BusinessSettingEntity>().Where(x => x.ImportKey == query.ImportKey);
            return new QueryResult<BusinessSettingEntity>(businessSettingData);
        }
    }
}
