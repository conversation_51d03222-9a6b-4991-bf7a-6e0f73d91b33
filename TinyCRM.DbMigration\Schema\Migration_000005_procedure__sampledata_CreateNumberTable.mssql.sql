
/****** Object:  StoredProcedure [sampledata].[CreateNumberTable]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROC [sampledata].[CreateNumberTable]
 @maxNumber INT = 200000
as
BEGIN

IF OBJECT_ID('sampledata.numbers') IS NOT NULL DROP TABLE sampledata.numbers
CREATE TABLE sampledata.numbers (n INTEGER)

;
WITH e1(n) AS (SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               UNION ALL SELECT 1
               ), -- 10
e2(n) AS (SELECT 1 FROM e1 CROSS JOIN e1 AS b), -- 10*10
e3(n) AS (SELECT 1 FROM e1 CROSS JOIN e2), -- 10*100
e4(n) AS (SELECT 1 FROM e2 CROSS JOIN e3) -- 1000*100 
,e5(n) AS (SELECT 1 FROM e4 CROSS JOIN e1) -- 100.000  *10   =   1 Million
INSERT sampledata.numbers (n)
SELECT TOP (@maxNumber)  n=ROW_NUMBER() OVER (ORDER BY n) 
FROM e5 
ORDER BY n

END
GO