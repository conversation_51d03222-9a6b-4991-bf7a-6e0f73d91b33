
/****** Object:  StoredProcedure [telesale].[UpdateCallInfo]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[UpdateCallInfo] 
	@Begin DATETIME,
	@End DATETIME,
	@CallInfo telesale.CallInfoType READONLY
AS
BEGIN
	-- Why force MERGE JOIN below? 
	-- Most of the cases, the number of inputted call duration values from Recording service count(@CallInfo) is almost equal to the number of calls created @UpdateScopeByMinute ago, so a merge join is hinted
	-- Noted that This store is blind from exact statistic due to @CallInfo and @UpdateScripByMinite in query, therefore, parameter sniffing will certainly be happened.
	UPDATE cc SET
	cc.Duration=cf.Duration,
	cc.Inum=cf.Inum
	FROM dbo.ContactCall cc
	INNER MERGE JOIN @CallInfo cf ON cf.CallID = cc.CallId
	-- the Where statement below is redundant in business logic, but still here to avoid one extra index ContactCall.CallId, force sql to use index ContactCall.CreatedDate
	WHERE
	cc.Inum IS NULL AND
	(cc.CreatedDate BETWEEN @Begin AND @End)
END
GO