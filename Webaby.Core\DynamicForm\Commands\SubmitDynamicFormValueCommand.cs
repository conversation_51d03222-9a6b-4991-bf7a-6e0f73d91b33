﻿using AutoMapper;
using Microsoft.Data.SqlClient;
using System.Data;
using System.Data.Common;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace Webaby.Core.DynamicForm.Commands
{
    public class SubmitDynamicFormValueCommand : CommandBase
    {
        public Guid DynamicFormValueId { get; set; }

        public Guid? DynamicFormId { get; set; }

        public Guid? ReferenceObjectId { get; set; }

        public string ReferenceObjectType { get; set; }

        public List<DynamicFieldValueInfo> DynamicFieldValues { get; set; }

        public bool TrackAudit { get; set; }

        public Guid? AuditSessionId { get; set; }
    }

    internal class SubmitDynamicFormValueCommandHandler : CommandHandlerBase<SubmitDynamicFormValueCommand>
    {
        private readonly IUserService _userService;

        public SubmitDynamicFormValueCommandHandler (
            IText text, 
            IMapper mapper, 
            IRepository repository, 
            IEntitySet entitySet, 
            IQueryExecutor queryExecutor, 
            ICommandExecutor commandExecutor, 
            ILocalTransactionManager transactionManager, 
            IEventBus eventBus,
            IUserService userService
        ) : base(text, mapper, repository, entitySet, queryExecutor, commandExecutor, transactionManager, eventBus)
        {
            _userService = userService;
        }

        public override async Task ExecuteAsync(SubmitDynamicFormValueCommand command)
        {
            DbCommand cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@DynamicFormValueId", command.DynamicFormValueId),
                DbParameterHelper.AddNullableGuid(cmd, "@DynamicFormId", command.DynamicFormId),
                DbParameterHelper.AddNullableGuid(cmd, "@ReferenceObjectId", command.ReferenceObjectId),
                DbParameterHelper.AddNullableString(cmd, "@ReferenceObjectType", command.ReferenceObjectType),
                DbParameterHelper.AddNullableGuid(cmd, "@UserId", _userService.GetCurrentUser().Id),
                DbParameterHelper.NewNullableDateTimeParameter(cmd, "@DateTime", DateTime.Now),
                DbParameterHelper.NewNullableBooleanParameter(cmd, "@TrackAudit", command.TrackAudit),
                DbParameterHelper.AddNullableGuid(cmd, "@AuditSessionId", command.AuditSessionId)
            });

            #region DynamicFieldValue List Param

            DataTable dynamicFieldValueTable = new DataTable("dbo.DynamicFieldValueTable");
            dynamicFieldValueTable.Columns.Add("Id", typeof(Guid));
            dynamicFieldValueTable.Columns.Add("DynamicFieldId", typeof(Guid));
            dynamicFieldValueTable.Columns.Add("DynamicFormValueId", typeof(Guid));
            dynamicFieldValueTable.Columns.Add("Value", typeof(string));
            dynamicFieldValueTable.Columns.Add("BusinessValidationResult", typeof(Boolean));

            foreach (DynamicFieldValueInfo dynamicFieldValueInfo in command.DynamicFieldValues)
            {
                dynamicFieldValueTable.Rows.Add(dynamicFieldValueInfo.Id.IsNullOrEmpty() ? Guid.NewGuid() : dynamicFieldValueInfo.Id.Value, dynamicFieldValueInfo.FieldId, command.DynamicFormValueId, dynamicFieldValueInfo.Value, dynamicFieldValueInfo.BusinessValidationResult);
            }

            SqlParameter dynamicFieldValueListParam = new SqlParameter("@DynamicFieldValueList", SqlDbType.Structured)
            {
                Value = dynamicFieldValueTable
            };

            cmd.Parameters.Add(dynamicFieldValueListParam);

            #endregion

            cmd.CommandText = "dbo.SaveDynamicFormValue";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}