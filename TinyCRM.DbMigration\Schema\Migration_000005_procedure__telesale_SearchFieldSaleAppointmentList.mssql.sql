
/****** Object:  StoredProcedure [telesale].[SearchFieldSaleAppointmentList]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

--<PERSON>TE<PERSON> PROCEDURE [telesale].[SearchFieldSaleAppointmentList]
CREATE PROCEDURE [telesale].[SearchFieldSaleAppointmentList]

	@FieldSaleTeamId	UNIQUEIDENTIFIER,
	@FieldSaleId		UNIQUEIDENTIFIER,
	@TMRTeamId			UNIQUEIDENTIFIER,
	@AppointmentStatus	INT,
	@MeetDate			DATETIME,
	@MeetDateTo			DATETIME = NULL,
	@Range BIT = 0,
	@CreatedDate		DATETIME,
	@StartRow			INT,
	@EndRow				INT,
	@LeadCode				INT,
	@TMRId UNIQUEIDENTIFIER,
	@ProvinceId UNIQUEIDENTIFIER,
	@DistrictId UNIQUEIDENTIFIER,
	@FeedbackCode UNIQUEIDENTIFIER,
	@UserLoginId UNIQUEIDENTIFIER,
	@Role INT --1 - D<PERSON>, 2- SupDMO, 3- DMO Manager
AS
BEGIN

	DECLARE @TeamFilter IdList;
	IF @Role=3
		IF @FieldSaleTeamId IS NULL
			INSERT INTO @TeamFilter SELECT o.Id FROM dbo.Organization o
			JOIN dbo.UserProfiles up ON up.OrganizationId=o.ParentId
			WHERE up.Id=@UserLoginId
		ELSE
			INSERT INTO @TeamFilter SELECT @FieldSaleTeamId
	ELSE IF @Role=2
		INSERT INTO @TeamFilter SELECT @FieldSaleTeamId
	ELSE IF @Role=1
		INSERT INTO @TeamFilter SELECT OrganizationId FROM dbo.UserProfiles WHERE id=@UserLoginId
	DECLARE @TeamCount INT = (SELECT COUNT(*) FROM @TeamFilter);

	DECLARE @selectQuery NVARCHAR(MAX)=
	' SELECT ap.Id aId, la.Id laId, l.Id lId, ROW_NUMBER() OVER (ORDER BY ap.MeetDate desc) rn FROM ';

	DECLARE @joinQuery NVARCHAR(MAX)=
	'
		dbo.LeadAssignment la
		JOIN dbo.Lead l ON la.Id = l.CurrentLeadAssignmentId
		JOIN dbo.Appointment ap on la.WaitingAppointmentId=ap.Id
		'+IIF(@ProvinceId IS NULL, '', ' JOIN dbo.Province pv ON pv.Id = ap.ProvinceId ')+'
		'+IIF(@TMRId IS NULL, '', ' JOIN UserProfiles tmr on tmr.Id = l.CreatedBy ')+'
		'+IIF(@TMRTeamId IS NULL, '', ' JOIN dbo.ProspectAssignment pa ON pa.Id = l.CreatedByProspectAssignmentId ')+'
		'+IIF(@DistrictId IS NULL, '', ' LEFT JOIN dbo.District d ON d.Id = ap.DistrictId ')+'
		LEFT JOIN dbo.UserProfiles dmo on la.AssignedFieldSaleId=dmo.Id
		LEFT JOIN @TeamFilter tf on tf.Id=dmo.OrganizationId
		'+IIF(@TeamCount=0 OR @Role=1, '', ' JOIN @TeamFilter tff ON tff.Id=la.AssignedFieldSaleTeamId OR tff.Id=dmo.OrganizationId ')+'

	';

	DECLARE @statusQuery NVARCHAR(MAX) =
	'
		'+IIF(@AppointmentStatus IS NULL, '',
			IIF(@AppointmentStatus=7, ' AND ap.Status=1 AND DATEDIFF(MINUTE, ap.MeetDate, GETDATE())<0 AND DATEDIFF(MINUTE, ap.MeetDate, GETDATE())>-60 ',
			IIF(@AppointmentStatus=8, ' AND (l.Status=1 OR l.Status=2 OR l.Status=3) AND DATEDIFF(DAY, ap.MeetDate, GETDATE())>15 ',
			IIF(@AppointmentStatus=9, ' AND ap.Status<4 AND DATEDIFF(DAY, ap.MeetDate, GETDATE())=0 ',
			IIF(@AppointmentStatus=3, ' AND ap.Status=3 AND l.Status<>4 ', ' AND ap.Status = @AppointmentStatus ')))))+'
	';

	DECLARE @whereQuery NVARCHAR(MAX)=
	'
		WHERE (dmo.Id IS NULL OR tf.Id IS NOT NULL)
		'+IIF(@FieldSaleId IS NULL, '', ' AND la.AssignedFieldSaleId=@FieldSaleId ')+'
		'+IIF(@FeedbackCode IS NULL, '', ' AND ap.AppointmentResultCodeId=@FeedbackCode ')+'
		'+IIF(LEN(@statusQuery)=0, '', @statusQuery)+'
		'+IIF(@Range=0, ' AND DATEDIFF(DAY, @MeetDate, ap.MeetDate)=0 ',
			IIF(@MeetDate IS NULL, '', ' AND DATEDIFF(DAY, ap.MeetDate, @MeetDate) <=0 '))+'
		'+IIF(@Range=1 AND @MeetDateTo IS NOT NULL, ' AND DATEDIFF(DAY, ap.MeetDate, @MeetDateTo) >=0 ', '')+'
		'+IIF(@CreatedDate IS NULL, '', ' AND DATEDIFF(DAY, ap.CreatedDate, @CreatedDate)=0 ')+'
		'+IIF(@LeadCode=0, '', ' AND l.LeadCode=@LeadCode ')+'
		'+IIF(@ProvinceId IS NULL, '', ' AND @ProvinceId=pv.Id ')+'
		'+IIF(@DistrictId IS NULL, '', ' AND @DistrictId=d.Id ')+'
		'+IIF(@TMRId IS NULL, '', ' AND @TMRId=tmr.Id ')+'
		'+IIF(@TMRTeamId IS NULL, '', ' AND pa.AssignedTeamId = @TMRTeamId ')+'
	';

	DECLARE @cteQuery NVARCHAR(MAX)=
	'
		WITH cte AS
		(
			'+@selectQuery+'
			'+@joinQuery+'
			'+@whereQuery+'
		)
		SELECT
		ap.Id AppointmentId, ap.SeenByFieldSale, ap.MeetAddress, ap.ProductBudget, ap.Notes, ap.FeedbackNotes, ap.Status AppointmentStatus, ap.MeetDate, FORMAT(ap.MeetDate, ''HH\H'') MeetHour,
		la.Id, la.SuggestedFieldSaleReason, la.UserCheckInId, la.Status LeadAssignmentStatus,
		l.Status LeadStatus, l.UserDefinedFormatCode,
		aprc.ResultCode AppointmentResultCode,
		pr.ProductName,
		pv.ProvinceName,
		d.DistrictName,
		c.Id ContactId, c.FullName, c.DOB ClientDob, c.MaritalStatus ClientMaritalStatus, c.Job ClientJob,
		tmrTeam.OrganizationName TMRTeamName,
		ISNULL(tmr.FullName, tmr.AgentCode) TMR,
		ISNULL(sug_dmo.FullName, sug_dmo.AgentCode) SuggestedDMOName,
		dmo.Id FieldSaleId, ISNULL(dmo.FullName, dmo.AgentCode) DMOName,
		(SELECT COUNT(*) FROM cte) TotalCount
		FROM cte
		JOIN dbo.Lead l ON l.Id = cte.lId
		JOIN dbo.LeadAssignment la on la.Id = cte.laId
		JOIN dbo.Appointment ap on ap.Id = cte.aId
		JOIN dbo.Province pv ON pv.Id = ap.ProvinceId
		JOIN dbo.Contact c ON c.Id = l.ContactId
		JOIN dbo.ProspectAssignment pa ON pa.Id = l.CreatedByProspectAssignmentId
		LEFT JOIN dbo.UserProfiles tmr ON tmr.Id = pa.AssignedAgentId
		LEFT JOIN dbo.Organization tmrTeam ON tmrTeam.Id = pa.AssignedTeamId
		LEFT JOIN dbo.District d ON d.Id = ap.DistrictId
		LEFT JOIN dbo.AppointmentResultCode aprc ON aprc.Id = ap.AppointmentResultCodeId
		LEFT JOIN dbo.Product pr ON pr.Id = l.ProductId -- product có thể null khi hẹn được import từ excel
		LEFT JOIN dbo.UserProfiles dmo ON dmo.Id = la.AssignedFieldSaleId
		LEFT JOIN dbo.UserProfiles sug_dmo ON sug_dmo.Id = la.SuggestedFieldSaleId
		WHERE cte.rn BETWEEN @StartRow AND @EndRow
	';

	PRINT @cteQuery;

	DECLARE @paramQuery NVARCHAR(MAX)=
	'
		@TeamFilter			IdList READONLY,
		@FieldSaleId		UNIQUEIDENTIFIER,
		@TMRTeamId			UNIQUEIDENTIFIER,
		@AppointmentStatus	INT,
		@MeetDate			DATETIME,
		@MeetDateTo			DATETIME,
		@CreatedDate		DATETIME,
		@StartRow			INT,
		@EndRow				INT,
		@LeadCode			INT,
		@TMRId				UNIQUEIDENTIFIER,
		@ProvinceId			UNIQUEIDENTIFIER,
		@DistrictId			UNIQUEIDENTIFIER,
		@FeedbackCode		UNIQUEIDENTIFIER
	';

	EXEC  sp_executesql @cteQuery,
						@paramQuery,
						@TeamFilter,
						@FieldSaleId,
						@TMRTeamId,
						@AppointmentStatus,
						@MeetDate,
						@MeetDateTo,
						@CreatedDate,
						@StartRow,
						@EndRow,
						@LeadCode,
						@TMRId,
						@ProvinceId,
						@DistrictId,
						@FeedbackCode;
END
GO