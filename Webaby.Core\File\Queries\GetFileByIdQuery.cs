﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Data;
using Webaby.Localization;

namespace Webaby.Core.File.Queries
{
    public class GetFileByIdQuery : QueryBase<FileData>
    {
        public Guid Id { get; set; }
    }

    internal class GetFileByIdQueryHandler : QueryHandlerBase<GetFileByIdQuery, FileData>
    {
        public GetFileByIdQueryHandler(IEntitySet entitySet, IRepository repository, IText text, IMapper mapper)
            : base(entitySet, repository, text, mapper) { }

        public override async Task<QueryResult<FileData>> ExecuteAsync(GetFileByIdQuery query)
        {
            var fileEntity = await (from file in EntitySet.Get<FileEntity>()
                                    where file.Id == query.Id
                                    select file).SingleOrDefaultAsync();

            return new QueryResult<FileData>(Mapper.Map<FileData>(fileEntity));
        }
    }
}