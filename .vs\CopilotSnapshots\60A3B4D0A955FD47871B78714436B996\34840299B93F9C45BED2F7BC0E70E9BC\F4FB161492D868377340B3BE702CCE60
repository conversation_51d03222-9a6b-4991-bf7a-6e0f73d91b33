﻿using System;
using System.Linq;
using AutoMapper;
using Webaby.Data;
using Webaby.Localization;
using Webaby;

namespace Webaby.Core.DynamicForm.Queries
    {
    public class GetDynamicFieldInFormByNameQuery : QueryBase<DynamicFieldDefinitionData>
    {
        public Guid? FieldId { get; set; }

        public string Name { get; set; }

        public Guid FormId { get; set; }
    }

    internal class GetDynamicFieldInFormByNameQueryHandler :
        QueryHandlerBase<GetDynamicFieldInFormByNameQuery, DynamicFieldDefinitionData>
    {
        public GetDynamicFieldInFormByNameQueryHandler(
            IEntitySet entitySet,
            IRepository repository,
            IText text,
            IMapper mapper)
          : base(entitySet, repository, text, mapper)
        { }
        public override async Task<QueryResult<DynamicFieldDefinitionData>> ExecuteAsync(GetDynamicFieldInFormByNameQuery query)
    {
            var fieldEntities = await EntitySet.GetAsync<DynamicFieldDefinitionEntity>();
            if (query.FieldId.HasValue)
    {
                fieldEntities = fieldEntities.Where(x => x.Id != query.FieldId);
            }
            var mainQuery = from field in fieldEntities
                where field.DynamicFormId == query.FormId && field.Name == query.Name
                select field;
            return QueryResult.Create(mainQuery, x => Mapper.Map<DynamicFieldDefinitionData>(x));
        }
    }
}

