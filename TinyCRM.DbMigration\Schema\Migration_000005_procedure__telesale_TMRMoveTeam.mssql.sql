
/****** Object:  StoredProcedure [telesale].[TMRMoveTeam]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE PROCEDURE [telesale].[TMRMoveTeam]
	@UitId UNIQUEIDENTIFIER,
	@NewTeam UNIQUEIDENTIFIER,
	@Manager UNIQUEIDENTIFIER
AS
BEGIN
--data của tmr đang mở
SELECT pa.* INTO #AllData FROM dbo.ProspectAssignment pa
JOIN dbo.UserInTeam uit ON uit.UserId=pa.AssignedAgentId
WHERE pa.Status<>3
AND uit.Id=@UitId

--các cuộc gọi của data đang mở
SELECT cc.* INTO #call FROM dbo.ProspectAssignment pa
JOIN dbo.UserInTeam uit ON uit.UserId=pa.AssignedAgentId
JOIN dbo.ContactCall cc ON cc.ProspectAssignmentId=pa.Id
WHERE pa.Status<>3
AND uit.Id=@UitId

--data có lead đang mở
SELECT l.* INTO #lead FROM dbo.ProspectAssignment pa
JOIN dbo.Lead l ON pa.Id=l.CreatedByProspectAssignmentId
JOIN dbo.UserInTeam uit ON uit.UserId=pa.AssignedAgentId
WHERE pa.Status<>3
AND (l.Status = 1 OR l.Status = 2 OR l.Status = 3)
AND uit.Id=@UitId

BEGIN TRY
BEGIN TRANSACTION

--insert data
INSERT INTO dbo.ProspectAssignment
        ( Id ,
          ProspectId ,
          AssignedTeamId ,
          AssignedTeamDate ,
          AssignedAgentId ,
          AssignedAgentDate ,
          UnassignedDate ,
          UnassignedBy ,
          Status ,
          CreatedDate ,
          CreatedBy ,
          ClosedReason ,
          FieldSaleAppointmentId ,
          FieldSaleFeedbackDate ,
          ReminderId ,
          Ignore15DaysRule ,
          CreatedReason ,
          PreviousProspectAssingmentId
        )
SELECT NEWID() Id ,
          ProspectId ,
          @NewTeam AssignedTeamId ,
          GETDATE() AssignedTeamDate ,
          AssignedAgentId ,
          AssignedAgentDate ,
          UnassignedDate ,
          UnassignedBy ,
          Status ,
          GETDATE() CreatedDate ,
          @Manager CreatedBy ,
          ClosedReason ,
          FieldSaleAppointmentId ,
          FieldSaleFeedbackDate ,
          ReminderId ,
          Ignore15DaysRule ,
          CreatedReason ,
          Id PreviousProspectAssingmentId
FROM #AllData
--đóng pa cũ
UPDATE pa SET pa.Status=3, pa.UnassignedBy=@Manager, pa.UnassignedDate = GETDATE(), pa.ClosedReason=8 FROM dbo.ProspectAssignment pa JOIN #AllData temp ON temp.Id=pa.Id
--cập nhật lại prospect
UPDATE p SET p.CurrentAssignmentId=cur.Id FROM dbo.ProspectAssignment prev JOIN #AllData a ON a.Id=prev.Id
JOIN dbo.ProspectAssignment cur ON cur.PreviousProspectAssingmentId=prev.Id
JOIN dbo.Prospect p ON p.Id=cur.ProspectId
--cập nhật lại các notifi
UPDATE rem SET rem.ReferenceObjectId=cur.Id FROM dbo.Reminder rem
JOIN dbo.ProspectAssignment prev ON prev.Id=rem.ReferenceObjectId
JOIN #AllData a ON a.Id=prev.Id
JOIN dbo.ProspectAssignment cur ON cur.PreviousProspectAssingmentId=prev.Id
--cập nhật lead
UPDATE l SET l.CreatedByProspectAssignmentId=pa.Id FROM #lead ltemp
JOIN dbo.ProspectAssignment pa ON pa.PreviousProspectAssingmentId=ltemp.CreatedByProspectAssignmentId
JOIN dbo.Lead l ON l.Id=ltemp.Id
--cập nhật contact call
UPDATE cc SET cc.ProspectAssignmentId=pa.Id FROM #call ctemp
JOIN dbo.ProspectAssignment pa ON pa.PreviousProspectAssingmentId=ctemp.ProspectAssignmentId
JOIN dbo.ContactCall cc ON cc.Id=ctemp.Id

COMMIT TRANSACTION
END TRY
BEGIN CATCH
ROLLBACK TRANSACTION
RAISERROR(N'Lỗi xảy ra trong quá trình xử lý', 15, 10)
END CATCH

END
--FROM #AllData
--UPDATE pa SET pa.Status=3, pa.UnassignedBy=@Manager, pa.UnassignedDate = GETDATE(), pa.ClosedReason=8 FROM dbo.ProspectAssignment pa JOIN #AllData temp ON temp.Id=pa.Id
--UPDATE l SET l.CreatedByProspectAssignmentId=pa.Id FROM #lead ltemp
--JOIN dbo.ProspectAssignment pa ON pa.PreviousProspectAssingmentId=ltemp.CreatedByProspectAssignmentId
--JOIN dbo.Lead l ON l.Id=ltemp.Id
--UPDATE cc SET cc.ProspectAssignmentId=pa.Id FROM #call ctemp
--JOIN dbo.ProspectAssignment pa ON pa.PreviousProspectAssingmentId=ctemp.ProspectAssignmentId
--JOIN dbo.ContactCall cc ON cc.Id=ctemp.Id
--END
GO