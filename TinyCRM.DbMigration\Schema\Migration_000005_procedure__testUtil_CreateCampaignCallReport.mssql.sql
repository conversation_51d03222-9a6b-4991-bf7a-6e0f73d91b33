
/****** Object:  StoredProcedure [testUtil].[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>allReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [testUtil].[C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>allReport]  @campaignId UNIQUEIDENTIFIER
AS
BEGIN
	
 

	-- Using modulo for weighted distribution
INSERT INTO dbo.ContactCall(Id, ProspectAssignmentId, CallResultId, Duration, CreatedDate, CreatedBy, PhoneNumber, Notes, ProductId, ProductBudget, ContactId, CallId, Inum, CampaignId, CreatedByTeamId)
SELECT TOP(900)
    NEWID(),
    pa.Id,
   CASE 
        -- Use ROW_NUMBER to create a deterministic random value per row
        WHEN ROW_NUMBER() OVER (ORDER BY pa.Id) % 100 < 30 THEN 
            (SELECT TOP 1 Id FROM dbo.CallResult WHERE ResultCodeSuiteId = '60B282A7-C04C-40DA-97E1-5304256ABE07' ORDER BY Id)
        WHEN ROW_NUMBER() OVER (ORDER BY pa.Id) % 100 < 55 THEN 
            (SELECT TOP 1 Id FROM dbo.CallResult WHERE ResultCodeSuiteId = '60B282A7-C04C-40DA-97E1-5304256ABE07' AND Id > 
                (SELECT MIN(Id) FROM dbo.CallResult WHERE ResultCodeSuiteId = '60B282A7-C04C-40DA-97E1-5304256ABE07') ORDER BY Id)
        WHEN ROW_NUMBER() OVER (ORDER BY pa.Id) % 100 < 75 THEN 
            (SELECT TOP 1 Id FROM (SELECT TOP 3 Id FROM dbo.CallResult WHERE ResultCodeSuiteId = '60B282A7-C04C-40DA-97E1-5304256ABE07' ORDER BY Id) AS T ORDER BY Id DESC)
        WHEN ROW_NUMBER() OVER (ORDER BY pa.Id) % 100 < 90 THEN 
            (SELECT TOP 1 Id FROM (SELECT TOP 4 Id FROM dbo.CallResult WHERE ResultCodeSuiteId = '60B282A7-C04C-40DA-97E1-5304256ABE07' ORDER BY Id) AS T ORDER BY Id DESC)
        ELSE 
            (SELECT TOP 1 Id FROM dbo.CallResult WHERE ResultCodeSuiteId = '60B282A7-C04C-40DA-97E1-5304256ABE07' ORDER BY NEWID())
    END,
    CAST(200 + ABS(CHECKSUM(NEWID())) % 2801 AS VARCHAR), -- Random between 200 and 3000
    GETDATE(), 
    pa.AssignedAgentId,
    c.Phone1,
    NULL,
    NULL,
    NULL,
    pa.CustomerId,
    NULL,
    'Te44A' + 
    RIGHT('00000' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR), 5) + -- Random 5 digits (zero-padded)
    FORMAT(GETDATE(), 'ddMMyyyy') + -- Current date in format ddMMyyyy
    RIGHT('0000000' + CAST(ABS(CHECKSUM(NEWID())) % 10000000 AS VARCHAR), 7), -- Random 7 digits (zero-padded)
    @campaignId,
    pa.AssignedTeamId 
FROM dbo.ProspectAssignment pa
JOIN dbo.Customer c ON c.Id = pa.CustomerId
 WHERE pa.CampaignId = @campaignId
  
   UPDATE pa 
   SET LastCallId = cc.Id, pa.CallResultId = cc.CallResultId
   FROM dbo.ProspectAssignment pa
   JOIN	 dbo.ContactCall cc ON pa.Id = cc.ProspectAssignmentId
    WHERE pa.CampaignId = @campaignId AND pa.CallResultId IS NULL

	UPDATE p
	SET p.LastCallId = cc.Id, p.CallResultId = cc.CallResultId
   FROM dbo.ProspectAssignment pa
   JOIN dbo.Prospect p ON p.Id = pa.ProspectId
   JOIN	 dbo.ContactCall cc ON pa.Id = cc.ProspectAssignmentId
    WHERE pa.CampaignId = @campaignId AND p.CallResultId IS NULL
END
GO