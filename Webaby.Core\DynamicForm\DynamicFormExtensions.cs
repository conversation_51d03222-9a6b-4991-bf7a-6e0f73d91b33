﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Collections;
using System.Collections.Specialized;
using System.Globalization;
using System.Text.RegularExpressions;
using Webaby.Core.DynamicForm.Commands;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Data;
using Webaby.EntityData;
using Webaby.Localization;
using Webaby.Web;

namespace Webaby.Core.DynamicForm
{
    public static class DynamicFormExtensions
    {
        public static bool TryMapping(this IEnumerable<DynamicFieldValueInfo> dynamicFieldList,
            NameValueCollection values,
            IFormFileCollection files,
            string prefix,
            IQueryExecutor queryExecutor, IText text,
            out Dictionary<string, List<string>> validateErrors)
        {
            validateErrors = new Dictionary<string, List<string>>();
            if (dynamicFieldList.Any())
            {
                foreach (var dynamicFieldValue in dynamicFieldList.Where(x => x.FieldType != FieldType.Calculated))
                {
                    var dataType = dynamicFieldValue.DataType.ToType();
                    if (dataType != null)
                    {
                        var inputName = prefix.IsNotNullOrEmpty() ? string.Format("{0}.{1}", prefix, dynamicFieldValue.Name) : dynamicFieldValue.Name;
                        var fieldValue = string.Empty;
                        var isCollection = dataType.IsCollection();
                        var baseType = dataType.BaseType();
                        Dictionary<string, List<string>> validate;
                        var hasValue = false;
                        if (baseType.IsEntityData())
                        {
                            if (isCollection)
                            {
                                var collectionEntityInstance = (ICollection<IEntityData>)(((IList)Activator.CreateInstance(dataType)).Cast<IEntityData>().ToList());
                                if (collectionEntityInstance.TryMapping(inputName, baseType, values, files, out validate))
                                {
                                    dynamicFieldValue.ObjectValue = collectionEntityInstance;
                                    dynamicFieldValue.Value = collectionEntityInstance.GetValue();

                                    if (collectionEntityInstance.Count > 0)
                                    {
                                        hasValue = true;
                                    }
                                }
                                else
                                {
                                    validateErrors.AddRange(validate);
                                }
                            }
                            else
                            {
                                HttpPostedFileCollection httpPostedFileCollection = null;
                                if (files != null)
                                {
                                    httpPostedFileCollection = new HttpPostedFileCollection(files);
                                }

                                var entityInstance = (IEntityData)Activator.CreateInstance(dataType);
                                if (dataType.IsUserDefinedTableGridData())
                                {
                                    IUserDefinedTableGridData userDefinedTableGridData = (IUserDefinedTableGridData)entityInstance;
                                    userDefinedTableGridData.OnDynamicFieldId = dynamicFieldValue.FieldId;
                                }
                                if (entityInstance.TryMapping(inputName, values, httpPostedFileCollection, out validate))
                                {
                                    dynamicFieldValue.ObjectValue = entityInstance;
                                    dynamicFieldValue.Value = entityInstance.GetValue();

                                    if (entityInstance.Id != Guid.Empty)
                                    {
                                        hasValue = true;
                                    }
                                }
                                else
                                {
                                    validateErrors.AddRange(validate);
                                }
                            }
                        }
                        else
                        {
                            var strValue = values[inputName];
                            if (!strValue.IsNullOrEmpty())
                            {
                                hasValue = true;

                                if (isCollection)
                                {
                                    List<string> splitValues = new List<string>();
                                    bool fromJson = false;
                                    try
                                    {
                                        splitValues = JsonConvert.DeserializeObject<List<string>>(strValue);
                                        fromJson = true;
                                    }
                                    catch (Exception) { }

                                    if (!fromJson)
                                    {
                                        string[] splitValue = strValue.Contains(",") ? strValue.Split(',') : new string[1] { strValue };
                                        splitValues = splitValue.ToList();
                                    }

                                    List<object> convertValueList = new List<object>();
                                    foreach (var val in splitValues)
                                    {
                                        try
                                        {
                                            var typeConverter = baseType.GetTypeConverter(string.IsNullOrEmpty(val));
                                            var convertValue = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, val);
                                            if (convertValue != null)
                                            {
                                                convertValueList.Add(convertValue);
                                            }
                                        }
                                        catch { }
                                    }
                                    dynamicFieldValue.ObjectValue = convertValueList.Any() ?
                                        (convertValueList.Count > 1 ?
                                            convertValueList :
                                            convertValueList[0]
                                         ) : null;

                                    dynamicFieldValue.Value = convertValueList.Any() ?
                                        (convertValueList.Count > 1 ?
                                            JsonConvert.SerializeObject(convertValueList) :
                                            convertValueList[0].ToString()) : null;
                                }
                                else
                                {
                                    try
                                    {
                                        var typeConverter = baseType.GetTypeConverter(string.IsNullOrEmpty(strValue));
                                        var convertValue = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, strValue);

                                        dynamicFieldValue.ObjectValue = convertValue;
                                        dynamicFieldValue.Value = strValue;
                                        if (convertValue != null)
                                        {
                                            if (convertValue is DateTime)
                                            {
                                                var dateTime = (DateTime)convertValue;
                                                dynamicFieldValue.Value = dateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                                            }
                                            else
                                            {
                                                dynamicFieldValue.Value = convertValue.ToString();
                                            }
                                        }
                                    }
                                    catch { }
                                }

                                if (dynamicFieldValue.ObjectValue == null)
                                {
                                    validateErrors.Add(inputName, new List<string> { text["Định dạng \"{0}\" không hợp lệ", dynamicFieldValue.DisplayName] });
                                }
                            }

                            if (!hasValue && !dynamicFieldValue.IsRequired)
                            {
                                dynamicFieldValue.Value = null;
                            }
                        }

                        if (!hasValue && dynamicFieldValue.IsRequired)
                        {
                            validateErrors.Add(inputName, new List<string> { text["Vui lòng nhập \"{0}\"", dynamicFieldValue.DisplayName] });
                        }

                        var fieldValidations = dynamicFieldValue.GetValidation();
                        foreach (var itm in fieldValidations)
                        {
                            if (!Validate(dynamicFieldValue, itm))
                            {
                                validateErrors.Add(dynamicFieldValue.Name, new List<string> { itm.ValidationMessage });
                            }
                        }
                    }
                }
                if (dynamicFieldList.Any(x => x.FieldType == FieldType.Calculated))
                {
                    var formId = dynamicFieldList.First().FormId;
                    dynamicFieldList = queryExecutor.ExecuteManyAsync(new CalculateDynamicFieldQuery
                    {
                        DynamicFormId = formId,
                        DynamicFieldValueInfo = dynamicFieldList,
                        FormValues = values
                    }).GetAwaiter().GetResult();
                }

                // Validate value cho các Field Calculated
                foreach (var dynamicFieldValue in dynamicFieldList.Where(x => x.FieldType == FieldType.Calculated))
                {
                    var fieldValidations = dynamicFieldValue.GetValidation();
                    if (fieldValidations != null && fieldValidations.Count() > 0)
                    {
                        var dataType = dynamicFieldValue.DataType.ToType();
                        var baseType = dataType.BaseType();

                        var typeConverter = baseType.GetTypeConverter(string.IsNullOrEmpty(dynamicFieldValue.Value));
                        var convertValue = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, dynamicFieldValue.Value);

                        dynamicFieldValue.ObjectValue = convertValue;
                    }

                    foreach (var itm in fieldValidations)
                    {
                        if (!Validate(dynamicFieldValue, itm))
                        {
                            validateErrors.Add(dynamicFieldValue.Name, new List<string> { itm.ValidationMessage });
                        }
                    }
                }

                foreach (var dynamicFieldValue in dynamicFieldList)
                {
                    var fieldCustomDependencies = dynamicFieldValue.GetCustomDependencies();
                    foreach (var fieldCustomDependency in fieldCustomDependencies)
                    {
                        if (fieldCustomDependency.ValidationType.HasValue)
                        {
                            if (!ValidateCustomDependency(dynamicFieldValue, dynamicFieldList, fieldCustomDependency))
                            {
                                validateErrors.Add(dynamicFieldValue.Name, new List<string> { fieldCustomDependency.ValidationMessage });
                            }
                        }
                    }
                }
            }
            return !validateErrors.Any();
        }

        public static async Task Save(this IEnumerable<DynamicFieldValueInfo> dynamicFieldList,
            Guid? referenceObjectId,
            string referenceObjectType,
            Guid? auditSessionId, bool trackAudit,
            ICommandExecutor commandExecutor)
        {
            dynamicFieldList.SaveEntityDataFields(referenceObjectId);

            if (dynamicFieldList.Any())
            {
                var dynamicFormValueId = dynamicFieldList.First().ObjectId;
                var dynamicFormId = dynamicFieldList.First().FormId;
                await commandExecutor.ExecuteAsync(new SubmitDynamicFormValueCommand
                {
                    DynamicFormValueId = dynamicFormValueId,
                    DynamicFormId = dynamicFormId,
                    DynamicFieldValues = dynamicFieldList.Where(dfd => dfd.FieldType != FieldType.Static).ToList(),
                    ReferenceObjectId = referenceObjectId,
                    ReferenceObjectType = referenceObjectType,
                    TrackAudit = trackAudit,
                    AuditSessionId = auditSessionId
                });
            }
        }

        public static async Task SaveEntityDataFields(this IEnumerable<DynamicFieldValueInfo> dynamicFieldList, Guid? referenceObjectId)
        {
            foreach (var dynamicFieldValue in dynamicFieldList)
            {
                if (dynamicFieldValue.ObjectValue != null)
                {
                    var dataType = dynamicFieldValue.ObjectValue.GetType();
                    var isCollection = dataType.IsCollection();
                    var baseType = dataType.BaseType();
                    if (baseType.IsEntityData())
                    {
                        if (isCollection)
                        {
                            await ((ICollection<IEntityData>)dynamicFieldValue.ObjectValue).SaveAsync();
                        }
                        else
                        {
                            IEntityData entityData = ((IEntityData)dynamicFieldValue.ObjectValue);
                            if (dataType.IsGridData() || dataType.IsUserDefinedTableGridData())
                            {
                                if (entityData.Id != Guid.Empty)
                                {
                                    dynamicFieldValue.Id = entityData.Id;
                                    dynamicFieldValue.Value = entityData.Id.ToString();
                                }
                                else
                                {
                                    if (dynamicFieldValue.Id.IsNullOrEmpty())
                                    {
                                        dynamicFieldValue.Id = Guid.NewGuid();
                                    }
                                    entityData.Id = dynamicFieldValue.Id.Value;
                                    dynamicFieldValue.Value = dynamicFieldValue.Id.Value.ToString();
                                }

                                if (dataType.IsUserDefinedTableGridData())
                                {
                                    IUserDefinedTableGridData userDefinedTableGridData = ((IUserDefinedTableGridData)dynamicFieldValue.ObjectValue);
                                    userDefinedTableGridData.OnDynamicFieldId = dynamicFieldValue.FieldId;
                                    if (userDefinedTableGridData.DynamicDefinedTableSchemaId == Guid.Empty && dynamicFieldValue.DynamicDefinedTableSchemaId.IsNotNullOrEmpty())
                                    {
                                        userDefinedTableGridData.DynamicDefinedTableSchemaId = dynamicFieldValue.DynamicDefinedTableSchemaId.Value;
                                    }
                                    if (referenceObjectId.IsNotNullOrEmpty())
                                    {
                                        userDefinedTableGridData.ReferenceObjectId = referenceObjectId.Value;
                                    }
                                }
                            }
                            await entityData.SaveAsync();
                        }
                    }
                }
                else
                {
                    var dataType = dynamicFieldValue.DataType.ToType();
                    if (dataType.IsGridData() || dataType.IsUserDefinedTableGridData())
                    {
                        if (dynamicFieldValue.Id.IsNullOrEmpty())
                        {
                            dynamicFieldValue.Id = Guid.NewGuid();
                        }
                        dynamicFieldValue.Value = dynamicFieldValue.Id.Value.ToString();
                    }
                }
                Dictionary<string, string> fieldMessages = new Dictionary<string, string>();
                dynamicFieldValue.BusinessValidationResult = BusinessValidateField(dynamicFieldValue, out fieldMessages);
            }
        }

        public static bool BusinessValidate(this IEnumerable<DynamicFieldValueInfo> dynamicFieldList,
            out Dictionary<string, string> businessValidateErrors)
        {
            businessValidateErrors = new Dictionary<string, string>();
            var validationExpressionRegex = new Regex(@"((equal|differ|range|in|notin|regex)(\[|\()(.*)(\]|\)))");
            foreach (var dynamicFieldValue in dynamicFieldList)
            {
                var validations = dynamicFieldValue.GetBusinessValidation();
                if (validations.Any())
                {
                    foreach (var itm in validations)
                    {
                        if (!Validate(dynamicFieldValue, itm))
                        {
                            businessValidateErrors.Add(dynamicFieldValue.Name, itm.ValidationMessage);
                        }
                    }
                }
            }
            return !businessValidateErrors.Any();
        }

        public static bool BusinessValidateField(DynamicFieldValueInfo dynamicFieldValue, out Dictionary<string, string> businessValidateErrors)
        {
            businessValidateErrors = new Dictionary<string, string>();
            var validations = dynamicFieldValue.GetBusinessValidation();
            if (validations.Any())
            {
                foreach (var itm in validations)
                {
                    if (!Validate(dynamicFieldValue, itm))
                    {
                        businessValidateErrors.Add(dynamicFieldValue.Name, itm.ValidationMessage);
                    }
                }
            }
            return !businessValidateErrors.Any();
        }

        private static bool Validate(DynamicFieldValueInfo dynamicFieldValue, DynamicValidationInfo vl)
        {
            if (dynamicFieldValue.ObjectValue != null)
            {
                var dataType = dynamicFieldValue.ObjectValue.GetType();
                var baseType = dataType.BaseType();
                bool isValid = true;
                if (baseType.IsEntityData())
                {
                    if (dataType.IsCollection())
                    {
                        if (!((ICollection<IEntityData>)dynamicFieldValue.ObjectValue).Evaluate(vl.Validation))
                        {
                            isValid = false;
                        }
                    }
                    else
                    {
                        if (!((IEntityData)dynamicFieldValue.ObjectValue).ExecuteExpression(vl.Validation))
                        {
                            isValid = false;
                        }
                    }
                }
                else
                {
                    if (dataType.IsCollection())
                    {
                        var lst = (List<object>)dynamicFieldValue.ObjectValue;
                        foreach (var obj in lst)
                        {
                            if (!obj.Evaluate(vl.Validation))
                            {
                                isValid = false;
                            }
                        }
                    }
                    else
                    {
                        if (!dynamicFieldValue.ObjectValue.Evaluate(vl.Validation))
                        {
                            isValid = false;
                        }
                    }
                }
                return isValid;
            }
            return true;
        }

        private static bool ValidateCustomDependency(DynamicFieldValueInfo dynamicFieldValue, IEnumerable<DynamicFieldValueInfo> dynamicFieldList, DynamicCustomDependency customDependency)
        {
            var dependencyField = dynamicFieldList.Where(dfd => dfd.Name == customDependency.ByDynamicFieldName).FirstOrDefault();
            if (dependencyField != null)
            {
                string expression = string.Format("Input1 {0} Input2", customDependency.ValidationOperator);
                bool matchExpression = expression.EvaluateBooleanExpression(dependencyField.ObjectValue ?? "", customDependency.ValidationValue);
                if (matchExpression)
                {
                    if (customDependency.ValidationType.Value == DynamicCustomDependencyValidationType.Required)
                    {
                        Guid entityDataId = Guid.Empty;
                        if (dynamicFieldValue.Value.IsNotNullOrEmpty() && Guid.TryParse(dynamicFieldValue.Value, out entityDataId))
                        {
                            if (entityDataId == Guid.Empty)
                            {
                                return false;
                            }
                        }

                        if (dynamicFieldValue.Value.IsNullOrEmpty() || dynamicFieldValue.ObjectValue == null)
                        {
                            return false;
                        }
                    }
                }
            }

            return true;
        }

        public static string Clone(this DynamicFieldValueInfo dynamicFieldInfo, out IEnumerable<IEntity> referenceEnities)
        {
            referenceEnities = new List<IEntity>();

            var dataType = dynamicFieldInfo.DataType.ToType();
            if (dataType != null)
            {
                if (dataType.IsGridData())
                {
                    if (dynamicFieldInfo.Value.IsNotNullOrEmpty())
                    {
                        Guid sourceValueGroupId = Guid.Empty;
                        if (Guid.TryParse(dynamicFieldInfo.Value, out sourceValueGroupId))
                        {
                            Guid newValueGroupId = Guid.NewGuid();

                            var entityInstance = (IGridData)Activator.CreateInstance(dataType);
                            referenceEnities = entityInstance.CloneReferenceEntities(sourceValueGroupId, newValueGroupId);
                            return newValueGroupId.ToString();
                        }
                    }
                }
            }

            return dynamicFieldInfo.Value;
        }

        public static DynamicFieldValueInfo PasteDynamicFieldValue(this DynamicFieldValueInfo dynamicFieldInfo)
        {
            var dataType = dynamicFieldInfo.DataType.ToType();
            if (dataType != null)
            {
                if (dataType.IsGridData())
                {
                    if (dynamicFieldInfo.Value.IsNotNullOrEmpty())
                    {
                        var entityInstance = dynamicFieldInfo.ObjectValue as IGridData;
                        if (entityInstance != null)
                        {
                            var newInstanceValue = entityInstance.CopyDynamicFieldValue();
                            dynamicFieldInfo.ObjectValue = newInstanceValue;
                            dynamicFieldInfo.Value = newInstanceValue.GetValue();
                        }
                    }
                }
            }

            return dynamicFieldInfo;
        }

        public static IEnumerable<DynamicFieldValueInfo> CopyDynamicFormValue(this IEnumerable<DynamicFieldValueInfo> dynamicFieldList, NameValueCollection values, IFormFileCollection files, string prefix, IQueryExecutor queryExecutor)
        {
            if (dynamicFieldList.Any())
            {
                foreach (var dynamicFieldValue in dynamicFieldList.Where(x => x.FieldType != FieldType.Calculated))
                {
                    var dataType = dynamicFieldValue.DataType.ToType();
                    if (dataType != null)
                    {
                        var inputName = prefix.IsNotNullOrEmpty() ? string.Format("{0}.{1}", prefix, dynamicFieldValue.Name) : dynamicFieldValue.Name;
                        var fieldValue = string.Empty;
                        var isCollection = dataType.IsCollection();
                        var baseType = dataType.BaseType();
                        Dictionary<string, List<string>> validate;
                        if (baseType.IsEntityData())
                        {
                            if (isCollection)
                            {
                                var collectionEntityInstance = (ICollection<IEntityData>)(((IList)Activator.CreateInstance(dataType)).Cast<IEntityData>().ToList());
                                if (collectionEntityInstance.TryMapping(inputName, baseType, values, files, out validate))
                                {
                                    dynamicFieldValue.ObjectValue = collectionEntityInstance;
                                    dynamicFieldValue.Value = collectionEntityInstance.GetValue();
                                }
                            }
                            else
                            {
                                var entityInstance = (IEntityData)Activator.CreateInstance(dataType);
                                if (entityInstance.TryMapping(inputName, values, new HttpPostedFileCollection(files), out validate))
                                {
                                    if (dataType.IsGridData())
                                    {
                                        entityInstance = ((IGridData)entityInstance).CopyDynamicFieldValue();
                                    }
                                    dynamicFieldValue.ObjectValue = entityInstance;
                                    dynamicFieldValue.Value = entityInstance.GetValue();
                                }
                            }
                        }
                        else
                        {
                            var strValue = values[inputName];
                            if (!strValue.IsNullOrEmpty())
                            {
                                var splitValue = strValue.Contains(",") ? strValue.Split(',') : new string[1] { strValue };
                                List<object> convertValueList = new List<object>();
                                foreach (var val in splitValue)
                                {
                                    object convertValue = baseType.GetTypeConverter(string.IsNullOrEmpty(val))
                                        .ConvertFromString(null, CultureInfo.CurrentCulture, val);
                                    if (convertValue != null)
                                    {
                                        convertValueList.Add(convertValue);
                                    }
                                }
                                dynamicFieldValue.ObjectValue = convertValueList.Any() ?
                                    (convertValueList.Count > 1 ?
                                        convertValueList :
                                        convertValueList[0]
                                     ) : null;

                                dynamicFieldValue.Value = convertValueList.Any() ?
                                    (convertValueList.Count > 1 ?
                                        JsonConvert.SerializeObject(convertValueList) :
                                        convertValueList[0].ToString()) : null;
                            }
                        }
                    }
                }
                if (dynamicFieldList.Any(x => x.FieldType == FieldType.Calculated))
                {
                    var formId = dynamicFieldList.First().FormId;
                    dynamicFieldList = queryExecutor.ExecuteManyAsync(new CalculateDynamicFieldQuery
                    {
                        DynamicFormId = formId,
                        DynamicFieldValueInfo = dynamicFieldList,
                        FormValues = values
                    }).GetAwaiter().GetResult();
                }
            }
            return dynamicFieldList;
        }

        public static void CopyValueTo(this DynamicFieldValueInfo fromDynamicFieldInfo, DynamicFieldValueInfo toDynamicFieldInfo)
        {
            var fromDataType = fromDynamicFieldInfo.DataType.ToType();
            var toDataType = toDynamicFieldInfo.DataType.ToType();
            if (fromDataType == toDataType)
            {
                if (toDataType.IsUserDefinedTableGridData())
                {
                    if (fromDynamicFieldInfo.Value.IsNotNullOrEmpty())
                    {
                        var entityInstance = (IUserDefinedTableGridData)Activator.CreateInstance(toDataType);
                        if (entityInstance != null)
                        {
                            entityInstance.Id = fromDynamicFieldInfo.Id.Value;
                            entityInstance.DynamicDefinedTableSchemaId = fromDynamicFieldInfo.DynamicDefinedTableSchemaId.Value;
                            entityInstance.LoadAsync().GetAwaiter().GetResult();

                            var newInstanceValue = entityInstance.CopyDynamicFieldValueByName(toDynamicFieldInfo.DynamicDefinedTableSchemaId.Value, toDynamicFieldInfo.Id, toDynamicFieldInfo.Value);
                            newInstanceValue.Id = toDynamicFieldInfo.Id.Value;
                            toDynamicFieldInfo.ObjectValue = newInstanceValue;
                            toDynamicFieldInfo.Value = newInstanceValue.GetValue();
                        }
                    }
                }
                else
                {
                    toDynamicFieldInfo.Value = fromDynamicFieldInfo.Value;
                }
            }
        }

        public static void CopyValuesTo(this IEnumerable<DynamicFieldValueInfo> fromDynamicFieldList, IEnumerable<DynamicFieldValueInfo> toDynamicFieldList)
        {
            foreach (var toDynamicFieldValue in toDynamicFieldList)
            {
                if (toDynamicFieldValue.FieldType != FieldType.Static && toDynamicFieldValue.FieldType != FieldType.Calculated && toDynamicFieldValue.FieldType != FieldType.GlobalCalculated)
                {
                    var fromDynamicFieldValue = fromDynamicFieldList.Where(df => df.Name.IsEqualIgnoreCase(toDynamicFieldValue.Name) && df.DataType.IsEqualIgnoreCase(toDynamicFieldValue.DataType)).SingleOrDefault();
                    if (fromDynamicFieldValue != null)
                    {
                        fromDynamicFieldValue.CopyValueTo(toDynamicFieldValue);
                    }
                }
            }
        }
    }
}