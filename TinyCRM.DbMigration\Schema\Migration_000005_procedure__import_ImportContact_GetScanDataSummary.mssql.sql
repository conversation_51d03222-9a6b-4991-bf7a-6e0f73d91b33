
/****** Object:  StoredProcedure [import].[ImportContact_GetScanDataSummary]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [import].[ImportContact_GetScanDataSummary]
	@ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
	SET NOCOUNT ON;
	SELECT @ImportSessionId ImportSessionId,
	COUNT(*) Total,
	SUM(IIF(ErrorCode = 'InvalidData',1,0)) InvalidData,
	SUM(IIF(ErrorCode = 'DuplicateInternal',1,0)) DuplicateInternal,
	SUM(IIF(ErrorCode = 'NewContact',1,0)) NewContact,
	SUM(IIF(ErrorCode = 'NotInCampaign',1,0)) NotInCampaign,
	SUM(IIF(ErrorCode = 'Campaign_NotAssigned',1,0)) Campaign_NotAssigned,
	SUM(IIF(ErrorCode = 'Team_NotCall',1,0)) Team_NotCall,
	SUM(IIF(ErrorCode = 'Agent_NotCall',1,0)) Agent_NotCall,
	SUM(IIF(ErrorCode = 'Agent_SimpleResult',1,0)) Agent_SimpleResult,
	SUM(IIF(ErrorCode = 'Team_SimpleResult',1,0)) Team_SimpleResult,
	SUM(IIF(ErrorCode = 'Agent_DoneLost',1,0)) Agent_DoneLost,
	SUM(IIF(ErrorCode = 'Team_DoneLost',1,0)) Team_DoneLost,
	SUM(IIF(ErrorCode = 'Agent_Appointment',1,0)) Agent_Appointment,
	SUM(IIF(ErrorCode = 'Team_Appointment',1,0)) Team_Appointment,
	SUM(IIF(ErrorCode = 'Agent_DoneSuccess',1,0)) Agent_DoneSuccess,
	SUM(IIF(ErrorCode = 'Team_DoneSuccess',1,0)) Team_DoneSuccess,
	SUM(IIF(ErrorCode = 'ExistInBasket',1,0)) ExistInBasket,
	SUM(IIF((ISNULL(WarningCode,0)&1)>0,1,0)) ProvinceInvalid,
	SUM(IIF((ISNULL(WarningCode,0)&2)>0,1,0)) DOBInvalid
	FROM import.ContactRaw 
	WHERE ImportSessionId=@ImportSessionId
END
GO