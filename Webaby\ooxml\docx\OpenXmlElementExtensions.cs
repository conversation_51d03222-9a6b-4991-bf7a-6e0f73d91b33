﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Webaby.OOXML.docx
{
    public static class OpenXmlElementExtensions
    {
        public static IEnumerable<T> FindChildDeep<T>(this OpenXmlElement current, Func<T, bool> cond) where T : OpenXmlElement
        {
            return current.Descendants<T>().Where(cond);
        }
        public static T CurrentOrBackParentUntil<T>(this OpenXmlElement current, Func<T, bool> cond) where T : OpenXmlElement
        {
            if (current as T != null && cond(current as T))
            {
                return current as T;
            }
            while (true)
            {
                T result = current.Parent as T;
                if (result != null && cond(result))
                {
                    return result;
                }
                if (current.Parent == null)
                {
                    return null;
                }
                current = current.Parent;
            }
        }

        public static IEnumerable<string> VariableList(this OpenXmlElement container, string prefix)
        {
            prefix = string.IsNullOrWhiteSpace(prefix) ? "" : Regex.Escape(prefix + ".");
            var regex = new Regex(@"^\s*MERGEFIELD\s+(" + prefix + @"[_a-zA-Z][^\s]*)\s+\\\*\sMERGEFORMAT\s*$");
            var regex2 = new Regex(@"^\s*MERGEFIELD\s+(" + prefix + @"[_a-zA-Z][^\s]*)\s*$");
            var regPic = new Regex("(" + prefix + @"[\W\w]+)");

            var simple = container.FindChildDeep<SimpleField>(x => x.Instruction != null && x.Instruction.Value != null && regex.IsMatch(x.Instruction.Value)).Select(x => regex.Match(x.Instruction.Value).Groups[1].Value).Distinct();
            var simple2 = container.FindChildDeep<SimpleField>(x => x.Instruction != null && x.Instruction.Value != null && regex2.IsMatch(x.Instruction.Value)).Select(x => regex2.Match(x.Instruction.Value).Groups[1].Value).Distinct();

            var complex = container.FindChildDeep<FieldCode>(x => x.InnerText != null && regex.IsMatch(x.InnerText)).Select(x => regex.Match(x.InnerText).Groups[1].Value).Distinct();
            var complex2 = container.FindChildDeep<FieldCode>(x => x.InnerText != null && regex2.IsMatch(x.InnerText)).Select(x => regex2.Match(x.InnerText).Groups[1].Value).Distinct();

            var docPr = container.FindChildDeep<DocumentFormat.OpenXml.Drawing.Wordprocessing.DocProperties>(x => x.Description != null && x.Description.Value != null && regPic.IsMatch(x.Description.Value)).Select(x => regPic.Match(x.Description.Value).Groups[1].Value).Distinct();
            var result = simple.Concat(simple2).Concat(complex).Concat(complex2).Concat(docPr).Distinct();
            return result;
        }

        public static void InsertBefore(this OpenXmlElement container, string text, OpenXmlElement refElm, Func<string, OpenXmlElement> handle)
        {
            var split_newline = text.Split(new string[] { Environment.NewLine }, StringSplitOptions.None);
            Break lastBr = null;
            foreach (var n_item in split_newline)
            {
                var split_tab = n_item.Split('\t');
                TabChar lastTab = null;
                foreach (var t_item in split_tab)
                {
                    container.InsertBefore(handle.Invoke(t_item), refElm);
                    lastTab = container.InsertBefore(new TabChar(), refElm);
                }
                container.RemoveChild(lastTab);
                lastBr = container.InsertBefore(new Break(), refElm);
            }
            container.RemoveChild(lastBr);
        }

        public static void Injection(this OpenXmlElement container, string variable, string replacement)
        {
            var regex = new Regex(@"^\s*MERGEFIELD\s+(" + Regex.Escape(variable) + @")\s*(\s+\\\*\s+MERGEFORMAT\s*)?$");
            var simpleList = container.FindChildDeep<SimpleField>(x => x.Instruction != null && x.Instruction.Value != null && regex.IsMatch(x.Instruction.Value));
            foreach (var simple in simpleList)
            {
                InsertBefore(simple.Parent, replacement, simple, x => new Run(new Text(x)));
                simple.Remove();
            }
            var complexList = container.FindChildDeep<FieldCode>(x => x.InnerText != null && regex.IsMatch(x.InnerText));
            foreach (var complex in complexList)
            {
                var _container = complex.Parent;
                InsertBefore(_container, replacement, complex, x => new Text(x));
                complex.Remove();
                var rmList = new List<OpenXmlElement>();
                var cur = _container.NextSibling();
                while (!cur.FindChildDeep<FieldChar>(x => x.FieldCharType == "end").Any())
                {
                    rmList.Add(cur);
                    cur = cur.NextSibling();
                }
                foreach (var rm in rmList)
                {
                    rm.Remove();
                }
                _container.NextSibling().Remove();
                _container.PreviousSibling().Remove();
            }
        }

        public static void Injection(this OpenXmlElement container, string variable, OpenXmlImageInfo image)
        {
            MainDocumentPart mainPart = CurrentOrBackParentUntil<DocumentFormat.OpenXml.Wordprocessing.Document>(container, x => x.MainDocumentPart != null).MainDocumentPart;
            ImagePart imagePart = mainPart.AddImagePart(image.Type);
            imagePart.FeedData(image.Data);
            var id = mainPart.GetIdOfPart(imagePart);
            var docPr = container.FindChildDeep<DocumentFormat.OpenXml.Drawing.Wordprocessing.DocProperties>(x => x.Description == variable);
            foreach (var docPrItem in docPr)
            {
                var drawing = docPrItem.CurrentOrBackParentUntil<Drawing>(x => true).First();
                var ablip = drawing.FindChildDeep<DocumentFormat.OpenXml.Drawing.Blip>(x => true).First();
                ablip.Embed = id;
            }
        }

        public static void Injection(this OpenXmlElement container, string source, System.Data.DataTable data)
        {
            var tableList = container.FindChildDeep<Table>(x => true);
            foreach (var table in tableList)
            {
                var template = table.FindChildDeep<TableRow>(x => VariableList(x, source).Any()).FirstOrDefault();
                TableRow lastRow = template;
                if (template != null)
                {
                    foreach (System.Data.DataRow row in data.Rows)
                    {
                        var newRow = (TableRow)template.CloneNode(true);
                        table.InsertAfter(newRow, lastRow);
                        lastRow = newRow;
                        var varList = VariableList(lastRow, source);
                        foreach (var v in varList)
                        {
                            var reg = new Regex("^" + Regex.Escape(source + ".") + @"([\W\w]+)");
                            var imgdata = row[reg.Match(v).Groups[1].Value] as OpenXmlImageInfo;
                            if (imgdata != null)
                            {
                                Injection(lastRow, v, imgdata);
                            }
                            else
                            {
                                Injection(lastRow, v, row[reg.Match(v).Groups[1].Value].ToString());
                            }
                        }
                    }
                    template.Remove();
                }
            }
        }

        public static void Injection<T>(this OpenXmlElement container, string source, IEnumerable<T> data)
        {
            System.Data.DataTable table = new System.Data.DataTable();
            PropertyInfo[] props = typeof(T).GetProperties();
            foreach (PropertyInfo prop in props)
            {
                Type type = Nullable.GetUnderlyingType(prop.PropertyType);
                type = type == null ? prop.PropertyType : type;
                table.Columns.Add(prop.Name, type);
            }
            foreach (T item in data)
            {
                System.Data.DataRow dr = table.NewRow();
                foreach (PropertyInfo prop in props)
                {
                    object value = prop.GetValue(item);
                    if (value != null)
                    {
                        dr[prop.Name] = value;
                    }
                }
                table.Rows.Add(dr);
            }
            container.Injection(source, table);
        }

        public static void RenderTable(this OpenXmlElement container, string variable, System.Data.DataTable data)
        {
            container.FindChildDeep<Table>(t => true
                && t.FindChildDeep<TableGrid>(tg => tg.FindChildDeep<GridColumn>(x => true).Count() == 1).Count() == 1
                && t.FindChildDeep<TableRow>(tr => true).Count() == 1
                && t.FindChildDeep<TableRow>(tr => tr.VariableList(null).Any(v => v == variable)).Count() == 1
            ).Concat((container is Table) ? Enumerable.Repeat(container as Table, 1) : Enumerable.Empty<Table>()).ToList().ForEach(tableTemplate =>
            {
                int width = 0;
                if (int.TryParse(tableTemplate.FindChildDeep<GridColumn>(gc => true).FirstOrDefault()?.Width, out width))
                {
                    width /= data.Columns.Count;
                    var table = tableTemplate.CloneNode(true) as Table;
                    table.RemoveAllChildren();
                    table.Append(tableTemplate.FindChildDeep<TableProperties>(tp => true).First().CloneNode(true));
                    table.Append(new TableGrid(Enumerable.Repeat(0, data.Columns.Count).Select(x => new GridColumn { Width = width.ToString() })));

                    if (true)
                    {
                        var tr = tableTemplate.FindChildDeep<TableRow>(_tr => true).First().CloneNode(true) as TableRow;
                        tr.RemoveAllChildren();
                        var tcTemplate = tableTemplate.FindChildDeep<TableRow>(_tr => true).First().FindChildDeep<TableCell>(tc => true).First().CloneNode(true) as TableCell;
                        tcTemplate.FindChildDeep<TableCellProperties>(tcp => true).First().TableCellWidth = new TableCellWidth { Width = width.ToString() };
                        foreach (System.Data.DataColumn dc in data.Columns)
                        {
                            var tc = tcTemplate.CloneNode(true) as TableCell;
                            tc.Injection(variable, dc.ColumnName);
                            tc.FindChildDeep<Paragraph>(p => true).ToList().ForEach(p =>
                            {
                                p.FindChildDeep<Run>(r => true).ToList().ForEach(r =>
                                {
                                    r.PrependChild(new RunProperties(new Bold()));
                                });
                            });
                            tr.Append(tc);
                        }
                        table.Append(tr);
                    }

                    foreach (System.Data.DataRow dr in data.Rows)
                    {
                        var tr = tableTemplate.FindChildDeep<TableRow>(_tr => true).First().CloneNode(true) as TableRow;
                        tr.RemoveAllChildren();
                        var tcTemplate = tableTemplate.FindChildDeep<TableRow>(_tr => true).First().FindChildDeep<TableCell>(tc => true).First().CloneNode(true) as TableCell;
                        tcTemplate.FindChildDeep<TableCellProperties>(tcp => true).First().TableCellWidth = new TableCellWidth { Width = width.ToString() };
                        foreach (System.Data.DataColumn dc in data.Columns)
                        {
                            var tc = tcTemplate.CloneNode(true) as TableCell;
                            tc.Injection(variable, dr[dc].ToString());
                            tr.Append(tc);
                        }
                        table.Append(tr);
                    }

                    tableTemplate.InsertAfterSelf(table);
                    tableTemplate.Remove();
                }
            });
        }

        public static void RenderTable(this OpenXmlElement container, string variable, System.Data.DataTable[] dataList)
        {
            container.FindChildDeep<Table>(t => true
                && t.FindChildDeep<TableGrid>(tg => tg.FindChildDeep<GridColumn>(x => true).Count() == 1).Count() == 1
                && t.FindChildDeep<TableRow>(tr => true).Count() == 1
                && t.FindChildDeep<TableRow>(tr => tr.VariableList(null).Any(v => v == variable)).Count() == 1
            ).ToList().ForEach(tableTemplate =>
            {
                for (var i = 0; i < dataList.Length; i++)
                {
                    var newTb = tableTemplate.CloneNode(true) as Table;
                    if (i > 0)
                    {
                        tableTemplate.InsertAfterSelf(new Paragraph { TextId = "77777777", ParagraphId = "77777777", RsidParagraphAddition = "77777777", RsidRunAdditionDefault = "77777777" });
                        tableTemplate.InsertAfterSelf(newTb);
                    }
                    else
                    {
                        tableTemplate.InsertAfterSelf(newTb);
                    }
                    if (dataList[i].TableName != null)
                    {
                        var title = new Paragraph { TextId = "77777777", ParagraphId = "77777777", RsidParagraphAddition = "77777777", RsidRunAdditionDefault = "77777777" };
                        title.Append(new Run(new Text(dataList[i].TableName)));
                        tableTemplate.InsertAfterSelf(title);
                    }
                    newTb.RenderTable(variable, dataList[i]);
                }
                tableTemplate.Remove();
            });
        }
    }
}
