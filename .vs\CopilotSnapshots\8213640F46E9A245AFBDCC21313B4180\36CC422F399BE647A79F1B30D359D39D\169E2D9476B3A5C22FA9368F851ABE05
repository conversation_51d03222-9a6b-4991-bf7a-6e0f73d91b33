﻿using System;
using System.Linq;
using Webaby;

namespace Webaby.Core.Role.Queries
{
    public class GetRoleByIdQuery : QueryBase<RoleData>
    {
        public GetRoleByIdQuery(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; private set; }
    }

    internal class GetRoleByIdQueryHandler : QueryHandlerBase<GetRoleByIdQuery, RoleData>
    {
        public override QueryResult<RoleData> Execute(GetRoleByIdQuery query)
        {
            return QueryResult.Create(EntitySet.Get<RoleEntity>().Where(x => x.Id == query.Id), query.Pagination, RoleData.FromEntity);
        }
    }
}
