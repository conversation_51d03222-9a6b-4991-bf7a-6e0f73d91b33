using System;
using AutoMapper;

namespace TinyCRM.PlanJob.Queries
{
    public class ServiceTypeComplianceData
    {
        public Guid Id { get; set; }

        public Guid ServiceTypeId { get; set; }

        public int TimePeriodIndex { get; set; }

        public RegularType RegularType { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; }

        public Boolean Deleted { get; set; }

        public Guid? DeletedBy { get; set; }

        public DateTime? DeletedDate { get; set; }

        
    }
}

