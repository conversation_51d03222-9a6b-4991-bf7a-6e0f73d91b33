﻿
/****** Object:  StoredProcedure [telesale].[GetFieldSaleTeam]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

--ALTER PROCEDURE [telesale].[GetFieldSaleTeam]
CREATE PROCEDURE [telesale].[GetFieldSaleTeam]
	@ProvinceId UNIQUEIDENTIFIER =NULL,
	@HotDataSource UNIQUEIDENTIFIER=NULL
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
    
	SELECT	SUM(IIF(HotDmo.IsBelongToSourceHot=1,1,0)) OVER (PARTITION BY TeamDmo.TeamId) HotFieldSaleInTeam,
			TeamId, TeamName, FieldSaleLeaderId, FieldSaleLeaderName, FieldSaleLeaderPhone, FieldSaleId, FieldSaleName, FieldSalePhone, FieldSaleCode, IsBelongToSourceHot
	FROM
    (
		SELECT	DISTINCT up.Id FieldSaleLeaderId, up.FullName FieldSaleLeaderName, up.Phone FieldSaleLeaderPhone, o.OrganizationName TeamName, o.Id TeamId  -- need distinct in case @ProvinceId is null
		FROM	dbo.UserProfiles up
				JOIN dbo.aspnet_Membership m ON m.UserId = up.Id
				JOIN dbo.UserInRoles ur ON ur.UserId=up.Id
				JOIN dbo.Organization o ON o.Id=up.OrganizationId
				LEFT JOIN dbo.OrganizationWorkingArea ow ON ow.OrganizationId=o.Id
		WHERE	m.IsApproved = 1
				AND ur.RoleId='94E5A159-A404-4D5C-9D6D-BE0C49ADC34C'  -- Field Sale Leader
				AND o.OrganizationType='DMO Team'
				AND (@ProvinceId IS NULL OR ow.ProvinceId=@ProvinceId)
	) TeamDmo  -- team dmo working on selected province, with sup DMO info
	LEFT JOIN 
	(
		SELECT	DISTINCT up.OrganizationId TeamId1, IIF(@HotDataSource IS NOT NULL AND hu.Id IS NOT NULL, CAST(1 AS BIT), CAST(0 AS BIT)) IsBelongToSourceHot, up.Id FieldSaleId, up.FullName FieldSaleName, up.AgentCode FieldSaleCode, up.Phone FieldSalePhone
		FROM	dbo.UserProfiles up
				JOIN dbo.aspnet_Membership m ON m.UserId = up.Id 
				JOIN dbo.UserInRoles ur ON ur.UserId=up.Id AND ur.RoleId='A4095631-C225-4644-B39A-F5152F4A525C' -- FieldSale
				LEFT JOIN telesale.HotListGroupUser hu ON hu.UserId=up.Id AND (@HotDataSource IS NULL OR hu.HotListGroupId = @HotDataSource )
		WHERE	m.IsApproved = 1
	) HotDmo ON TeamDmo.TeamId=HotDmo.TeamId1
******/
END
GO