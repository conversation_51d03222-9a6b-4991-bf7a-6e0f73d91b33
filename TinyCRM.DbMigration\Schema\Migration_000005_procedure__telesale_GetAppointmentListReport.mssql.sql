﻿
/****** Object:  StoredProcedure [telesale].[GetAppointmentListReport]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO




CREATE PROCEDURE [telesale].[GetAppointmentListReport]
	@UserQueryId UNIQUEIDENTIFIER,
	@AppointmentId UNIQUEIDENTIFIER,

	@TimeAppFrom DATETIME,
	@TimeAppTo DATETIME,

	@TimeCreatedAppFrom DATETIME,
	@TimeCreatedAppTo DATETIME,

	@TimeAssignFieldSaleTeamFrom DATETIME,
	@TimeAssignFieldSaleTeamTo DATETIME,

	@TimeAssignFieldSaleFrom DATETIME,
	@TimeAssignFieldSaleTo DATETIME,

	@AssignTeamFieldSaleId UNIQUEIDENTIFIER,
	@AssignFieldSaleIdList IdList READONLY,

	@CreatedByTeamId UNIQUEIDENTIFIER,
	@CreatedByAgentId UNIQUEIDENTIFIER,

	@LeadCode NVARCHAR(MAX),
	@FullName NVARCHAR(500),
	@AppResultCodeIdList IdList READONLY,

	@PageIndex INT,
	@PageSize INT,
	@IsExport BIT
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	DECLARE @AvailableOrg AS TABLE
	(
		Id UNIQUEIDENTIFIER PRIMARY KEY
	);
    
	INSERT INTO @AvailableOrg EXEC dbo.GetAvailableOrganization @userId = @UserQueryId, @GetIdOnly=1, @IncludeCurrent=1;
	SELECT * INTO #AvailableOrg FROM @AvailableOrg;
	SELECT * INTO #AssignFieldSaleIdList FROM @AssignFieldSaleIdList
	SELECT* INTO #AppResultCodeIdList FROM @AppResultCodeIdList

	DECLARE @IsJoinLa BIT = 0;
	IF 
		@TimeAssignFieldSaleTeamFrom IS NOT NULL OR
		@TimeAssignFieldSaleTeamTo IS NOT NULL OR
		@TimeAssignFieldSaleFrom IS NOT NULL OR
		@TimeAssignFieldSaleTo IS NOT NULL OR
		@AssignTeamFieldSaleId IS NOT NULL OR
		@LeadCode IS NOT NULL OR
		(SELECT COUNT(*) FROM #AssignFieldSaleIdList)>0
	SET @IsJoinLa=1;

	DECLARE @cteQuery NVARCHAR(MAX)=
	N'
		SELECT
		ap.Id AppointmentId
		'+IIF(@IsExport=1, '', ' , ROW_NUMBER() OVER (ORDER BY (SELECT 1)) rn ')+'
		FROM dbo.Appointment ap
		'+IIF(@CreatedByTeamId IS NOT NULL OR @CreatedByAgentId IS NOT NULL, ' JOIN dbo.ProspectAssignment pa ON ap.CreatedByProspectAssignmentId=pa.Id ', '')+'
		'+IIF((SELECT COUNT(*) FROM #AppResultCodeIdList)=0, '', ' JOIN #AppResultCodeIdList parc on parc.Id=dbo.GuidEmpty() AND ap.AppointmentResultCodeId IS NULL OR parc.Id = ap.AppointmentResultCodeId ')+'
		'+IIF(@IsJoinLa=0, '', ' JOIN dbo.LeadAssignment la ON ap.LeadAssignmentId=la.Id ')+'
		'+IIF(@LeadCode IS NULL, '', ' JOIN dbo.Lead l ON la.LeadId=l.Id ')+'
		'+IIF(ISNULL(@FullName,'')='', '', ' JOIN dbo.Contact c ON c.Id = ap.ContactId ')+'
		'+IIF((SELECT COUNT(*) FROM #AssignFieldSaleIdList)=0, '', ' JOIN #AssignFieldSaleIdList pfs on pfs.Id=dbo.GuidEmpty() AND la.AssignedFieldSaleId IS NULL OR pfs.Id = la.AssignedFieldSaleId ')+'
		WHERE 1=1
		'+IIF(@AppointmentId IS NULL, '', ' AND ap.Id = '''+CAST(@AppointmentId AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@TimeAppFrom IS NULL, '', ' AND ap.MeetDate >= '''+CAST(@TimeAppFrom AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@TimeAppTo IS NULL, '', ' AND ap.MeetDate <= '''+CAST(@TimeAppTo AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@TimeCreatedAppFrom IS NULL, '', ' AND ap.CreatedDate >= '''+CAST(@TimeCreatedAppFrom AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@TimeCreatedAppTo IS NULL, '', ' AND ap.CreatedDate <= '''+CAST(@TimeCreatedAppTo AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@TimeAssignFieldSaleTeamFrom IS NULL, '', ' AND la.AssignedFieldSaleTeamDate >= '''+CAST(@TimeAssignFieldSaleTeamFrom AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@TimeAssignFieldSaleTeamTo IS NULL, '', ' AND la.AssignedFieldSaleTeamDate <= '''+CAST(@TimeAssignFieldSaleTeamTo AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@TimeAssignFieldSaleFrom IS NULL, '', ' AND la.AssignedFieldSaleDate >= '''+CAST(@TimeAssignFieldSaleFrom AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@TimeAssignFieldSaleTo IS NULL, '', ' AND la.AssignedFieldSaleDate <= '''+CAST(@TimeAssignFieldSaleTo AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@AssignTeamFieldSaleId IS NULL, '', ' AND la.AssignedFieldSaleTeamId = '''+CAST(@AssignTeamFieldSaleId AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@LeadCode IS NULL, '', ' AND l.LeadCode = '''+CAST(@LeadCode AS NVARCHAR(MAX))+''' ')+'
		'+IIF(@CreatedByAgentId IS NOT NULL, ' AND pa.AssignedAgentId='''+CAST(@CreatedByAgentId AS NVARCHAR(MAX))+''' ', '')+'
		'+IIF(@CreatedByTeamId IS NOT NULL, ' AND pa.AssignedTeamId='''+CAST(@CreatedByTeamId AS NVARCHAR(MAX))+''' ', '')+'
		'+IIF(ISNULL(@FullName,'')='', '', ' AND CONTAINS(c.FullName, @FullName) ')+'
	';
	DECLARE @fullQuery NVARCHAR(MAX)=
	N'
		WITH cte AS
		(
			'+@cteQuery+'
		)
		SELECT
			(SELECT COUNT(*) FROM cte) Total,
			cte.AppointmentId,
			prd.Name,
			FORMAT(ap.MeetDate,''dd.MM.yyyy'') APPTDATE,
			FORMAT(ap.MeetDate,''hh:mm tt'') APPTTIME,
			c.Name CLIENTNAME,
			ap.MeetAddress + '' ('' + IIF(w.WardName IS NOT NULL, w.WardName + '' - '', '''') + IIF(dt.DistrictName IS NOT NULL, dt.DistrictName + '' - '', '''') + IIF(pv.ProvinceName IS NOT NULL, pv.ProvinceName , '''') + '')''  MeetAddress,
			w.WardName,
			dt.DistrictName,
			pv.ProvinceName,
			tmr.FullName TMRNAME,
			tmr.AgentCode TMRCode,
			ap.Notes,
			ap.LeadBudget,
			fs.FullName AssignedFieldSaleName,
			arc.ResultCode,
			ap.FeedbackNotes,
			ap.IncludedDocuments,
			tmrTeam.Name TmrLeaderName,
			ap.CreatedDate CallDate,
			c.DataSource SOURCE,
			c.Phone1 Phone,
			c.Phone2,
			c.Phone3,
			c.Address,
			c.AddressPermanent,
			FORMAT(c.DOB,''dd.MM.yyyy'') DOB,
			c.CMND,
			l.LeadCode LeadID
			'+IIF(@IsExport=0,'', ' ,UPPER(df.Name) DynamicFormName, dfid.DataType, dfv.DynamicFormId, dfid.Id DynamicFieldId, dfid.DisplayName DynamicField, dfiv.Value DynamicFieldValue, dfid.[Order] DynamicFieldOrder ')+'
		FROM cte
		JOIN dbo.Appointment ap ON cte.AppointmentId=ap.Id
		JOIN dbo.LeadAssignment la ON ap.LeadAssignmentId=la.Id 
		JOIN dbo.Lead l ON la.LeadId=l.Id 
		JOIN dbo.Customer c ON ap.ContactId=c.Id
		JOIN dbo.ProspectAssignment pa ON ap.CreatedByProspectAssignmentId=pa.Id
		JOIN dbo.UserProfiles tmr ON pa.AssignedAgentId=tmr.Id
		LEFT JOIN dbo.Product prd on ap.ProductId=prd.Id
		LEFT JOIN dbo.Organization tmrTeam ON tmr.OrganizationId=tmrTeam.Id
		LEFT JOIN dbo.UserProfiles fs ON  la.AssignedFieldSaleId=fs.Id
		LEFT JOIN dbo.AppointmentResultCode arc ON ap.AppointmentResultCodeId=arc.Id
		LEFT JOIN dbo.Province pv ON ap.ProvinceId=pv.Id
		LEFT JOIN dbo.District dt ON ap.DistrictId=dt.Id
		LEFT JOIN dbo.Ward w ON ap.WardId=w.Id
		'+IIF(@IsExport=0, '',
		'
			LEFT JOIN dbo.DynamicFormValue dfv ON dfv.Id = ap.AdditionalDynamicFormValueId
			LEFT JOIN dbo.DynamicForm df ON df.Id = dfv.DynamicFormId
			LEFT JOIN dbo.DynamicFieldValue dfiv ON dfiv.DynamicFormValueId = dfv.Id
			LEFT JOIN dbo.DynamicFieldDefinition dfid ON dfid.Id = dfiv.DynamicFieldId
		')+'		
		'+IIF(@IsExport=1, '', ' WHERE cte.rn BETWEEN '+CAST(@PageSize*@PageIndex+1 AS NVARCHAR(MAX))+' AND '+CAST(@PageSize*@PageIndex+@PageSize AS NVARCHAR(MAX))+' ')+'
	';

	EXEC sp_executesql @fullQuery, N'@FullName NVARCHAR(500)',
									@FullName = @FullName
******/
END
GO