﻿
/****** Object:  StoredProcedure [telesale].[AssignProspectBackToLeadersDueTo15Days]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [telesale].[AssignProspectBackToLeadersDueTo15Days]
	@ExecutedDate	DATETIME
AS
BEGIN
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******
	BEGIN TRANSACTION;
	BEGIN TRY

		SELECT	pa.Id ProspectAssignmentId, NEWID() NewProspectAssignmentId, p.Id ProspectId, pa.AssignedTeamId, pa.AssignedTeamDate, pa.CreatedBy,
				CASE WHEN pa.Status = 1 THEN 6 ELSE 2 END ClosedReason,
				CASE WHEN pa.Status = 1 THEN 4 ELSE 2 END CreatedReason,
				c.Id ContactId, c.DataSource
		INTO	#TempTable
		FROM	dbo.ProspectAssignment pa
				JOIN dbo.Prospect p ON p.CurrentAssignmentId = pa.Id
				JOIN dbo.Contact c ON c.Id = p.ContactId
		WHERE	p.HotListGroupId IS NULL AND p.IsHot=0
				AND DATEDIFF(DAY, p.BackToCommonBasketDate, @ExecutedDate) > 0

		-- Close current ProspectAssignment
		UPDATE	pa
		SET		Status = 3,
				ClosedReason = temp.ClosedReason,
				UnassignedDate=GETDATE()
		FROM	dbo.ProspectAssignment pa
				JOIN #TempTable temp ON temp.ProspectAssignmentId = pa.Id

		-- Close current lead
		UPDATE	l
		SET		l.Status=5 
		FROM	#TempTable t 
				JOIN dbo.Lead l ON t.ProspectAssignmentId=l.CreatedByProspectAssignmentId

		-- Create new Prospect Assignment
		INSERT	dbo.ProspectAssignment
			( 
				Id,
				ProspectId,
				Status,
				CreatedReason,
				AssignedTeamId,
				AssignedTeamDate,
				PreviousProspectAssingmentId,
				CreatedDate,
				CreatedBy
			)
		SELECT	NewProspectAssignmentId, ProspectId, 1, CreatedReason, AssignedTeamId, AssignedTeamDate, ProspectAssignmentId, GETDATE(), CreatedBy
		FROM	#TempTable

		-- If DataSource is "TMR Created", change to "TMR Repo"
		UPDATE	c
		SET		DataSource = 'TMR Repo'
		FROM	#TempTable temp
				JOIN dbo.Contact c ON c.Id = temp.ContactId
		WHERE	c.DataSource = 'TMR Created'

		-- Update CurrentProspectAssignment
		UPDATE	p
		SET		CurrentAssignmentId = temp.NewProspectAssignmentId, BackToCommonBasketDate=NULL
		FROM	dbo.Prospect p
				JOIN #TempTable temp ON temp.ProspectId = p.Id

		COMMIT TRANSACTION;

	END TRY  
	BEGIN CATCH
		IF @@TRANCOUNT > 0
		rollback transaction;
		THROW;
	END CATCH
******/
END
GO