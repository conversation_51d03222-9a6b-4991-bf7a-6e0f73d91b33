﻿
/****** Object:  StoredProcedure [telesale].[TMRSpeedAndRessultNewCalling]    Script Date: 5/23/2025 5:24:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [telesale].[TMRSpeedAndRessultNewCalling] 
	@DataSources stringList READONLY,
	@FromDate DATETIME = NULL,
	@ToDate DATETIME = NULL
AS
BEGIN
	SET NOCOUNT ON;
	RAISERROR('PROCEDURE được comment để tránh xảy ra lỗi khi migration upgrade lên EF core', 16, 1);
/******	
	DECLARE @Sources stringList;
	IF (SELECT COUNT(*) FROM @DataSources)=0 INSERT INTO @Sources SELECT DISTINCT DataSource Value FROM dbo.Contact;
	ELSE INSERT INTO @Sources SELECT Value FROM @DataSources;

	WITH cte AS
	(
		SELECT
		c.DataSource,
		pa.AssignedAgentId,
		SUM(IIF(gset.G_ProspectIdLastCallDate=1, 1, 0)) TotalLead,
		SUM(IIF(gset.G_ProspectIdLastCallDate=1 AND cc.Id IS NOT NULL, 1, 0)) LeadCalled,
		SUM(IIF(gset.G_ProspectIdLastConnected=1 AND cr.IsConnected=1, 1, 0)) LeadConnected,
		COUNT(DISTINCT gset.ContactCallId) CalledAttempts,
		SUM(IIF(gset.G_AppointmentDefault=1 AND gset.AppointmentId IS NOT NULL AND ap.Status<>4, 1, 0)) NewApp,
		SUM(IIF(gset.G_AppointmentDefault=1 AND gset.AppointmentId IS NOT NULL AND ap.Status<>4 AND DATEPART(HOUR, ap.CreatedDate) > 15, 1, 0)) NewAppOverTime,
		SUM(IIF(gset.G_AppointmentDefault=1 AND ap.Status=3, 1, 0)) NewMeet,
		SUM(IIF(gset.G_LeadDefault=1 AND l.Status=4, 1, 0)) CaseSub,
		SUM(IIF(gset.G_LeadAssignmentDefault=1, gset.APE, 0)) APESub
		FROM
		(
			SELECT
			ROW_NUMBER() OVER (PARTITION BY pa.AssignedAgentId, p.Id ORDER BY cc.CreatedDate DESC) G_ProspectIdLastCallDate,
			ROW_NUMBER() OVER (PARTITION BY pa.AssignedAgentId, p.Id ORDER BY cr.IsConnected DESC) G_ProspectIdLastConnected,
			ROW_NUMBER() OVER (PARTITION BY pa.AssignedAgentId, l.Id ORDER BY (SELECT 1)) G_LeadDefault, --la
			ROW_NUMBER() OVER (PARTITION BY pa.AssignedAgentId, la.Id ORDER BY (SELECT 1)) G_LeadAssignmentDefault, --sale
			ROW_NUMBER() OVER (PARTITION BY pa.AssignedAgentId, ap.Id ORDER BY (SELECT 1)) G_AppointmentDefault,
			c.Id ContactId,
			p.Id ProspectId,
			pa.Id ProspectAssignmentId,
			cc.Id ContactCallId,
			cr.Id CallResultId,
			l.Id LeadId,
			ap.Id AppointmentId,
			s.APE
			FROM
			@Sources ds
			JOIN dbo.Contact c ON ds.Value=c.DataSource
			LEFT JOIN dbo.Prospect p ON c.Id=p.ContactId
			LEFT JOIN dbo.ProspectAssignment pa ON p.Id=pa.ProspectId
			LEFT JOIN dbo.ContactCall cc ON pa.Id=cc.ProspectAssignmentId
			LEFT JOIN dbo.CallResult cr ON cc.CallResultId=cr.Id
			LEFT JOIN dbo.Lead l ON l.CreatedByContactCallId=cc.Id
			LEFT JOIN dbo.LeadAssignment la ON la.LeadId=l.Id
			LEFT JOIN dbo.Appointment ap ON ap.LeadAssignmentId=la.Id
			LEFT JOIN dbo.Sale s ON s.LeadAssignmentId=la.Id
		) gset
		JOIN dbo.Contact c ON c.Id = gset.ContactId
		LEFT JOIN dbo.Prospect p ON p.Id = gset.ProspectId
		LEFT JOIN dbo.ProspectAssignment pa ON pa.Id = gset.ProspectAssignmentId
		LEFT JOIN dbo.CallResult cr ON cr.Id = gset.CallResultId
		LEFT JOIN dbo.ContactCall cc ON cc.Id = gset.ContactCallId
		LEFT JOIN dbo.Lead l ON l.Id = gset.LeadId
		LEFT JOIN dbo.Appointment ap ON ap.Id=gset.AppointmentId
		WHERE (@FromDate IS NULL OR pa.AssignedAgentDate >= @FromDate)
		AND (@ToDate IS NULL OR pa.AssignedAgentDate <= @ToDate)
		GROUP BY c.DataSource, pa.AssignedAgentId
	)
	SELECT cte.*,
	IIF(up.AgentCode IS NOT NULL, up.AgentCode, '') + '-' + IIF(up.FullName IS NOT NULL, up.FullName, '') TMRName,
	org2.OrganizationName TMRManager,
	org.OrganizationName TMRLead
	FROM cte
	JOIN dbo.UserProfiles up ON cte.AssignedAgentId=up.Id
	LEFT JOIN dbo.Organization org ON org.Id=up.OrganizationId
	LEFT JOIN dbo.Organization org2 ON org2.Id=org.ParentId
	--ORDER BY cte.DataSource, cte.TotalLead DESC
******/
END
GO